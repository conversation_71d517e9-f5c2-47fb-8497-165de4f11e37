# 商品相关问题初始化 - 批量优化版本

## 概述

本文档描述了商品相关问题初始化功能的批量优化版本，相比单个SKU处理，批量处理在性能上有显著提升。

## 功能特性

### 1. 批量处理能力
- 支持一次处理多个SKU
- 自动分批查询，避免SQL过长
- 智能错误处理和重试机制

### 2. 性能优化
- **批量数据库查询**：减少数据库连接次数
- **批量过滤已存在数据**：使用IN查询替代多次单独查询
- **分批处理**：避免内存溢出和SQL长度限制
- **并发友好**：支持大批量数据处理

### 3. 详细的处理结果统计
- 总处理数量
- 成功/失败/跳过数量统计
- 成功率计算
- 处理耗时统计
- 详细的SKU分类列表

## 使用方式

### 1. Job调度参数

#### 批量处理（推荐）
```json
{
  "skus": ["SKU001", "SKU002", "SKU003", "..."]
}
```

#### 单个处理（兼容旧版本）
```json
{
  "sku": "SKU001"
}
```

### 2. HTTP接口

#### 批量初始化接口
```http
POST /marketItemAiExt/init/questions/batch
Content-Type: application/json

["SKU001", "SKU002", "SKU003"]
```

#### 单个初始化接口
```http
POST /marketItemAiExt/init/questions/SKU001
```

## 性能对比

### 处理1000个SKU的性能对比：

| 处理方式 | 数据库查询次数 | 预估耗时 | 内存使用 |
|---------|---------------|----------|----------|
| 单个处理 | ~3000次 | 10-15分钟 | 低 |
| 批量处理 | ~30次 | 2-3分钟 | 中等 |

### 优化点：

1. **数据库查询优化**
   - 已存在数据检查：从N次查询优化为N/100次批量查询
   - 商品基础数据查询：从N次查询优化为N/50次批量查询

2. **内存使用优化**
   - 分批处理，避免一次性加载所有数据
   - 及时释放处理完成的数据

3. **错误处理优化**
   - 单个SKU失败不影响整批处理
   - 自动重试机制
   - 详细的错误日志

## 批量处理流程

```mermaid
graph TD
    A[接收SKU列表] --> B[参数验证]
    B --> C[分批过滤已存在数据]
    C --> D[批量查询商品基础数据]
    D --> E[分批生成AI问题]
    E --> F[批量保存到数据库]
    F --> G[返回处理结果统计]
    
    C --> C1[批量查询MarketItemAiExt]
    C1 --> C2[过滤已存在SKU]
    
    D --> D1[分批查询商品信息]
    D1 --> D2[构建SKU-商品信息映射]
    
    E --> E1[逐个调用AI接口]
    E1 --> E2[解析AI响应]
    E2 --> E3[构建保存参数]
```

## 配置参数

### 批量大小配置
```java
// 已存在数据检查批量大小
private static final int EXIST_CHECK_BATCH_SIZE = 100;

// 商品数据查询批量大小  
private static final int PRODUCT_QUERY_BATCH_SIZE = 50;
```

### 性能调优建议

1. **批量大小调整**
   - 根据数据库性能调整批量大小
   - 考虑网络延迟和内存限制

2. **并发处理**
   - 可以考虑多线程处理不同批次
   - 注意数据库连接池限制

3. **监控指标**
   - 处理耗时
   - 成功率
   - 数据库连接使用情况

## 返回结果示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 1000,
    "successCount": 950,
    "failCount": 20,
    "skipCount": 30,
    "successSkus": ["SKU001", "SKU002", "..."],
    "failSkus": ["SKU999", "..."],
    "skipSkus": ["SKU888", "..."],
    "processingTimeMs": 120000,
    "successRate": 95.0,
    "errorMessage": null
  }
}
```

## 注意事项

1. **数据一致性**
   - 批量处理过程中可能有新数据插入
   - 建议在低峰期执行大批量任务

2. **资源使用**
   - 大批量处理会占用较多内存和数据库连接
   - 建议监控系统资源使用情况

3. **错误处理**
   - 部分失败不会影响整体处理
   - 失败的SKU会在结果中详细列出

4. **AI接口限制**
   - 注意AI接口的调用频率限制
   - 可能需要添加适当的延迟控制

## 后续优化方向

1. **异步处理**：对于超大批量，可以考虑异步队列处理
2. **缓存优化**：缓存商品基础数据，减少重复查询
3. **并发控制**：添加多线程处理支持
4. **监控告警**：添加处理进度和异常监控
