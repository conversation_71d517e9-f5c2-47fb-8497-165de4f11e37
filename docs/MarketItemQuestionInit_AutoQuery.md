# 商品相关问题初始化 - 自动查询与刷新功能

## 功能概述

新增了自动查询需要处理的SKU功能和强制刷新功能，支持更灵活的商品问题初始化场景。

## 新增功能

### 1. 自动查询SKU功能

当Job参数为空或未指定SKU列表时，系统会自动查询需要生成AI问题的SKU。

**查询条件：**
```sql
select sku.sku
from inventory sku
inner join products spu on spu.pd_id = sku.pd_id
    and spu.`pd_name` not like '%测试%'
    and spu.`pd_name` not like '%压测%'
left join market_item_ai_ext miae on sku.sku = miae.sku and miae.ext_type = 1
where sku.`tenant_id` = 1
and sku.`outdated` = 0
and sku.sku in (
    select distinct sku from area_sku where on_sale = true
)
and miae.sku is null
```

**查询逻辑：**
- 只查询在售的商品（area_sku.on_sale = true）
- 排除测试和压测商品
- 排除已有AI问题的商品（miae.sku is null）
- 只查询有效的商品（outdated = 0）

### 2. 强制刷新功能

支持强制重新生成商品相关问题，会先删除已存在的问题数据，然后重新生成。

## 使用方式

### 1. Job调度

#### 自动查询模式（推荐）
```json
{}
```
或者不传参数，系统会自动查询需要处理的SKU。

#### 指定SKU模式
```json
{
  "skus": ["SKU001", "SKU002", "SKU003"]
}
```

### 2. HTTP接口

#### 批量初始化（自动查询）
```http
POST /marketItemAiExt/init/questions
Content-Type: application/json

# 空body或不传body，自动查询需要处理的SKU
```

#### 批量初始化（指定SKU）
```http
POST /marketItemAiExt/init/questions
Content-Type: application/json

["SKU001", "SKU002", "SKU003"]
```

#### 强制刷新
```http
POST /marketItemAiExt/refresh/questions
Content-Type: application/json

["SKU001", "SKU002", "SKU003"]
```

#### 查询需要处理的SKU
```http
GET /marketItemAiExt/query/skus-need-questions
```

## 功能对比

| 功能 | 初始化 | 刷新 |
|------|--------|------|
| 处理已存在数据 | 跳过 | 删除后重新生成 |
| 适用场景 | 首次生成 | 更新问题内容 |
| 性能影响 | 较低 | 较高（需要删除操作） |

## 使用场景

### 1. 定期自动初始化
```json
# Job参数（每日定时执行）
{}
```
系统会自动查询新上架且未生成问题的商品，进行批量初始化。

### 2. 手动指定商品初始化
```json
# Job参数
{
  "skus": ["SKU001", "SKU002"]
}
```
针对特定商品进行问题生成。

### 3. 强制刷新商品问题
```http
POST /marketItemAiExt/refresh/questions
["SKU001", "SKU002"]
```
当商品信息发生变化，需要重新生成问题时使用。

### 4. 查看待处理商品
```http
GET /marketItemAiExt/query/skus-need-questions
```
查看当前有哪些商品需要生成AI问题。

## 处理流程

### 初始化模式
```mermaid
graph TD
    A[接收参数] --> B{SKU列表是否为空?}
    B -->|是| C[自动查询需要处理的SKU]
    B -->|否| D[使用指定的SKU列表]
    C --> E[过滤已存在数据]
    D --> E
    E --> F[批量查询商品信息]
    F --> G[生成AI问题]
    G --> H[保存结果]
```

### 刷新模式
```mermaid
graph TD
    A[接收SKU列表] --> B[删除已存在的问题数据]
    B --> C[批量查询商品信息]
    C --> D[生成AI问题]
    D --> E[保存结果]
```

## 性能优化

1. **自动查询优化**：
   - 使用LEFT JOIN避免子查询
   - 添加必要的索引提升查询性能

2. **批量删除优化**：
   - 分批删除避免长事务
   - 记录删除日志便于追踪

3. **内存控制**：
   - 自动查询结果过多时进行分批处理
   - 及时释放不需要的数据

## 监控建议

1. **自动查询监控**：
   - 监控查询到的SKU数量
   - 设置合理的阈值告警

2. **刷新操作监控**：
   - 记录刷新操作的频率
   - 监控删除和重新生成的耗时

3. **数据一致性监控**：
   - 定期检查是否有遗漏的商品
   - 验证生成的问题质量

## 注意事项

1. **自动查询限制**：
   - 建议设置最大查询数量限制
   - 避免一次性处理过多商品

2. **刷新操作风险**：
   - 刷新会删除现有数据，操作需谨慎
   - 建议在业务低峰期执行

3. **数据库性能**：
   - 大量商品刷新时注意数据库负载
   - 考虑分批执行避免影响业务

## 错误处理

1. **查询失败**：系统会记录错误日志，返回空列表
2. **删除失败**：记录错误但不影响后续处理
3. **部分失败**：详细记录失败的SKU便于后续处理
