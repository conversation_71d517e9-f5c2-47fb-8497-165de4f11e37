# 商品相关问题初始化 - 简化版使用指南

## 功能说明

商品相关问题初始化功能支持两种使用方式：
1. **指定SKU列表**：处理指定的商品
2. **自动查询**：系统自动查询需要处理的商品

## 使用方式

### 1. Job调度

#### 自动查询模式（推荐）
```json
{}
```
系统会自动查询需要生成AI问题的商品进行处理。

#### 指定SKU模式
```json
{
  "skus": ["SKU001", "SKU002", "SKU003"]
}
```
只处理指定的SKU列表。

### 2. HTTP接口

#### 批量初始化
```http
# 自动查询模式
POST /marketItemAiExt/init/questions
Content-Type: application/json
# 空body

# 指定SKU模式  
POST /marketItemAiExt/init/questions
Content-Type: application/json
["SKU001", "SKU002", "SKU003"]
```

#### 强制刷新
```http
POST /marketItemAiExt/refresh/questions
Content-Type: application/json
["SKU001", "SKU002", "SKU003"]
```

#### 查询待处理商品
```http
GET /marketItemAiExt/query/skus-need-questions?limit=100
```

## 自动查询条件

系统自动查询满足以下条件的SKU：
- 商品在售（area_sku.on_sale = true）
- 商品有效（inventory.outdated = 0）
- 排除测试商品（pd_name不包含'测试'、'压测'）
- 未生成AI问题（market_item_ai_ext中不存在记录）
- 租户ID为1

## 处理结果

```json
{
  "totalCount": 100,        // 总处理数量
  "successCount": 95,       // 成功数量
  "failCount": 2,           // 失败数量
  "skipCount": 3,           // 跳过数量（已存在）
  "successSkus": [...],     // 成功的SKU列表
  "failSkus": [...],        // 失败的SKU列表
  "skipSkus": [...],        // 跳过的SKU列表
  "processingTimeMs": 5000, // 处理耗时
  "successRate": 95.0       // 成功率
}
```

## 功能对比

| 功能 | 初始化 | 刷新 |
|------|--------|------|
| 已存在数据 | 跳过 | 更新现有记录 |
| 适用场景 | 首次生成 | 更新问题内容 |

## 注意事项

1. **自动查询**：建议用于定期维护，系统会自动处理所有需要的商品
2. **指定SKU**：适用于特定商品的处理
3. **强制刷新**：会更新现有记录的问题内容，不会删除数据
4. **查询接口**：默认返回100个SKU，最大1000个

## 常见使用场景

### 定期自动维护
```bash
# 每日定时Job
参数: {}
```

### 新商品上架后处理
```bash
# 指定新上架的商品
参数: {"skus": ["NEW001", "NEW002"]}
```

### 商品信息更新后刷新
```http
POST /marketItemAiExt/refresh/questions
["UPDATED001", "UPDATED002"]
```
