package net.summerfarm.manage.facade.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.provider.MessageSendProvider;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.enums.DingTalkMsgTypeEnum;
import net.summerfarm.manage.facade.message.input.FeiShuMsgInput;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.robot.feishu.dto.card.FeishuCard;
import net.xianmu.robot.feishu.dto.card.FeishuCardBasic;
import net.xianmu.robot.feishu.dto.card.FeishuCardElement;
import net.xianmu.robot.feishu.enums.CardTagEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 飞书私信
 *
 * <AUTHOR>
 * @date 2023/7/24  15:51
 */
@Component
@Slf4j
public class FeiShuPersonalMsgFacade {
    @DubboReference
    private MessageSendProvider messageSendProvider;
    /**
     * 鲜沐tenantId
     */
    public static final Long XIANMU_TENANT_ID = 1L;




    public void sendMessageWithFeiShu(FeiShuMsgInput msgBO) {
        log.info("发送消息给个人，dingTalkMsgBO：{}", JSON.toJSONString(msgBO));
        if (CollectionUtils.isEmpty(msgBO.getReceiverIdList())) {
            throw new ParamsException("消息发送失败，未指定消息接收人");
        }

        MessageBodyReq req = new MessageBodyReq();
        req.setContentType(1);
        if (msgBO.getMsgType().equals(DingTalkMsgTypeEnum.TXT.getType())) {
            req.setMsgBodyType(0);
            req.setTitle(msgBO.getTitle());

            JSONObject text = new JSONObject();
            text.put("text", msgBO.getContent());
            req.setData(text.toJSONString());
        } else {
            req.setMsgBodyType(3);
            req.setTitle(msgBO.getTitle());

            String msgStr = msgBO.getText().replaceAll("#","")
                    .replaceAll(">>", ">")
                    .replaceAll(">", "\n")
                    .replaceAll("\n\n", "\n")
                    .replaceAll(" ","");

            FeishuCardElement feishuCardElement = new FeishuCardElement();
            feishuCardElement.setTag(CardTagEnum.div.getValue());
            feishuCardElement.setText(new FeishuCardBasic(CardTagEnum.lark_md.getValue(), msgStr));

            FeishuCard feishuCard = new FeishuCard();
            feishuCard.setElements(Collections.singletonList(feishuCardElement));

            req.setData(JSON.toJSONString(feishuCard));
        }

        this.sendFeiShuPersonalMsg(msgBO.getReceiverIdList(), req);
    }











    /**
     * 发送消息
     *
     * @param originEnum     账号体系来源
     * @param sendAdminId    发送人
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(SystemOriginEnum originEnum, Long sendAdminId, List<Long> receiveAdminId, MessageBodyReq mesg) {
        MessageUserReq sender = null;
        if (sendAdminId != null) {
            sender = new MessageUserReq();
            sender.setBizUserId(sendAdminId);
            sender.setSystemOriginEnum(originEnum);
        }

        List<MessageUserReq> receiverList = new ArrayList<>();
        for (Long adminId : receiveAdminId) {
            MessageUserReq receiver = new MessageUserReq();
            receiver.setBizUserId(adminId);
            receiver.setSystemOriginEnum(originEnum);
            receiverList.add(receiver);
        }

        DubboResponse<List<MsgSendLogResp>> response = null;
        try {
            response = messageSendProvider.batchSendMessage(XIANMU_TENANT_ID, ChannelTypeEnum.FEISHU_SYSTEM, sender, receiverList, mesg);
            if (!response.isSuccess()) {
                log.error("飞书消息发送失败,param:{}", JSONObject.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("飞书消息发送失败,param:{}，e：{}", JSONObject.toJSONString(response),e);
        }

    }

    /**
     * 发送消息
     *
     * @param sendAdminId    发送人
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(Long sendAdminId, List<Long> receiveAdminId, MessageBodyReq mesg) {
        sendFeiShuPersonalMsg(SystemOriginEnum.ADMIN, sendAdminId, receiveAdminId, mesg);
    }

    /**
     * 发送消息
     *
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(List<Long> receiveAdminId, MessageBodyReq mesg) {
        sendFeiShuPersonalMsg(SystemOriginEnum.ADMIN, null, receiveAdminId, mesg);
    }
}
