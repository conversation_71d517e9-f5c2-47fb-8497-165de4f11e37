package net.summerfarm.manage.facade.wiki.dto;

import lombok.Data;

/**
 * Wiki知识库搜索请求
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class WikiSearchRequest {
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 搜索文本
     */
    private String text;
    
    /**
     * 搜索模式，默认为混合召回
     */
    private String searchMode = "mixedRecall";
    
    /**
     * 是否使用重排序，默认false
     */
    private Boolean usingReRank = false;
    
    /**
     * 返回结果的最大文本长度，默认5000
     */
    private Integer limit = 5000;
    
    /**
     * 相似度阈值，默认0
     */
    private Integer similarity = 0;
    
    /**
     * 是否使用扩展查询，默认false
     */
    private Boolean datasetSearchUsingExtensionQuery = false;
    
    /**
     * 扩展模型，默认空
     */
    private String datasetSearchExtensionModel = "";
    
    /**
     * 扩展背景，默认空
     */
    private String datasetSearchExtensionBg = "";
}
