package net.summerfarm.manage.facade.merchant;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import net.summerfarm.mall.client.provider.MerchantCancelProvider;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.mall.client.resp.MerchantCancelResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/5/4 11:18:34
 */
@Service
@Slf4j
public class MerchantCancelFacade {

    @DubboReference
    private MerchantCancelProvider merchantCancelProvider;

    /**
     * @description: 插入门店注销记录信息
     * @author: lzh
     * @date: 2023/5/4 11:23
     * @param: [merchantCancelInputReq]
     * @return: java.lang.Long
     **/
    public MerchantCancelResp insert(MerchantCancelInputReq merchantCancelInputReq) {
        DubboResponse<MerchantCancelResp> insert = merchantCancelProvider.insert(merchantCancelInputReq);
        if (Objects.isNull(insert) || !DubboResponse.COMMON_SUCCESS_CODE.equals(insert.getCode()) || Objects.isNull(insert.getData())){
            log.error("MerchantCancelFacade[]insert[]insert error cause:{}", JSON.toJSONString(insert));
            throw new BizException(insert.getMsg());
        }
        MerchantCancelResp merchantCancelResp = insert.getData();
        return merchantCancelResp;
    }

    /**
     * @description: 更新门店注销状态
     * @author: lzh
     * @date: 2023/5/4 11:25
     * @param: [merchantCancelInputReq]
     * @return: java.lang.Boolean
     **/
    public Boolean updateStatus(MerchantCancelInputReq merchantCancelInputReq) {
        DubboResponse<Boolean> updateStatus = merchantCancelProvider.updateStatus(merchantCancelInputReq);
        if (Objects.isNull(updateStatus) || !DubboResponse.COMMON_SUCCESS_CODE.equals(updateStatus.getCode())){
            log.error("MerchantCancelFacade[]updateStatus[]updateStatus error cause:{}", JSON.toJSONString(updateStatus));
            throw new BizException(updateStatus.getMsg());
        }
        return updateStatus.getData();
    }

    /**
     * @description: 检查是否可以注销
     * @author: lzh
     * @date: 2023/5/4 11:27
     * @param: []
     * @return: java.util.List<java.lang.String>
     **/
    public List<String> check(MerchantCancelInputReq merchantCancelInputReq) {
        DubboResponse<List<String>> check = merchantCancelProvider.check(merchantCancelInputReq);
        if (Objects.isNull(check) || !DubboResponse.COMMON_SUCCESS_CODE.equals(check.getCode())){
            log.error("MerchantCancelFacade[]check[]check error cause:{}", JSON.toJSONString(check));
            throw new BizException(check.getMsg());
        }
        return check.getData();
    }

    /**
     * @description: 立即注销
     * @author: lzh
     * @date: 2023/8/7 11:00
     * @param: [merchantCancelInputReq]
     * @return: java.lang.Boolean
     **/
    public Boolean promptlyCancel(MerchantCancelInputReq merchantCancelInputReq) {
        DubboResponse<Boolean> cancel = merchantCancelProvider.promptlyCancel(merchantCancelInputReq);
        if (Objects.isNull(cancel) || !DubboResponse.COMMON_SUCCESS_CODE.equals(cancel.getCode())){
            log.error("MerchantCancelFacade[]promptlyCancel[]cancel error cause:{}", JSON.toJSONString(cancel));
            throw new BizException(cancel.getMsg());
        }
        return cancel.getData();
    }

    /**
     * @description: 立即注销无注销记录 用于转移
     * @author: sg
     * @date: 2023/8/7 11:00
     * @param: [merchantCancelInputReq]
     * @return: java.lang.Boolean
     **/
    public Boolean transferCancel(MerchantCancelInputReq merchantCancelInputReq) {
        DubboResponse<Boolean> cancel = merchantCancelProvider.transferCancel(merchantCancelInputReq);
        if (Objects.isNull(cancel) || !DubboResponse.COMMON_SUCCESS_CODE.equals(cancel.getCode())){
            log.error("MerchantCancelFacade[]transferCancel[]cancel error cause:{}", JSON.toJSONString(cancel));
            throw new BizException(cancel.getMsg());
        }
        return cancel.getData();
    }
}
