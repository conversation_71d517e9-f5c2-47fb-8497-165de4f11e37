package net.summerfarm.manage.facade.merchant;

import cn.hutool.core.collection.CollUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;

@Component
public class MerchantAccountFacade {
    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;







    /**
     * 查询门店已删除的店长信息
     * @param req
     * @return
     */
    public List<MerchantStoreAccountResultResp> queryDeleteMerchantManager(List<Long> midList) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setTenantId(XM_TENANTID);
        req.setMIdList(midList);
        req.setType(MerchantAccountEnums.Type.MANAGER.getCode());
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.DELETED.getCode());
        DubboResponse<List<MerchantStoreAccountResultResp>> merchantStoreAccounts = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(req);
        if (merchantStoreAccounts.isSuccess()) {
            return merchantStoreAccounts.getData();
        }
        throw new BizException(merchantStoreAccounts.getMsg());
    }



    /**
     * 查询门店子账号列表
     * @param req
     * @return
     */
    public List<MerchantStoreAccountResultResp> queryMerchantAccount(MerchantStoreAccountQueryReq req) {
        req.setTenantId(XM_TENANTID);
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        DubboResponse<List<MerchantStoreAccountResultResp>> merchantStoreAccounts = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(req);
        if (merchantStoreAccounts.isSuccess()) {
            return merchantStoreAccounts.getData();
        }
        throw new BizException(merchantStoreAccounts.getMsg());
    }


    /**
     * 根据account_id查询
     * @param req
     * @return
     */
    public MerchantStoreAccountResultResp selectMerchantAccountByAccountId(Long accountId) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setTenantId(XM_TENANTID);
        req.setXmAccountIdList(Collections.singletonList(accountId));
        DubboResponse<List<MerchantStoreAccountResultResp>> merchantStoreAccounts = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(req);
        if (merchantStoreAccounts.isSuccess()) {
            List<MerchantStoreAccountResultResp> list = merchantStoreAccounts.getData();
            return CollUtil.isNotEmpty(list) ? list.get(0) : null;
        }
        throw new BizException(merchantStoreAccounts.getMsg());
    }


}
