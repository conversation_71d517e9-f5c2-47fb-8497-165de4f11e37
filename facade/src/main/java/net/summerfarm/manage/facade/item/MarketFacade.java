package net.summerfarm.manage.facade.item;

import com.cofso.item.client.enums.DeleteFlagEnum;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.req.MarketQueryReq;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketResp;
import com.cofso.page.PageResp;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.security.ProviderException;

@Component
public class MarketFacade {
    @DubboReference
    private MarketProvider marketProvider;

    private static final Long xmTenantId = 1L;

    public PageResp<MarketResp> queryMarketListPage(MarketQueryReq param) {
        param.setTenantId(xmTenantId);
        param.setDeleteFlag(DeleteFlagEnum.NORMAL.getFlag());
        DubboResponse<PageResp<MarketResp>> response = marketProvider.queryMarketListPage(param);

        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException(response.getMsg());
    }
}
