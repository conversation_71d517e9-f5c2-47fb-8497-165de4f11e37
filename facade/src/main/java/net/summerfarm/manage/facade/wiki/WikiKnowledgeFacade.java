package net.summerfarm.manage.facade.wiki;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.facade.wiki.dto.ScoreItem;
import net.summerfarm.manage.facade.wiki.dto.SearchResultItem;
import net.summerfarm.manage.facade.wiki.dto.WikiSearchRequest;
import net.summerfarm.manage.facade.wiki.dto.WikiSearchResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Wiki知识库访问门面
 * 
 * 提供与Wiki知识库交互的核心服务功能
 * 通过文本搜索文档知识库，获取与烘焙和餐饮原材料相关的专业知识
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Component
@Slf4j
public class WikiKnowledgeFacade {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    /**
     * 通过文本搜索文档知识库
     * 
     * 提供了一个结构化查询接口，用于从知识库中获取与"乳制品""奶油""奶油芝士""植物奶"等
     * 烘焙和餐饮原材料相关的专业知识。
     * 知识内容包括但不限于产品产地、成分类型、储存方式、保质期、使用建议、口感描述、适用场景和常见问题。
     * 
     * @param text 搜索文本，例如："日清山茶花适合什么甜品使用？"，"植物奶和动物奶油的区别是什么？"，"安佳淡奶油使用方法"
     * @return 搜索结果数据列表，如果请求失败则返回空列表
     */
    public List<SearchResultItem> searchWiki(String text) {
        return searchWiki(text, null, null);
    }

    /**
     * 通过文本搜索文档知识库
     * 
     * @param text 搜索文本
     * @param datasetId 要搜索的数据集ID，如果为null则使用默认数据集
     * @param limit 返回结果的最大文本长度，默认为5000字符
     * @return 搜索结果数据列表，如果请求失败则返回空列表
     */
    public List<SearchResultItem> searchWiki(String text, String datasetId, Integer limit) {
        if (StringUtils.isBlank(text)) {
            log.warn("searchWiki: 搜索文本为空");
            return Collections.emptyList();
        }

        // 使用默认数据集ID
        String targetDatasetId = StringUtils.isNotBlank(datasetId) ? datasetId : nacosPropertiesHolder.getCateringWikiDatasetId();
        limit = limit != null ? limit : nacosPropertiesHolder.getCateringWikiLimit();
        
        if (StringUtils.isBlank(targetDatasetId) || StringUtils.isBlank(nacosPropertiesHolder.getWikiAuthApiKey())) {
            log.error("searchWiki: Wiki配置常量缺失, datasetId={}, apiKey={}", 
                    targetDatasetId, StringUtils.isNotBlank(nacosPropertiesHolder.getWikiAuthApiKey()) ? "已配置" : "未配置");
            throw new IllegalStateException("Wiki配置常量缺失");
        }

        try {
            // 构建请求
            WikiSearchRequest request = new WikiSearchRequest();
            request.setDatasetId(targetDatasetId);
            request.setText(text);
            request.setLimit(limit != null ? limit : 2000);

            String url = nacosPropertiesHolder.getWikiAuthApiUrlPrefix() + "api/core/dataset/searchTest";
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(nacosPropertiesHolder.getWikiAuthApiKey().replace("Bearer ", ""));

            HttpEntity<WikiSearchRequest> httpEntity = new HttpEntity<>(request, headers);

            log.info("searchWiki: 发起请求, text={}, url={}", text, url);
            log.debug("searchWiki: 请求参数={}", JSON.toJSONString(request));

            // 发起请求
            ResponseEntity<WikiSearchResponse> responseEntity = restTemplate.postForEntity(
                    url, httpEntity, WikiSearchResponse.class);

            WikiSearchResponse response = responseEntity.getBody();
            log.info("searchWiki: 响应状态={}, 响应码={}", 
                    responseEntity.getStatusCode(), response != null ? response.getCode() : "null");

            if (response == null) {
                log.error("searchWiki: 响应为空");
                return Collections.emptyList();
            }

            if (response.getCode() == null || !response.getCode().equals(200)) {
                log.error("searchWiki: API返回错误, code={}, message={}", 
                        response.getCode(), response.getMessage());
                throw new RuntimeException("API返回错误: " + response.getMessage());
            }

            // 解析响应数据
            return parseSearchResults(response);

        } catch (Exception e) {
            log.error("searchWiki: 请求异常, text={}", text, e);
            return Collections.emptyList();
        }
    }

    /**
     * 解析搜索结果
     */
    private List<SearchResultItem> parseSearchResults(WikiSearchResponse response) {
        List<SearchResultItem> searchDataList = new ArrayList<>();
        
        if (response.getData() == null || response.getData().getList() == null) {
            log.warn("parseSearchResults: 响应数据为空");
            return searchDataList;
        }

        List<SearchResultItem> rawResults = response.getData().getList();
        log.debug("parseSearchResults: 解析{}条搜索结果", rawResults.size());

        for (SearchResultItem item : rawResults) {
            try {
                // 确保tokens字段存在，默认为0
                if (item.getTokens() == null) {
                    item.setTokens(0);
                }

                // 处理score字段：确保ScoreItem对象正确构建
                if (item.getScore() != null) {
                    List<ScoreItem> processedScores = new ArrayList<>();
                    for (ScoreItem scoreItem : item.getScore()) {
                        if (scoreItem != null) {
                            processedScores.add(scoreItem);
                        }
                    }
                    item.setScore(processedScores);
                }

                searchDataList.add(item);
                
            } catch (Exception e) {
                log.error("parseSearchResults: 解析结果项失败, id={}", 
                        item != null ? item.getId() : "null", e);
                // 继续处理其他项，不中断整个流程
            }
        }

        log.info("parseSearchResults: 成功解析{}条结果", searchDataList.size());
        return searchDataList;
    }
}
