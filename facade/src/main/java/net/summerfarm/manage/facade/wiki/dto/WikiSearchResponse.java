package net.summerfarm.manage.facade.wiki.dto;

import lombok.Data;

import java.util.List;

/**
 * Wiki知识库搜索响应
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class WikiSearchResponse {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private WikiSearchData data;
    
    @Data
    public static class WikiSearchData {
        /**
         * 搜索结果列表
         */
        private List<SearchResultItem> list;
    }
}
