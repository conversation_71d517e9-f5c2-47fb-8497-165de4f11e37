package net.summerfarm.manage.facade.scp.converter;

import net.summerfarm.manage.facade.scp.dto.DocInfoDTO;
import net.xianmu.scp.client.resp.doc.DocInfoResp;

public class DocConverter {

    public static DocInfoDTO toDocInfoResp(DocInfoResp docInfoResp) {
        if (docInfoResp == null) {
            return null;
        }
        DocInfoDTO docInfoDTO = new DocInfoDTO();
        docInfoDTO.setDoc(docInfoResp.getDoc());
        docInfoDTO.setLevelLabel(docInfoResp.getLevelLabel());
        docInfoDTO.setStockLevelMinimumDay(docInfoResp.getStockLevelMinimumDay());
        docInfoDTO.setStockLevelTargetDay(docInfoResp.getStockLevelTargetDay());
        docInfoDTO.setStockLevelMaximumDay(docInfoResp.getStockLevelMaximumDay());
        docInfoDTO.setSalesQuantity(docInfoResp.getSalesQuantity());
        docInfoDTO.setStockLevelMinimum(docInfoResp.getStockLevelMinimum());
        docInfoDTO.setStockLevelMaximum(docInfoResp.getStockLevelMaximum());
        docInfoDTO.setStockLevelTarget(docInfoResp.getStockLevelTarget());
        docInfoDTO.setInitStockQuantity(docInfoResp.getInitStockQuantity());
        return docInfoDTO;
    }
}
