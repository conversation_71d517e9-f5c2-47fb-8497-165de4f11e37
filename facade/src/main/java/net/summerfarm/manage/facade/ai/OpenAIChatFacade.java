package net.summerfarm.manage.facade.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.provider.OpenAIChatProvider;
import net.summerfarm.common.client.req.openai.OpenAIChatReq;
import net.summerfarm.common.client.resp.openai.OpenAIChatResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Description
 * @Date 2025/5/19 11:13
 * @<AUTHOR>
 */
@Component
@Slf4j
public class OpenAIChatFacade {

    @DubboReference
    private OpenAIChatProvider openAIChatProvider;

    /**
     * openAi chat
     *
     * <AUTHOR>
     * @date 2025/5/19 11:32
     */
    public String chat(String message, String prompt) {
        if (StringUtils.isBlank(message)) {
            return StringUtil.EMPTY_STRING;
        }
        OpenAIChatReq openAIChatReq = new OpenAIChatReq();
        openAIChatReq.setSystemPrompt(prompt);
        openAIChatReq.setUserMessage(message);
        DubboResponse<OpenAIChatResp> openAIChatResp = openAIChatProvider.chat(openAIChatReq);
        if (openAIChatResp == null || !openAIChatResp.isSuccess() || openAIChatResp.getData() == null) {
            log.error("invoke openAIChatProvider.chat fail req:{}", JSON.toJSONString(openAIChatReq));
            return StringUtil.EMPTY_STRING;
        }
        return openAIChatResp.getData().getRespMessage();
    }
}
