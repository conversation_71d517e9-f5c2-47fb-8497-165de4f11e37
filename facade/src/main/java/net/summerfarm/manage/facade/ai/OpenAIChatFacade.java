package net.summerfarm.manage.facade.ai;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.provider.OpenAIChatProvider;
import net.summerfarm.common.client.req.openai.OpenAIChatReq;
import net.summerfarm.common.client.resp.openai.OpenAIChatResp;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * @Description
 * @Date 2025/5/19 11:13
 * @<AUTHOR>
 */
@Component
@Slf4j
public class OpenAIChatFacade {

    @DubboReference
    private OpenAIChatProvider openAIChatProvider;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    /**
     * openAi chat
     *
     * <AUTHOR>
     * @date 2025/5/19 11:32
     */
    public String chat(String message, String prompt) {
        if (StringUtils.isBlank(message)) {
            return StringUtil.EMPTY_STRING;
        }
        OpenAIChatReq openAIChatReq = new OpenAIChatReq();
        openAIChatReq.setSystemPrompt(prompt);
        openAIChatReq.setUserMessage(message);
        DubboResponse<OpenAIChatResp> openAIChatResp = openAIChatProvider.chat(openAIChatReq);
        if (openAIChatResp == null || !openAIChatResp.isSuccess() || openAIChatResp.getData() == null) {
            log.error("invoke openAIChatProvider.chat fail req:{}", JSON.toJSONString(openAIChatReq));
            return StringUtil.EMPTY_STRING;
        }
        return openAIChatResp.getData().getRespMessage();
    }

    public Map<String, String> getGoodsAliasName(Map<String, String> skuNameMap) {
        if (CollectionUtil.isEmpty(skuNameMap)) {
            return Collections.emptyMap();
        }
        Map<String, String> skuAliasMap = Maps.newHashMap();
        skuNameMap.forEach((sku, pdName) -> {
            String alias = chat(pdName, nacosPropertiesHolder.getGoodsAliasPrompt());
            if (StringUtils.isBlank(alias)) {

                log.warn("getGoodsAliasName fail sku:{}, pdName:{}, prompt:{}", sku, pdName, nacosPropertiesHolder.getGoodsAliasPrompt());
                return;
            }
            skuAliasMap.put(sku, alias);
        });
        return skuAliasMap;
    }




}
