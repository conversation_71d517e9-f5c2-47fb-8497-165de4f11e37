package net.summerfarm.manage.domain.activity.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam;



/**
*
* <AUTHOR>
* @date 2024-05-31 17:39:38
* @version 1.0
*
*/
public interface MarketRuleHistoryQueryRepository {

    PageInfo<MarketRuleHistoryEntity> getPage(MarketRuleHistoryQueryParam param);

    MarketRuleHistoryEntity selectById(Long id);

    List<MarketRuleHistoryEntity> selectByCondition(MarketRuleHistoryQueryParam param);

}