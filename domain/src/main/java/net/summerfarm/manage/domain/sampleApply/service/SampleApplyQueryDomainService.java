package net.summerfarm.manage.domain.sampleApply.service;


import net.summerfarm.manage.domain.sampleApply.repository.SampleApplyQueryRepository;
import net.summerfarm.manage.domain.sampleApply.repository.SampleApplyCommandRepository;
import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 样品申请表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-01-02 14:00:39
 * @version 1.0
 *
 */
@Service
public class SampleApplyQueryDomainService {


}
