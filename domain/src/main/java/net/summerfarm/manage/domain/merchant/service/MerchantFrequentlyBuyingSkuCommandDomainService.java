package net.summerfarm.manage.domain.merchant.service;


import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuQueryRepository;
import net.summerfarm.manage.domain.merchant.repository.MerchantFrequentlyBuyingSkuCommandRepository;
import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 采购助手-常购清单表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
@Service
public class MerchantFrequentlyBuyingSkuCommandDomainService {


    @Autowired
    private MerchantFrequentlyBuyingSkuCommandRepository merchantFrequentlyBuyingSkuCommandRepository;
    @Autowired
    private MerchantFrequentlyBuyingSkuQueryRepository merchantFrequentlyBuyingSkuQueryRepository;



    public MerchantFrequentlyBuyingSkuEntity insert(MerchantFrequentlyBuyingSkuCommandParam param) {
        return merchantFrequentlyBuyingSkuCommandRepository.insertSelective(param);
    }


    public int update(MerchantFrequentlyBuyingSkuCommandParam param) {
        return merchantFrequentlyBuyingSkuCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return merchantFrequentlyBuyingSkuCommandRepository.remove(id);
    }
}
