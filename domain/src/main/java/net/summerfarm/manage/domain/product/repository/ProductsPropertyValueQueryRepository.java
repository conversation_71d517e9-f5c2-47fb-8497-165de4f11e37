package net.summerfarm.manage.domain.product.repository;

import net.summerfarm.manage.domain.product.entity.ProductsPropertyEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
public interface ProductsPropertyValueQueryRepository {

    List<ProductsPropertyValueEntity> listByPdIds(@Param("pdIds") List<Long> pdIds, @Param("propertyIds") List<Integer> propertyIds);

    /***
     * @author: lzh
     * @description: 校验销售属性
     * @date: 2024/4/30 18:14
     * @param: [params]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> updateCheckValueFormat(List<ProductsPropertyValueQueryParam> params);

    List<ProductsPropertyValueEntity> listByConditions(ProductsPropertyValueQueryParam params);

    List<ProductsPropertyValueEntity> selectSaleValueBySku(String sku);

    /**
     * 根据sku查询商品属性值
     *
     * <AUTHOR>
     * @date 2025/2/11 16:21
     */
    List<ProductsPropertyValueEntity> selectSaleValueBySkuList(List<String> skuList);
    List<ProductsPropertyValueEntity> selectSaleValueBySkuListAndPropertyIds(@Param("skuList") Set<String> skuList, @Param("propertyIds") List<Integer> propertyIds);


    List<ProductsPropertyValueEntity> listBySkuAndPdid(String sku, Long pdId);

    ProductsPropertyEntity selectOneConditions(ProductsPropertyValueQueryParam productsProperty);

    List<ProductsPropertyEntity> selectEffectKeyPropertyByCategoryId(Integer categoryId);

    List<String> equalsSpuByKeyProperty(String pdNo, Integer categoryId, Integer createType, List<ProductsPropertyValueQueryParam> params);

    List<String> selectSkusByKeyValue(String name, Integer status, Integer type, List<String> productsPropertyValue);
}
