package net.summerfarm.manage.domain.marketItem.service;


import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtCommandRepository;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 商品AI扩展信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
@Service
public class MarketItemAiExtQueryDomainService {


}
