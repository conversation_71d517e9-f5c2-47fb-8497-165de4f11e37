package net.summerfarm.manage.domain.payment.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity;
import net.summerfarm.manage.domain.payment.param.query.MasterPaymentQueryParam;



/**
*
* <AUTHOR>
* @date 2024-10-11 14:22:49
* @version 1.0
*
*/
public interface MasterPaymentQueryRepository {

    MasterPaymentEntity selectById(Long id);

    List<MasterPaymentEntity> selectByCondition(MasterPaymentQueryParam param);

}