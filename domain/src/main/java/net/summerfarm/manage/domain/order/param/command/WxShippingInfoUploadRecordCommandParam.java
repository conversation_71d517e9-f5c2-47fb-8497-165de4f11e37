package net.summerfarm.manage.domain.order.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-15 17:49:41
 * @version 1.0
 *
 */
@Data
public class WxShippingInfoUploadRecordCommandParam {
	/**
	 * 主键、自增
	 */
	private Long id;

	/**
	 * 主订单编号
	 */
	private String masterOrderNo;

	/**
	 * 交易流水号（请求流水号）
	 */
	private String transactionId;

	/**
	 * 状态：1-已上传发货信息
	 */
	private Integer status;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}