package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

/**
 * 商品基础信息实体，用于AI问题生成
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class ProductBasicInfoEntity {
    
    /**
     * SKU编码
     */
    private String sku;

    /**
     * 商品ID
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;
    
    /**
     * 规格
     */
    private String specification;
    
    /**
     * 产地
     */
    private String origin;
    
    /**
     * 体积(长*宽*高,m单位)
     */
    private String volume;
    
    /**
     * 重量
     */
    private String weightNum;
    
    /**
     * 重量单位
     */
    private String weightUnit;
    
    /**
     * 包装单位
     */
    private String pack;
    
    /**
     * 售后单位
     */
    private String afterSaleUnit;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 三级类目名称
     */
    private String thirdCategoryName;
    
    /**
     * 二级类目名称
     */
    private String secondCategoryName;
    
    /**
     * 一级类目名称
     */
    private String firstCategoryName;
    
    /**
     * 商品描述
     */
    private String pdDetail;
    
    /**
     * 商品主图
     */
    private String picturePath;
    
    /**
     * 商品详情图片
     */
    private String detailPicture;
    
    /**
     * 保质期时长
     */
    private String qualityTime;
    
    /**
     * 保质期单位
     */
    private String qualityTimeUnit;
    
    /**
     * 临期时长
     */
    private String warnTime;
    
    /**
     * 临期时长单位
     */
    private String warnTimeUnit;
    
    /**
     * 可退款原因
     */
    private String refundType;
    
    /**
     * 售后类型
     */
    private String afterSaleType;
    
    /**
     * 仓储区域
     */
    private String storageArea;
    
    /**
     * 产品属性_储藏温度
     */
    private String productAttrStorageTemp;
    
    /**
     * 产品属性_商品形态
     */
    private String productAttrGoodsForm;
    
    /**
     * 产品属性_乳脂含量
     */
    private String productAttrMilkFatContent;
    
    /**
     * 产品属性_使用方法
     */
    private String productAttrUsageMethod;
    
    /**
     * 产品属性_成分
     */
    private String productAttrIngredient;
    
    /**
     * 产品属性_面筋含量
     */
    private String productAttrGlutenContent;
    
    /**
     * 产品属性_口味
     */
    private String productAttrTaste;
    
    /**
     * 产品属性_是否含糖
     */
    private String productAttrContainsSugar;
    
    /**
     * 产品属性_湿度
     */
    private String productAttrHumidity;
    
    /**
     * 产品属性_肉类品种
     */
    private String productAttrMeatVariety;
    
    /**
     * 产品属性_其他
     */
    private String productAttrOther;
    
    /**
     * 产品属性_蔬菜品种
     */
    private String productAttrVegetableVariety;
    
    /**
     * 产品属性_品种
     */
    private String productAttrVariety;
    
    /**
     * 产品属性_熟度
     */
    private String productAttrRipeness;
    
    /**
     * 产品属性_每100g乳脂含量
     */
    private String productAttrMilkFatPer100g;
    
    /**
     * 产品属性_每100g含蛋白质
     */
    private String productAttrProteinPer100g;
    
    /**
     * SKU属性_规格
     */
    private String skuAttrSpecification;
    
    /**
     * SKU属性_级别
     */
    private String skuAttrLevel;
    
    /**
     * SKU属性_果规
     */
    private String skuAttrFruitSpec;
    
    /**
     * SKU属性_自定义属性
     */
    private String skuAttrCustom;
    
    /**
     * SKU属性_口味
     */
    private String skuAttrTaste;
    
    /**
     * SKU属性_原料/成品
     */
    private String skuAttrRawMaterialFinished;
    
    /**
     * SKU属性_尺寸
     */
    private String skuAttrSize;
    
    /**
     * SKU属性_重复利用
     */
    private String skuAttrReusable;
    
    /**
     * SKU属性_自动出库
     */
    private String skuAttrAutoOutbound;
}
