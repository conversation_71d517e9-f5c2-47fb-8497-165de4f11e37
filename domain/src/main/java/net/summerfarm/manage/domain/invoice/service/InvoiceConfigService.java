package net.summerfarm.manage.domain.invoice.service;

import net.summerfarm.manage.common.input.invoiceConfig.InvoiceConfigVO;
import net.summerfarm.manage.domain.invoice.entity.InvoiceConfig;
import net.summerfarm.manage.domain.invoice.repository.InvoiceConfigRepository;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InvoiceConfigService {
    @Resource
    InvoiceConfigRepository invoiceConfigRepository;

    public Map<Long, List<InvoiceConfig>> getInvoiceConfigTittleMap(List<Long> mids){
        InvoiceConfigVO invoiceConfigVO = new InvoiceConfigVO();
        invoiceConfigVO.setMIds(mids);
        invoiceConfigVO.setType(NumberUtils.INTEGER_ZERO);
        if (CollectionUtils.isEmpty(invoiceConfigVO.getMIds())){
            return new HashMap<>();
        }
        List<Long> queryMids = invoiceConfigVO.getMIds().stream().distinct().collect(Collectors.toList());
        invoiceConfigVO.setMIds(queryMids);

        List<InvoiceConfig> collect = invoiceConfigRepository.selectByAdminIdsType(invoiceConfigVO).stream().filter(
                it -> it.getType() <= 1
        ).collect(Collectors.toList());

        return collect.stream().collect(Collectors.groupingBy(InvoiceConfig::getMerchantId));
    }
}
