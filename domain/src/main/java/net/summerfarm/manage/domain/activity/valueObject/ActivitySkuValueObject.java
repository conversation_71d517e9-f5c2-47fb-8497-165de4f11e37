package net.summerfarm.manage.domain.activity.valueObject;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by wjd on 2017/9/25.
 */
@Data
public class ActivitySkuValueObject implements Serializable {

    private Long activityId;

    private String sku;

    private BigDecimal activityPrice;

    private BigDecimal salePrice;

    private String ladderPrice;



}
