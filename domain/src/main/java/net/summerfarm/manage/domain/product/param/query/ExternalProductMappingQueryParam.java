package net.summerfarm.manage.domain.product.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
@Data
public class ExternalProductMappingQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 映射类型 1-sku 2-类目
	 */
	private Integer type;

	/**
	 * 鲜沐内部值
	 */
	private String internalValue;

	/**
	 * 外部平台值
	 */
	private String externalValue;

	/**
	 * 外部平台值
	 */
	private List<String> externalValueList;

	/**
	 * 买手id
	 */
	private Long buyerId;

	/**
	 * 类目id
	 */
	private Long categoryId;

	/**
	 * 商品名称
	 */
	private String title;

	/**
	 * 忽视已映射的商品
	 * 	true: 搜索结果包含已绑定商品数据
	 * 	false：搜索结果过滤掉已绑定商品数据
	 */
	private boolean ignoreMapping;
}