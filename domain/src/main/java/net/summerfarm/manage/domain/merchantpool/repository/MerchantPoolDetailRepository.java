package net.summerfarm.manage.domain.merchantpool.repository;

import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolDetailEntity;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/22 16:27
 * @PackageName:net.summerfarm.manage.domain.merchantpool.repository
 * @ClassName: MerchantPoolDetailRepository
 * @Description: TODO
 * @Version 1.0
 */
public interface MerchantPoolDetailRepository {

    List<MerchantPoolDetailEntity> selectByPoolInfoIds(Collection<Long> scopeIds);
}
