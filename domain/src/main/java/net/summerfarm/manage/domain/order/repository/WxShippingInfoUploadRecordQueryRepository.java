package net.summerfarm.manage.domain.order.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam;



/**
*
* <AUTHOR>
* @date 2024-10-15 17:49:41
* @version 1.0
*
*/
public interface WxShippingInfoUploadRecordQueryRepository {

    PageInfo<WxShippingInfoUploadRecordEntity> getPage(WxShippingInfoUploadRecordQueryParam param);

    WxShippingInfoUploadRecordEntity selectById(Long id);

    List<WxShippingInfoUploadRecordEntity> selectByCondition(WxShippingInfoUploadRecordQueryParam param);

    WxShippingInfoUploadRecordEntity selectByMasterOrderNo(String masterOrderNo);

}