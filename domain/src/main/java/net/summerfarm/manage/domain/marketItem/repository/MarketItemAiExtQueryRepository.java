package net.summerfarm.manage.domain.marketItem.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;



/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
public interface MarketItemAiExtQueryRepository {

    PageInfo<MarketItemAiExtEntity> getPage(MarketItemAiExtQueryParam param);

    MarketItemAiExtEntity selectById(Long id);

    List<MarketItemAiExtEntity> selectByCondition(MarketItemAiExtQueryParam param);

    /**
     * 批量查询指定SKU和扩展类型的数据
     * @param skus SKU列表
     * @param extType 扩展类型
     * @return 匹配的数据列表
     */
    List<MarketItemAiExtEntity> batchSelectBySkusAndExtType(List<String> skus, Integer extType);

    /**
     * 根据SKU和扩展类型查询数据
     * @param sku
     * @param extType
     * @return
     */
    List<MarketItemAiExtEntity> selectBySkuAndExtType(String sku, Integer extType);
}