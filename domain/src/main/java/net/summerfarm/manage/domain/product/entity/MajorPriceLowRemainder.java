package net.summerfarm.manage.domain.product.entity;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-23
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class MajorPriceLowRemainder extends MajorPriceEntity {

    /**
     * 商城原价
     */
    private BigDecimal originalPrice;

    /**
     * 商城售卖价
     */
    private BigDecimal mallPrice;

    /**
     * 特价（原活动价）
     */
    private BigDecimal activityPrice;

    /**
     * 根据当前报价单规则计算的价格
     */
    private BigDecimal currentMajorPrice;

    /**
     * 当前报价单可用最小价格，规则：售价/活动价＜当前报价单规则计算的价格 ？ 售价/活动价 ：当前报价单规则计算的价格
     */
    private BigDecimal currentMinPrice;


    /**
     * 根据售价/活动价/当前报价单规则计算的价格 初始化最小可用价格
     */
    public void initCurrentMinPrice(){
        log.info("开始初始话化当前最优价：msg:{}", JSON.toJSONString(this));
        if(currentMajorPrice == null || mallPrice == null) {
            log.warn("商城售价/大客户报价为空! msg:{}", JSON.toJSONString(this));
            return;
        }
        if(activityPrice != null) {
            // 先比较活动价和报价
            currentMinPrice = activityPrice.min(currentMajorPrice);
            // 再比较商城售价
            currentMinPrice = currentMinPrice.min(mallPrice);
        } else {
            currentMinPrice = mallPrice.min(currentMajorPrice);
        }
    }
}
