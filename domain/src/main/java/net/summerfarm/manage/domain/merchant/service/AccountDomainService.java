package net.summerfarm.manage.domain.merchant.service;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import net.summerfarm.manage.domain.merchant.repository.MerchantSubAccountCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/27 11:34
 */
@Service
public class AccountDomainService {

    @Autowired
    private MerchantSubAccountCommandRepository accountCommandRepository;


    public Long addAccount(MerchantEntity merchant){
        MerchantSubAccountEntity accountEntity = new MerchantSubAccountEntity();
        accountEntity.setMId(merchant.getMId());
        accountEntity.setStatus(0);
        accountEntity.setDeleteFlag(1);
        accountEntity.setType(0);
        accountEntity.setPopView(0);
        accountEntity.setContact(merchant.getMcontact());
        accountEntity.setPhone(merchant.getPhone());
        accountEntity.setOpenid(merchant.getPhone());
        accountEntity.setRegisterTime(LocalDateTime.now());
        accountEntity = accountCommandRepository.insertSelective(accountEntity);
        return accountEntity.getAccountId();
    }
}
