package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.repository.ProductLabelValueQueryRepository;
import net.summerfarm.manage.domain.product.repository.ProductLabelValueCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductLabelValueCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: SKU标签映射表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-07 14:12:46
 * @version 1.0
 *
 */
@Service
public class ProductLabelValueCommandDomainService {


    @Autowired
    private ProductLabelValueCommandRepository productLabelValueCommandRepository;
    @Autowired
    private ProductLabelValueQueryRepository productLabelValueQueryRepository;



    public ProductLabelValueEntity insert(ProductLabelValueCommandParam param) {
        return productLabelValueCommandRepository.insertSelective(param);
    }


    public int update(ProductLabelValueCommandParam param) {
        return productLabelValueCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return productLabelValueCommandRepository.remove(id);
    }
}
