package net.summerfarm.manage.domain.delivery.repository;

import net.summerfarm.manage.domain.delivery.entity.DeliveryPlanEntity;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject;
import net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject;

import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName DeliveryPlanQueryRepository
 * @Description 配送计划查询
 * <AUTHOR>
 * @Date 11:10 2024/1/18
 * @Version 1.0
 **/
public interface DeliveryPlanQueryRepository {

    /***
     * @author: lzh
     * @description: 根据时间查询自动收货订单
     * @date: 2024/1/18 14:03
     * @param: [now, oldOrderNo]
     * @return: java.util.List<net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject>
     **/
    List<DeliveryPlanFlatObject> getAutoConfirmOrder(LocalDate now, String oldOrderNo, int pageIndex, int pageSize);

    /***
     * @author: lzh
     * @description: 根据订单号查询配送数量
     * @date: 2024/1/18 16:03
     * @param: [orderNo]
     * @return: java.lang.Integer
     **/
    Integer getDeliveryPlanQuantity(String orderNo);

    /***
     * @author: lzh
     * @description: 查询需要配送提醒的订单信息
     * @date: 2024/1/30 14:59
     * @param: [plusDays]
     * @return: java.util.List<net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject>
     **/
    List<DeliveryPlanFlatObject> noticeLists(LocalDate plusDays, String orderNo, int pageStart, int pageSize);

    /***
     * @author: lzh
     * @description: 省心送配送计划信息
     * @date: 2024/2/23 14:10
     * @param: [plusDays, orderNo, pageStart, pageSize]
     * @return: java.util.List<net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject>
     **/
    List<DeliveryPlanFlatObject> timingNoticeLists(LocalDate plusDays, String orderNo, int pageStart, int pageSize);

    /**
     * 查询城配仓省心送代销不入仓未冻结Sku信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<NoFreezeProxySaleNoWareNoSkuFlatObject> queryTimingOrderNoFreezeProxySaleNoWarehouse(LocalDate startDate, LocalDate endDate, Integer storeNo);

    /**
     * 查询待发货数量
     * @param normalOrderNos 订单号
     * @return 结果
     */
    List<DeliveryPlanFlatObject> getWaitingDeliveryPlanQuantity(List<String> normalOrderNos);

    /**
     * 根据配送日期、订单号查询有效的配送计划订单详情
     * @param deliveryTime 配送日期
     * @param orderNoList 订单号
     * @return 结果
     */
    List<OrderDeliveryPlanFlatObject> queryValidOrderDeliveryPlanDetail(LocalDate deliveryTime, List<String> orderNoList);


    List<DeliveryPlanEntity> getDeliveryPlanByOrderNo(String orderNo);

    Integer getDeliveryPlanQuantityById(Integer id);
}
