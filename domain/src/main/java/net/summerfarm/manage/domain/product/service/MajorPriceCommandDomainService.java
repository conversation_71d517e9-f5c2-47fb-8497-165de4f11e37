package net.summerfarm.manage.domain.product.service;


import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.summerfarm.manage.domain.product.repository.MajorPriceCommandRepository;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-08 15:19:21
 * @version 1.0
 *
 */
@Service
public class MajorPriceCommandDomainService {


    @Autowired
    private MajorPriceCommandRepository majorPriceCommandRepository;
    @Autowired
    private MajorPriceQueryRepository majorPriceQueryRepository;



    public MajorPriceEntity insert(MajorPriceCommandParam param) {
        return majorPriceCommandRepository.insertSelective(param);
    }

    public void insertBatch(List<MajorPriceCommandParam> params) {
        majorPriceCommandRepository.insertBatch(params);
    }

    public void updateBatch(List<MajorPriceCommandParam> updateList) {
        majorPriceCommandRepository.updateBatch(updateList);
    }


    public int update(MajorPriceCommandParam param) {
        return majorPriceCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return majorPriceCommandRepository.remove(id);
    }

    public void removeByIds(List<Integer> removeIds) {
        majorPriceCommandRepository.removeByIds(removeIds);
    }

    public void commitBatch(List<Long> ids) {
        majorPriceCommandRepository.commitBatch(ids);
    }
}
