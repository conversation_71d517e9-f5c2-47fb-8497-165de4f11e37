package net.summerfarm.manage.domain.marketItem.repository;



import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;




/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
public interface MarketItemAiExtCommandRepository {

    MarketItemAiExtEntity insertSelective(MarketItemAiExtCommandParam param);

    int updateSelectiveById(MarketItemAiExtCommandParam param);

    int remove(Long id);

}