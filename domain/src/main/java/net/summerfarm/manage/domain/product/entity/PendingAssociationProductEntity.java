package net.summerfarm.manage.domain.product.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 实体类：待关联商品信息
 * @Date 2025/4/3 16:20
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PendingAssociationProductEntity {

    /**
     * sku
     */
    private String sku;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 主图
     */
    private String picturePath;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;
}