package net.summerfarm.manage.domain.activity.service;


import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuDetailCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动sku配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Service
public class ActivitySkuDetailCommandDomainService {


    @Autowired
    private ActivitySkuDetailCommandRepository activitySkuDetailCommandRepository;
    @Autowired
    private ActivitySkuDetailQueryRepository activitySkuDetailQueryRepository;



    public ActivitySkuDetailEntity insert(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.insertSelective(param);
    }


    public int update(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return activitySkuDetailCommandRepository.remove(id);
    }
}
