package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-06 16:02:27
* @version 1.0
*
*/
public interface InventoryCommandRepository {

    InventoryEntity insertSelective(InventoryCommandParam param);

    int updateSelectiveBySku(InventoryCommandParam param);
    int updateWithNull(InventoryCommandParam param);
}