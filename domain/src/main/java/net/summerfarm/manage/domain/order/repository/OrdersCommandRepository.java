package net.summerfarm.manage.domain.order.repository;



import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.param.command.OrdersCommandParam;




/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:06
* @version 1.0
*
*/
public interface OrdersCommandRepository {

    OrdersEntity insertSelective(OrdersCommandParam param);

    int updateSelectiveById(OrdersCommandParam param);

    int remove(Long orderId);

    /***
     * @author: lzh
     * @description: 省心送确认收货修改对应表状态
     * @date: 2024/1/18 17:21
     * @param: [orderNo]
     * @return: void
     **/
    Boolean orderConfirm(String orderNo);
}