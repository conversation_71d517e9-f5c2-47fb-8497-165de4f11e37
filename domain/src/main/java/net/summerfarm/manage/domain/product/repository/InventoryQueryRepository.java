package net.summerfarm.manage.domain.product.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryGroupByLargeAreaParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationCategoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationProductQueryParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public interface InventoryQueryRepository {

    List<InventoryEntity> selectByIds(List<Long> invIds);

    PageInfo<ProductEntity> listByQuery(ProductPageQueryParam param);

    List<ProductEntity> listProductEntityByQuery(ProductPageQueryParam param);

    InventoryEntity queryBySku(String sku);

    PageInfo<ProductEntity> selectPage(ProductPageQueryParam param);

    InventoryEntity querySelectOne(InventoryQueryParam queryParam);

    List<InventoryEntity> selectByPdId(Long pdId);

    List<InventoryEntity> selectList(InventoryQueryParam query);

    List<InventoryEntity> listBySkus(List<String> skus);

    List<InventoryEntity> queryInfo(InventoryQueryParam param);

    PageInfo<InvetoryGroupByLargeAreaNoEntity> listByParamGroupByLargeArea(InventoryQueryGroupByLargeAreaParam param);

    List<String> querySkusByNameLike(String spuTitleLike);

    PageInfo<InventoryEntity> pageQueryInventory(InventoryQueryParam inventoryQueryParam);

    PageInfo<PendingAssociationProductEntity> pagePendingAssociationProduct(PendingAssociationProductQueryParam convert);

    List<CategoryEntity> listPendingAssociationCategory(PendingAssociationCategoryQueryParam convert);

    /**
     * 批量查询商品基础数据用于AI问题生成
     * @param skus SKU编码列表
     * @return 商品基础数据列表
     */
    List<ProductBasicInfoEntity> batchQueryProductBasicInfoForAI(List<String> skus);

    /**
     * 查询需要生成AI问题的SKU列表
     * @return 需要处理的SKU列表
     */
    List<String> querySkusNeedAiQuestions(Integer pageNo, Integer pageSize);
}
