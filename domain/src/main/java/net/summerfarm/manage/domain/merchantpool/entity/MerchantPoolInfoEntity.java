package net.summerfarm.manage.domain.merchantpool.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MerchantPoolInfoEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 分群名称
     */
    private String name;

    /**
     * 创建方式,0 在线圈选,1 Excel导入
     */
    private Boolean createWay;

    /**
     * 更新方式,0 不支持,1 自动,2 手动
     */
    private Boolean updateWay;

    /**
     * 数据状态
     */
    private Boolean status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前最新版本号
     */
    private Integer version;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后一次操作者
     */
    private String updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 数据源 0-mysql计算 1-数仓计算
     */
    private Boolean dataSource;
}

