package net.summerfarm.manage.domain.order.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import net.summerfarm.manage.domain.order.flatObject.AfterSaleOrderFlatObject;
import net.summerfarm.manage.domain.order.param.query.AfterSaleOrderQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-01-18 16:06:21
* @version 1.0
*
*/
public interface AfterSaleOrderQueryRepository {

    PageInfo<AfterSaleOrderEntity> getPage(AfterSaleOrderQueryParam param);

    AfterSaleOrderEntity selectById(Long id);

    List<AfterSaleOrderEntity> selectByCondition(AfterSaleOrderQueryParam param);

    /***
     * @author: lzh
     * @description: 查询售后数量
     * @date: 2024/1/18 16:20
     * @param: [orderNo]
     * @return: java.lang.Integer
     **/
    Integer getAfterSaleSuccessQuantity(String orderNo);

    /***
     * @author: lzh
     * @description: 根据订单号查询售后成功的信息
     * @date: 2024/1/23 16:53
     * @param: [param]
     * @return: java.util.List<net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity>
     **/
    List<AfterSaleOrderFlatObject> selectByOrderNo(AfterSaleOrderQueryParam param);

    /***
     * @author: lzh
     * @description: 根据订单号查询售后成功的信息
     * @date: 2024/1/23 16:53
     * @param: [normalOrderNos]
     * @return: java.util.List<net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity>
     **/
    List<AfterSaleOrderEntity> getAfterSaleInfoByOrderNos(List<String> normalOrderNos);


    /***
     * @author: lzh
     * @description: 根据订单号查询所有未到货售后
     * @date: 2024/1/23 16:53
     * @param: [normalOrderNos]
     * @return: java.util.List<net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity>
     **/
    List<AfterSaleOrderEntity> getNotDeliveryAfterSaleInfoByOrderNo(String order);
}