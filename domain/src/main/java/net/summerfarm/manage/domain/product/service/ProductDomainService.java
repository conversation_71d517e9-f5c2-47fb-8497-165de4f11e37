package net.summerfarm.manage.domain.product.service;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.common.query.product.SummerFarmSkuPriceInfoInput;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.facade.fence.FenceQueryFacade;
import net.summerfarm.manage.facade.fence.dto.DeliveryFenceDTO;
import net.xianmu.common.exception.BizException;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Slf4j
@Component
public class ProductDomainService {

    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;

    @Resource
    private FenceQueryFacade fenceQueryFacade;

    @Resource
    private AdminQueryRepository adminQueryRepository;

    @Resource
    private ProductsPropertyValueQueryRepository propertyValueQueryRepository;

    /**
     * 产地属性ID
     */
    public static final Integer ORIGIN = 1;

    /**
     * 品牌属性ID
     */
    public static final Integer BRAND = 2;
    /**
     * 是否常规属性
     */
    public static final Integer NOMAL = 6;

    @Resource
    private CategoryQueryRepository categoryQueryRepository;

    public List<SkuPriceInfoEntity> queryAdminSkuPriceInfo(
            List<SummerFarmSkuPriceInfoInput> inputList) {
        if (CollectionUtil.isEmpty(inputList)) {
            return Lists.newArrayList();
        }
        List<Long> adminIds = inputList.stream().map(e -> e.getAdminId()).distinct()
                .collect(Collectors.toList());
        List<AdminEntity> admins = adminQueryRepository.selectByAdminIds(adminIds);
        List<Long> skuIds = inputList.stream().map(x -> x.getSkuId()).distinct()
                .collect(Collectors.toList());
        List<InventoryEntity> inventoryEntities = inventoryQueryRepository.selectByIds(skuIds);
        if (CollectionUtils.isEmpty(inventoryEntities) || CollectionUtils.isEmpty(admins)) {
            return Lists.newArrayList();
        }
        List<String> cityNames = Lists.newArrayList();
        inputList.stream().map(x -> x.getCityName()).distinct().forEach(cityNames::addAll);
        // 查询围栏信息
        List<DeliveryFenceDTO> fenceVOs = fenceQueryFacade.queryFenceCity(cityNames);
        if (CollectionUtils.isEmpty(fenceVOs)) {
            return Lists.newArrayList();
        }
        Map<Long, AdminEntity> adminMap = admins.stream()
                .collect(Collectors.toMap(AdminEntity::getAdminId, Function.identity()));
        Map<Long, InventoryEntity> inventoryMap = inventoryEntities.stream()
                .collect(Collectors.toMap(InventoryEntity::getInvId, Function.identity()));
        //一个城市对应得围栏下可能有多个区域
        Map<String, List<DeliveryFenceDTO>> fenceVOMap = fenceVOs.stream()
                .collect(Collectors.groupingBy(DeliveryFenceDTO::getCityName));

        // 查询围栏信息
        List<SkuPriceInfoEntity> skuPriceInfoEntityList = new ArrayList<>();
        Map<Long, List<SummerFarmSkuPriceInfoInput>> adminIdGroups = inputList.stream()
                .collect(Collectors.groupingBy(SummerFarmSkuPriceInfoInput::getAdminId));
        for (Map.Entry<Long, List<SummerFarmSkuPriceInfoInput>> entry : adminIdGroups.entrySet()) {
            AdminEntity admin = adminMap.get(entry.getKey());
            if (admin == null || Objects.equals(admin.getIsDisabled(), 1)) {
                throw new BizException("admin参数异常");
            }
            List<SummerFarmSkuPriceInfoInput> skuPriceInfoInput = entry.getValue();
            Map<Long, List<SummerFarmSkuPriceInfoInput>> skuIdGroups = skuPriceInfoInput.stream()
                    .collect(Collectors.groupingBy(SummerFarmSkuPriceInfoInput::getSkuId));
            for (Map.Entry<Long, List<SummerFarmSkuPriceInfoInput>> skuEntry : skuIdGroups.entrySet()) {
                InventoryEntity inventory = inventoryMap.get(skuEntry.getKey());
                if (inventory == null) {
                    continue;
                }
                List<String> skuCityNames = Lists.newArrayList();
                skuEntry.getValue().stream().map(SummerFarmSkuPriceInfoInput::getCityName)
                        .forEach(skuCityNames::addAll);
                List<DeliveryFenceDTO> skuFenceVOS = Lists.newArrayList();
                for (String skuCityName : skuCityNames) {
                    if (!CollectionUtils.isEmpty(fenceVOMap.get(skuCityName))) {
                        skuFenceVOS.addAll(fenceVOMap.get(skuCityName));
                    }
                }
                // 查询围栏信息
                if (CollectionUtils.isEmpty(skuFenceVOS)) {
                    continue;
                }
                List<Integer> areaNos = skuFenceVOS.stream().map(DeliveryFenceDTO::getAreaNo)
                        .collect(Collectors.toList());
                //获取sku 在所有区域下价格
                List<AreaSkuEntity> areaSkus = areaSkuQueryRepository.queryListSkuPrice(
                        Collections.singletonList (inventory.getSku ()), areaNos,true);
                Map<Integer, AreaSkuEntity> areaSkuMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(areaSkus)) {
                    areaSkuMap = areaSkus.stream().collect(
                            Collectors.toMap(AreaSkuEntity::getAreaNo, Function.identity()));
                }
                //获取sku 在所有区域下报价单
                List<MajorPriceEntity> majorPrices = majorPriceQueryRepository.queryListMajorPrice(
                        admin.getAdminId().intValue(), inventory.getSku(), areaNos);
                Map<Integer, MajorPriceEntity> areaMajarPriceMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(majorPrices)) {
                    areaMajarPriceMap = majorPrices.stream().collect(
                            Collectors.toMap(MajorPriceEntity::getAreaNo, Function.identity()));
                }
                for (SummerFarmSkuPriceInfoInput priceInfoInput : skuEntry.getValue()) {
                    SkuPriceInfoEntity entity = new SkuPriceInfoEntity();
                    entity.setCitySupplyPriceId(priceInfoInput.getCitySupplyPriceId());
                    entity.setSkuId(priceInfoInput.getSkuId());
                    List<AreaSkuEntity> areaSkuList = Lists.newArrayList();
                    List<MajorPriceEntity> majorPriceList = Lists.newArrayList();
                    // 查询围栏信息
                    for (String cityName : priceInfoInput.getCityName()) {
                        List<DeliveryFenceDTO> fenceVOList = fenceVOMap.get(cityName);
                        if (CollectionUtils.isEmpty(fenceVOList)) {
                            continue;
                        }
                        for (DeliveryFenceDTO fenceVO : fenceVOList) {
                            AreaSkuEntity areaSku = areaSkuMap.get(fenceVO.getAreaNo());
                            if (Objects.nonNull(areaSku)) {
                                areaSkuList.add(areaSku);
                            }
                            MajorPriceEntity majorPrice = areaMajarPriceMap.get(
                                    fenceVO.getAreaNo());
                            if (Objects.nonNull(majorPrice)) {
                                majorPriceList.add(majorPrice);
                            }
                        }
                    }
                    Map<Integer, List<MajorPriceEntity>> majorMap;
                    if (!CollectionUtils.isEmpty(majorPriceList)) {
                        majorMap = majorPriceList.stream().collect(Collectors.groupingBy(MajorPriceEntity::getAreaNo));
                    } else {
                        majorMap = Collections.emptyMap ();
                    }

                    Set<BigDecimal> priceList = new HashSet<> ();
                    if (!CollectionUtils.isEmpty(areaSkuList)) {
                        for(AreaSkuEntity e:areaSkuList){
                            Integer areaNo = e.getAreaNo ();
                            List<MajorPriceEntity> majorPriceEntitys = majorMap.get (areaNo);
                            if(!CollectionUtils.isEmpty (majorPriceEntitys)){
                                majorPriceEntitys.forEach (majorPriceEntity -> priceList.add (PriceCalculator.calculateMajorPriceByType (majorPriceEntity.getPrice (),e.getPrice (),  majorPriceEntity.getPriceAdjustmentValue (), majorPriceEntity.getPriceType ())));
                            }
                            priceList.add(e.getPrice ());
                        }
                    }
                    entity.setMinPrice(priceList.stream().min(BigDecimal::compareTo).orElse(null));
                    entity.setMaxPrice(priceList.stream().max(BigDecimal::compareTo).orElse(null));
                    skuPriceInfoEntityList.add(entity);
                }
            }
        }
        return skuPriceInfoEntityList;
    }

    public PageInfo<ProductEntity> pageByQuery(ProductPageQueryParam param) {
        //先处理类目id,前端传过来的类目id,可能是1,2,3级都包含
        if (param != null && CollectionUtil.isNotEmpty(param.getCategoryIds())) {
            List<CategoryLevelEntity> categoryLevelList = categoryQueryRepository.listCategoryLevel(param.getCategoryIds());
            List<Long> categoryIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(categoryLevelList)) {
                List<Long> firstLevelList = categoryLevelList.stream().filter(x -> x.getFirstLevel() != null).map(t -> t.getFirstLevel()).distinct()
                        .collect(Collectors.toList());
                List<Long> secondLevelList = categoryLevelList.stream().filter(x -> x.getSecondLevel() != null).map(t -> t.getSecondLevel())
                        .distinct().collect(Collectors.toList());
                List<Long> thirdLevelList = categoryLevelList.stream().filter(x -> x.getThirdLevel() != null).map(t -> t.getThirdLevel()).distinct()
                        .collect(Collectors.toList());
                categoryIds.addAll(firstLevelList);
                categoryIds.addAll(secondLevelList);
                categoryIds.addAll(thirdLevelList);
            }
            param.setCategoryIds(categoryIds);
        }
        param.setNEqSubType (4);
        PageInfo<ProductEntity> pageInfo = inventoryQueryRepository.listByQuery(param);
        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        List<ProductEntity> entityList = pageInfo.getList();
        List<Long> pdIds = entityList.stream().map(x -> x.getPdId()).distinct().collect(Collectors.toList());
        //获取销售属性
        List<ProductsPropertyValueEntity> valueEntityList = propertyValueQueryRepository.listByPdIds(pdIds, null);
        Map<Long, List<ProductsPropertyValueEntity>> propertyMap = valueEntityList.stream()
                .collect(Collectors.groupingBy(ProductsPropertyValueEntity::getPdId));
        for (ProductEntity entity : entityList) {
            List<ProductsPropertyValueEntity> propertyValueEntityList = propertyMap.get(entity.getPdId());
            entity.setKeyValueList(propertyValueEntityList);
            entity.sortKeyValueList();
            entity.resetSkuNameAndSkuPic();
        }
        return pageInfo;
    }


    public PageInfo<ProductEntity> selectPage(ProductPageQueryParam param) {
        PageInfo<ProductEntity> pageInfo = inventoryQueryRepository.selectPage(param);
        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        List<ProductEntity> entityList = pageInfo.getList();
        List<Long> pdIds = entityList.stream().map(x -> x.getPdId()).distinct().collect(Collectors.toList());

        //获取销售属性
        List<Integer> salePropertyIds = new ArrayList<>();
        salePropertyIds.add(ORIGIN);
        salePropertyIds.add(BRAND);
        List<ProductsPropertyValueEntity> valueEntityList = propertyValueQueryRepository.listByPdIds(pdIds, salePropertyIds);
        Map<Long, List<ProductsPropertyValueEntity>> propertyMap = valueEntityList.stream()
                .collect(Collectors.groupingBy(ProductsPropertyValueEntity::getPdId));
        for (ProductEntity productEntity : entityList) {
            if (!propertyMap.containsKey(productEntity.getPdId())) {
                continue;
            }
            List<ProductsPropertyValueEntity> propertyValueEntityList = propertyMap.get(productEntity.getPdId());
            for (ProductsPropertyValueEntity productsPropertyValueEntity : propertyValueEntityList) {
                if (Objects.equals(productsPropertyValueEntity.getProductsPropertyId(), ORIGIN)) {
                    productEntity.setOrigin(productsPropertyValueEntity.getProductsPropertyValue());
                } else if (Objects.equals(productsPropertyValueEntity.getProductsPropertyId(), BRAND)) {
                    productEntity.setBrandName(productsPropertyValueEntity.getProductsPropertyValue());
                }
            }
        }
        return pageInfo;
    }
}
