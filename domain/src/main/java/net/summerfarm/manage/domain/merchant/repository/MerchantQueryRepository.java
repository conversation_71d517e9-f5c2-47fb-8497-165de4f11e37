package net.summerfarm.manage.domain.merchant.repository;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantQueryParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-19 10:55:24
* @version 1.0
*
*/
public interface MerchantQueryRepository {

    /**
     * 根据名称精确查询
     * 可直接接入买家中心
     * @param name
     * @return
     */
    List<MerchantEntity> selectByName(String name);


    /**
     * 是否存在相同邀请码
     * @param channelCode 邀请码
     * @return t、存在 f、不存在
     */
    boolean existChannelCode(String channelCode);


    MerchantEntity selectById(Long id);

    MerchantEntity selectByIdForceMaster(Long id);


    List<MerchantEntity>  selectByIds(List<Long> ids);

    /***
     * @author: lzh
     * @description: 根据条件获取用户信息
     * @date: 2024/1/23 17:23
     * @param: [param]
     * @return: net.summerfarm.manage.domain.merchant.entity.MerchantEntity
     **/
    MerchantEntity selectByParam(MerchantQueryParam param);
}