package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.domain.product.param.command.FrontCategoryCommandParam;




/**
*
* <AUTHOR>
* @date 2025-03-27 15:26:47
* @version 1.0
*
*/
public interface FrontCategoryCommandRepository {

    FrontCategoryEntity insertSelective(FrontCategoryCommandParam param);

    int updateSelectiveById(FrontCategoryCommandParam param);

    int remove(Integer id);

}