package net.summerfarm.manage.domain.product.repository;

import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public interface AreaSkuQueryRepository {

    List<AreaSkuEntity> queryListSkuPrice(List<String> skus, List<Integer> areaNos,Boolean onsale);

    List<AreaSkuEntity> selectVOList(String sku, Integer areaNo);
    AreaSkuEntity selectValidAndOnSale(Integer areaNo, String sku);
}
