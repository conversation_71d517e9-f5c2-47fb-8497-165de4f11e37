package net.summerfarm.manage.domain.admin.entity;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-06-18 23:13:51
 * @version 1.0
 *
 */
@Data
public class AdminDataPermissionEntity {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 
	 */
	private Integer adminId;

	/**
	 * 
	 */
	private String permissionValue;

	/**
	 * 
	 */
	private String permissionName;

	/**
	 * 
	 */
	private LocalDateTime addtime;

	/**
	 * 
	 */
	private String type;

	

	
}