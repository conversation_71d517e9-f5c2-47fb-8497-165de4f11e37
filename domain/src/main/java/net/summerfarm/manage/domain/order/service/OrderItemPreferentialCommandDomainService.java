package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.OrderItemPreferentialQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrderItemPreferentialCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrderItemPreferentialEntity;
import net.summerfarm.manage.domain.order.param.command.OrderItemPreferentialCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 订单优惠明细表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-31 15:06:17
 * @version 1.0
 *
 */
@Service
public class OrderItemPreferentialCommandDomainService {


    @Autowired
    private OrderItemPreferentialCommandRepository orderItemPreferentialCommandRepository;
    @Autowired
    private OrderItemPreferentialQueryRepository orderItemPreferentialQueryRepository;



    public OrderItemPreferentialEntity insert(OrderItemPreferentialCommandParam param) {
        return orderItemPreferentialCommandRepository.insertSelective(param);
    }


    public int update(OrderItemPreferentialCommandParam param) {
        return orderItemPreferentialCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return orderItemPreferentialCommandRepository.remove(id);
    }
}
