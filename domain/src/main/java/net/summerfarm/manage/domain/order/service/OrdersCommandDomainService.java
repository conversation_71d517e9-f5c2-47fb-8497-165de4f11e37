package net.summerfarm.manage.domain.order.service;


import net.summerfarm.manage.domain.order.repository.OrdersQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrdersCommandRepository;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.param.command.OrdersCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 订单领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-01-18 15:49:06
 * @version 1.0
 *
 */
@Service
public class OrdersCommandDomainService {


    @Autowired
    private OrdersCommandRepository ordersCommandRepository;
    @Autowired
    private OrdersQueryRepository ordersQueryRepository;



    public OrdersEntity insert(OrdersCommandParam param) {
        return ordersCommandRepository.insertSelective(param);
    }


    public int update(OrdersCommandParam param) {
        return ordersCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long orderId) {
        return ordersCommandRepository.remove(orderId);
    }

    /***
     * @author: lzh
     * @description: 确认收货修改对应表状态
     * @date: 2024/1/18 17:20
     * @param: [orderNo]
     * @return: void
     **/
    public Boolean orderConfirm(String orderNo) {
        ordersCommandRepository.orderConfirm(orderNo);
        return Boolean.TRUE;
    }
}
