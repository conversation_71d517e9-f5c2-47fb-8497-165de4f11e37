package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2024-04-08 15:19:21
* @version 1.0
*
*/
public interface MajorPriceCommandRepository {

    MajorPriceEntity insertSelective(MajorPriceCommandParam param);
    void insertBatch(List<MajorPriceCommandParam> params);
    void updateBatch(List<MajorPriceCommandParam> updateList);

    int updateSelectiveById(MajorPriceCommandParam param);

    int remove(Long id);
    void removeByIds(List<Integer> removeIds);

    void commitBatch(List<Long> ids);

    void deleteBySku(String sku, Integer adminId, Integer direct);
}