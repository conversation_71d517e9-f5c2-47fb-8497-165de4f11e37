package net.summerfarm.manage.domain.searchSynonym.repository;



import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;




/**
*
* <AUTHOR>
* @date 2025-04-24 14:53:58
* @version 1.0
*
*/
public interface ProductSearchSynonymDictionaryCommandRepository {

    ProductSearchSynonymDictionaryEntity insertSelective(ProductSearchSynonymDictionaryCommandParam param);

    int updateSelectiveById(ProductSearchSynonymDictionaryCommandParam param);

    int remove(Integer id);

}