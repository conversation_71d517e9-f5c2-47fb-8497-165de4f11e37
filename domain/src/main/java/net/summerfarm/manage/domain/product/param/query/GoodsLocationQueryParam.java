package net.summerfarm.manage.domain.product.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-07 14:36:00
 * @version 1.0
 *
 */
@Data
public class GoodsLocationQueryParam extends BasePageInput {
	/**
	 * 
	 */
	private Integer id;

	/**
	 * 仓库编号
	 */
	private Integer storeNo;

	/**
	 * 货位编码
	 */
	private String glNo;

	/**
	 * 创建时间
	 */
	private LocalDateTime addTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 温区 。常温，冷藏，冷冻，对应的编码为3，2，1
	 */
	private Integer temperature;

	/**
	 * 通道
	 */
	private String passageway;

	/**
	 * 类型 0 储货位 1 暂存位 2 捡货位
	 */
	private Integer type;

	

	
}