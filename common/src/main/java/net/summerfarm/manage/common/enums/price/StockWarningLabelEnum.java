package net.summerfarm.manage.common.enums.price;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

import java.math.BigDecimal;

/**
 * 库存预警标签
 */
@Getter
@AllArgsConstructor
public enum StockWarningLabelEnum implements Enum2Args {

    UNSALABLE_EARLY_WARNING(1,"滞销预警"),
    UNSALABLE_WARNING(2,"滞销风险"),
    URGENT_UNSALABLE_WARNING(3,"紧急滞销"),
    SELLOUT_EARLY_WARNING(4,"售罄预警"),
    SELLOUT_WARNING(5,"售罄风险"),
    URGENT_SELLOUT_WARNING(6,"紧急售罄风险"),
    DAILY_SALE_QUANTITY_ZERO(7,"日销为0"),
    DATA_INCOMPLETE(7,"数据不完整"),
    ;

    private Integer value;

    private String content;

    public static StockWarningLabelEnum dealLabel(BigDecimal stockLevelMaximumDay, BigDecimal stockLevelMinimumDay, BigDecimal doc, Integer salesQuantity) {
        // 优先判断日销为0的情况
        if (salesQuantity != null && salesQuantity == 0) {
            return DAILY_SALE_QUANTITY_ZERO;
        }
        // 没有配置的情况
        if (stockLevelMaximumDay == null || stockLevelMinimumDay == null || doc == null) {
            return DATA_INCOMPLETE;
        }
        // 和最大目标库存的比决定滞销标签
        //- 最大目标库存天数<DOC<最大目标库存天数（1+10%），滞销预警
        //- 最大目标库存天数（1+10%）≤DOC<最大目标库存天数（1+30%），滞销风险
        //- DOC≥最大目标库存天数（1+30%），紧急滞销
        if (doc.compareTo(stockLevelMaximumDay) > 0) {
            if (doc.compareTo(stockLevelMaximumDay.multiply(new BigDecimal("1.1"))) < 0) {
                return UNSALABLE_EARLY_WARNING;
            } else if (doc.compareTo(stockLevelMaximumDay.multiply(new BigDecimal("1.3"))) < 0) {
                return UNSALABLE_WARNING;
            } else {
                return URGENT_UNSALABLE_WARNING;
            }
        }
        // 和最小目标库存比决定售罄标签
        //- 最低库存天数（1-10%）<DOC<最低库存天数，售罄预警
        //- 最低库存天数（1-10%）≥DOC>最低库存天数（1-20%），售罄风险
        //- DOC≤最低库存天数（1-20%），紧急售罄风险
        if (doc.compareTo(stockLevelMinimumDay) < 0) {
            if (doc.compareTo(stockLevelMinimumDay.multiply(new BigDecimal("0.9"))) > 0) {
                return SELLOUT_EARLY_WARNING;
            } else if (doc.compareTo(stockLevelMaximumDay.multiply(new BigDecimal("0.8"))) > 0) {
                return SELLOUT_WARNING;
            } else {
                return URGENT_SELLOUT_WARNING;
            }
        }
        return null;
    }
}
