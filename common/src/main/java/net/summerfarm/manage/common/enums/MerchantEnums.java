package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/9/27 13:50
 */
public interface MerchantEnums {


    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    enum PreRegisterFlag implements Enum2Args {
        /**
         * 预注册标记
         */
        YES(1, "预注册"),
        NO(0, "非预注册")
        ;

        private Integer value;
        private String content;
    }

    @Getter
    enum MerchantIsLockEnum {

        APPROVE(0, "审核通过"),
        AUDIT(1, "审核中"),
        FAILED_AUDIT(2, "审核未通过"),
        BLACK(3, "账号被拉黑"),
        CANCELLED(4, "已注销");
        private Integer code;

        private String value;

        MerchantIsLockEnum(Integer code, String value) {
            this.code = code;
            this.value = value;
        }
    }

    enum BusinessLineEnum {

        /**
         * 鲜沐
         */
        XM(0, "鲜沐"),

        /**
         * pop
         */
        POP(1, "pop");

        private final Integer code;
        private final String description;

        BusinessLineEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
