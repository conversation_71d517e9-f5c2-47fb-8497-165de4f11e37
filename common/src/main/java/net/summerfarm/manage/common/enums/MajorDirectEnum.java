package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MajorDirectEnum {
    /**
     * 1-账期
     */
    PERIOD(1, "账期"),
    /**
     * 2-现结
     */
    CASH(2, "现结");

    private Integer type;

    private String desc;

    public static MajorDirectEnum valueOfDesc(String directString) {
        for (MajorDirectEnum c : MajorDirectEnum.values()) {
            if (c.getDesc () .equals(directString)) {
                return c;
            }
        }
        return null;
    }
    public static MajorDirectEnum valueOfType(Integer type) {
        for (MajorDirectEnum c : MajorDirectEnum.values()) {
            if (c.getType ().equals(type)) {
                return c;
            }
        }
        return null;
    }
}
