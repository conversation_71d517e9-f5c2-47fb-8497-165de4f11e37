package net.summerfarm.manage.common.enums.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/10  11:49
 */
public class PaymentEnums {
    /**
     * 支付渠道枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PaymentChannel {
        WECHAT(0, "微信支付"),
        XIANMU_CARD(1, "鲜沐卡"),
        @Deprecated
        BOC(2, "BOC"),
        ACCOUNT(3, "账期"),
        CMB(4, "招行");

        /**
         * 支付渠道
         */
        private final Integer channel;
        /**
         * 支付渠道描述
         */
        private final String desc;
    }



    /**
     * 支付平台枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PaymentPlatform {
        ZFBA("ZFBA", "支付宝"),
        WEIX("WEIX", "微信"),
        UPAY("UPAY", "银联二维码");

        /**
         * 付款类型
         */
        private final String type;
        /**
         * 付款类型描述
         */
        private final String desc;
    }

    /**
     * 支付枚举，payment表status
     */
    @Getter
    @AllArgsConstructor
    public enum PaymentStatus {
        PAYING(0, "支付中"),
        SUCCESS(1, "支付成功"),
        CANCEL(11,"支付取消");

        /**
         * 支付状态
         */
        private final Integer status;
        /**
         * 支付状态描述
         */
        private final String desc;
    }
}
