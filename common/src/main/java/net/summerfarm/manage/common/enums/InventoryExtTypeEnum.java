package net.summerfarm.manage.common.enums;

public enum InventoryExtTypeEnum {


    NORMAL(0, "常规"),
    ACTIVITY(1, "活动"),
    TEMPORARY_INSURANCE(2, "临保"),
    UNPACKING(3, "拆包"),
    NOT_SALE(4, "不卖"),
    BROKEN_BAG(5, "破袋");


    private Integer type;

    private String desc;

    InventoryExtTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getValue(Integer type) {
        InventoryExtTypeEnum[] extTypeEnums = values();
        for (InventoryExtTypeEnum extTypeEnum : extTypeEnums) {
            if (extTypeEnum.type().equals(type)) {
                return extTypeEnum.desc();
            }
        }
        return null;
    }

    public static Integer getType(String desc) {
        InventoryExtTypeEnum[] extTypeEnums = values();
        for (InventoryExtTypeEnum extTypeEnum : extTypeEnums) {
            if (extTypeEnum.desc().equals(desc)) {
                return extTypeEnum.type();
            }
        }
        return null;
    }

    /**
     * 是否临保或者破袋
     * @return
     */
    public static boolean isWillExpireOrBroken(Integer type) {
        if (TEMPORARY_INSURANCE.type.equals(type) || BROKEN_BAG.type.equals(type)) {
            return true;
        }
        return false;
    }

    public Integer type() {
        return this.type;
    }

    private String desc() {
        return this.desc;
    }
}