package net.summerfarm.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Nacos动态配置；
 */
@Data
@Slf4j
@Component
@NacosConfigurationProperties(dataId = "${spring.application.name}",
    type = ConfigType.PROPERTIES,
    autoRefreshed = true,
    ignoreInvalidFields = true)
public class NacosPropertiesHolder implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;
    /**
     * pop 默认挂属大客户id
     */
    @Value(value = "${popDefaultAdminId:999999}")
    private Long popDefaultAdminId;

    /**
     * 大客户门店列表是否查询老版的运费规则（DistributionRules）
     */
    @Value(value = "${majorMerchantListQueryDistributionRules:true}")
    private Boolean majorMerchantListQueryDistributionRules;


    /**
     * 微信自动发货延迟时间（单位：秒）
     */
    @Value(value = "${weixinShippingDelayTime:1800}")
    private Long weixinShippingDelayTime;



    /**
     * 微信自动发货开关
     */
    @Value(value = "${weixinShippingSwitch:true}")
    private Boolean weixinShippingSwitch;

    /**
     * 竞品基础数据数量下限
     *  ps: 小于该值则不触发失效商品清理动作
     */
    @Value(value = "${externalProductCount:1000}")
    private int externalProductCount;

    /**
     * pop 需要加价的分销区域
     */
    @Value(value = "${popMarkupPriceAreaNos:0}")
    private List<Integer> popMarkupPriceAreaNos;

    /**
     * 鲜沐复制到pop默认仓库
     */
    @Value(value = "${defaultXmToPopWarehouseNo:10}")
    private Integer defaultXmToPopWarehouseNo;


    @NacosValue(value = "${goodsRelationQuestionPrompt:请根据以下商品信息，生成5-10个客户可能会询问的关于该商品的问题。问题应该涵盖商品的特性、用途、保存方法、营养价值等方面，问题尽量短一些。请以JSON数组格式返回，每个元素是一个问题字符串。}", autoRefreshed = true)
    private String goodsRelationQuestionPrompt;

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
