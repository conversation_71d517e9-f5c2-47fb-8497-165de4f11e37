package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MajorPriceStatusEnum {
    SAVE(0, "保存"),
    COMMIT(1, "提交");

    private Integer type;

    private String desc;



    public static MajorPriceStatusEnum valueOfDesc(String directString) {
        for (MajorPriceStatusEnum c : MajorPriceStatusEnum.values()) {
            if (c.getDesc () .equals(directString)) {
                return c;
            }
        }
        return null;
    }
}
