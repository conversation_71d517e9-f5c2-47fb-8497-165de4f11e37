package net.summerfarm.manage.common.util;


import jodd.util.StringUtil;

/**
 * @Description
 * @Date 2025/4/15 17:13
 * @<AUTHOR>
 */
public class VolumeUtil {

    private static String pattern = "^\\d+(\\.\\d+)?\\*\\d+(\\.\\d+)?\\*\\d+(\\.\\d+)?$";

    public static boolean isValidVolumeStr(String volume) {
        // 为空跳过校验
        if (StringUtil.isBlank(volume)) {
            return true;
        }
        return volume.matches(pattern);
    }

}
