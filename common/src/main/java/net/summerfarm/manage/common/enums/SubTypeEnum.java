package net.summerfarm.manage.common.enums;

/**
 * @Classname SubTypeEnum
 * @Description 商品二级性质枚举
 * @Date 2021/11/15 13:42
 * @Created by hx
 */
public enum SubTypeEnum {
    SELF_NOT_INTO_WAREHOUSE(1,"[自营-代销不入仓]"),
    SELF_INTO_WAREHOUSE(2,"[自营-代销入仓]"),
    SELF_DISTRIBUTION(3,"[自营-经销]"),
    PROXY_AGENT(4,"[代仓-代仓]"),
    POP(5,"POP鲜果"),
    ;


    private Integer extType;
    private String desc;

    SubTypeEnum(Integer extType, String desc) {
        this.extType = extType;
        this.desc = desc;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getValueByKey(Integer key){
        for (SubTypeEnum c : SubTypeEnum.values()) {
            if (c.getExtType().equals(key)) {
                return c.getDesc();
            }
        }
        return null;
    }
}


