package net.summerfarm.manage.application.service.product.mall;

import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.AreaSkuVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.SkuAreaWarehouseNoMapVO;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AreaSkuQueryService {
    List<AreaSkuEntity> queryAreaSkuBySkuList(List<String> skus, Boolean onsale);

    /**
     * 根据运营大区 及sku 查询可用运营区域
     * @return
     */
    SkuAreaWarehouseNoMapVO queryAvailableAreaMap(Map<String, List<Integer>> skuLargeAreaNoMap,Collection<Integer> largeAreaNos);

    Map<String, List<WarehouseBySkuAreaNoResp>> getWarehouseBySkuAreaNoRespMap(Map<String, Set<Integer>> skuAreaNoMap);
}
