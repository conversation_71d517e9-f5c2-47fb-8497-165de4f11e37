package net.summerfarm.manage.application.inbound.controller.product;

import net.summerfarm.manage.application.inbound.controller.product.assembler.ProductsPropertyMappingAssembler;
import net.summerfarm.manage.application.inbound.controller.product.input.ProductsPropertyMappingQueryInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductsPropertyMappingVO;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingRelationInfo;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyMappingQueryRepository;
import net.summerfarm.manage.domain.product.service.ProductsPropertyMappingQueryDomainService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2025/3/31 13:41
 * @<AUTHOR>
 */
@RestController
@RequestMapping("/products_property")
public class ProductsPropertyController {

    @Resource
    private ProductsPropertyMappingQueryRepository productsPropertyMappingQueryRepository;

    /**
     * 查询自定义属性映射列表
     *
     * <AUTHOR>
     * @date 2025/3/31 15:15
     */
    @RequestMapping("/query/listProductsPropertyMapping")
    public CommonResult<List<ProductsPropertyMappingVO>> listProductsPropertyMapping(@RequestBody @Valid ProductsPropertyMappingQueryInput queryInput) {
        List<ProductsPropertyMappingRelationInfo> mappingRelationInfoList = productsPropertyMappingQueryRepository.listProductsPropertyMapping(ProductsPropertyMappingAssembler.assembleQueryParam(queryInput));
        List<ProductsPropertyMappingVO> result = mappingRelationInfoList.stream().map(ProductsPropertyMappingAssembler::assemble).collect(Collectors.toList());
        return CommonResult.ok(result);
    }

}
