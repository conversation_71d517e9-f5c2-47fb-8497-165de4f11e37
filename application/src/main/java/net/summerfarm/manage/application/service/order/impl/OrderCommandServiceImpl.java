package net.summerfarm.manage.application.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.manage.application.service.area.dto.MemberDTO;
import net.summerfarm.manage.application.service.order.OrderCommandService;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.constants.RocketMqConstant;
import net.summerfarm.manage.common.enums.*;
import net.summerfarm.manage.common.enums.activity.MarketRuleCouponRuleEnum;
import net.summerfarm.manage.common.enums.activity.MarketRuleHistorySendStatusEnum;
import net.summerfarm.manage.common.enums.activity.MarketRuleHistoryTypeEnum;
import net.summerfarm.manage.common.enums.coupon.CouponReceiveTypeEnum;
import net.summerfarm.manage.common.enums.order.OrderItemPreferentialTypeEnum;
import net.summerfarm.manage.common.input.order.AutoOrderMqInput;
import net.summerfarm.manage.common.input.order.AutoOrderSendCouponDTO;
import net.summerfarm.manage.common.middleware.MQOperator;
import net.summerfarm.manage.common.valueobject.MQData;
import net.summerfarm.manage.domain.activity.entity.MarketRuleEntity;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.command.MarketRuleHistoryCommandParam;
import net.summerfarm.manage.domain.activity.param.query.MarketRuleHistoryQueryParam;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryQueryRepository;
import net.summerfarm.manage.domain.activity.service.MarketRuleHistoryCommandDomainService;
import net.summerfarm.manage.domain.activity.service.MarketRuleHistoryQueryDomainService;
import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.coupon.param.command.MerchantCouponCommandParam;
import net.summerfarm.manage.domain.coupon.service.MerchantCouponCommandDomainService;
import net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject;
import net.summerfarm.manage.domain.delivery.param.command.DeliveryPlanCommandParam;
import net.summerfarm.manage.domain.delivery.repository.DeliveryPlanQueryRepository;
import net.summerfarm.manage.domain.delivery.service.DeliveryCommonDomainService;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;
import net.summerfarm.manage.domain.merchant.param.query.MerchantQueryParam;
import net.summerfarm.manage.domain.merchant.repository.MerchantQueryRepository;
import net.summerfarm.manage.domain.merchant.service.MerchantDomainService;
import net.summerfarm.manage.domain.order.entity.*;
import net.summerfarm.manage.domain.order.flatObject.AfterSaleOrderFlatObject;
import net.summerfarm.manage.domain.order.param.command.OrdersCommandParam;
import net.summerfarm.manage.domain.order.param.query.*;
import net.summerfarm.manage.domain.order.repository.*;
import net.summerfarm.manage.domain.order.service.OrdersCommandDomainService;
import net.summerfarm.manage.domain.trade.service.TimingOrderRefundTimeCommandDomainService;
import net.summerfarm.manage.facade.deliivery.TmsDistOrderDetailQueryFacade;
import net.summerfarm.manage.facade.deliivery.dto.DistOrderDTO;
import net.summerfarm.manage.facade.deliivery.input.DistOrderDetailInput;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OrderServiceImpl
 * @Description 订单处理
 * <AUTHOR>
 * @Date 18:11 2024/1/17
 * @Version 1.0
 **/
@Service
@Slf4j
public class OrderCommandServiceImpl implements OrderCommandService {

    @Resource
    private TmsDistOrderDetailQueryFacade tmsDistOrderDetailFacade;

    @Resource
    private DeliveryPlanQueryRepository deliveryPlanQueryRepository;

    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;

    @Resource
    private OrdersCommandDomainService ordersCommandDomainService;

    @Resource
    private AfterSaleOrderQueryRepository afterSaleOrderQueryRepository;

    @Resource
    private DeliveryCommonDomainService deliveryCommonDomainService;

    @Resource
    private MerchantDomainService merchantDomainService;

    @Resource
    private TimingOrderRefundTimeCommandDomainService timeCommandDomainService;

    @Resource
    private OrdersQueryRepository ordersQueryRepository;

    @Resource
    private MerchantQueryRepository merchantQueryRepository;

    @Resource
    private AreaQueryRepository areaQueryRepository;

    @Resource
    private OrderRelationQueryRepository orderRelationQueryRepository;

    @Resource
    private MQOperator mqOperator;

    @Resource
    @Lazy
    private OrderCommandService orderCommandService;

    @Resource
    private OrderItemPreferentialQueryRepository orderItemPreferentialQueryRepository;

    @Resource
    private MerchantCouponCommandDomainService merchantCouponCommandDomainService;

    @Resource
    private MarketRuleHistoryQueryRepository marketRuleHistoryQueryRepository;

    @Resource
    private MarketRuleHistoryCommandDomainService marketRuleHistoryCommandDomainService;


    @Override
    public void autoConfirm(String oldOrderNo) {
        //查询配送时间在<=（今天-1)的订单
        int pageIndex = 1;
        int pageSize = 500;

        while (true) {
            int pageStart = pageSize * (pageIndex - 1);
            List<DeliveryPlanFlatObject> autoConfirmOrders = deliveryPlanQueryRepository.getAutoConfirmOrder(LocalDate.now(), oldOrderNo, pageStart, pageSize);
            log.info("OrderCommandService[]autoConfirm[]autoConfirmOrders[]size:{}", autoConfirmOrders.size());
            if (CollectionUtils.isEmpty(autoConfirmOrders)) {
                break;
            }

            List<AutoOrderSendCouponDTO> autoOrderSendCouponDTOS = new ArrayList<>();
            List<String> abnormalOrderNos = new ArrayList<>();
            for (DeliveryPlanFlatObject autoConfirmOrder : autoConfirmOrders) {
                Integer orderType = autoConfirmOrder.getType();
                String orderNo = autoConfirmOrder.getOrderNo();
                Long mId = autoConfirmOrder.getMId();

                //自提订单由出库任务控制状态 , 订单未被拦截才可收货，配送状态为完成才可自动收货
                DistOrderDetailInput distOrderDetailInput = new DistOrderDetailInput();
                distOrderDetailInput.setOrderNo(orderNo);
                distOrderDetailInput.setContactId(autoConfirmOrder.getContactId());
                distOrderDetailInput.setDeliveryTime(autoConfirmOrder.getDeliveryTime());
                distOrderDetailInput.setSource(TmsSourceEnum.getDistOrderSource(orderType));
                DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(distOrderDetailInput);

                //校验配送状态是否已完成
                if (Objects.isNull(distOrderDTO) || Objects.isNull(distOrderDTO.getStatus()) ||
                        !DeliveryStatusEnum.COMPLETE_DELIVERY.getStatus().equals(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()))) {
                    log.warn("OrderCommandService[]autoConfirm[]distOrderDTO[]abnormal[]distOrderDetailInput:{}",
                            JSON.toJSONString(distOrderDetailInput));
                    continue;
                }

                //校验是否自提或者已经了被拦截
                if (Objects.equals(autoConfirmOrder.getDeliverytype(), CommonStatus.YES.getCode()) ||
                        Objects.equals(autoConfirmOrder.getInterceptFlag(), CommonStatus.YES.getCode())) {
                    log.warn("OrderCommandService[]autoConfirm[]deliverytype or interceptFlag[]autoConfirmOrder:{}",
                            JSON.toJSONString(autoConfirmOrder));
                    continue;
                }

                OrdersEntity ordersEntity = ordersQueryRepository.selectByOrderNo(orderNo);
                if (Objects.isNull(ordersEntity)) {
                    log.warn("OrderCommandService[]autoConfirm[]ordersEntity is null orderNo:{}",
                            orderNo);
                    continue;
                }

                MerchantEntity merchant = merchantQueryRepository.selectById(mId);

                try {
                    //执行具体的确认收货逻辑  省心送 普通和代下单分开处理
                    orderCommandService.startAutoConfirm(autoConfirmOrder, merchant, ordersEntity, autoOrderSendCouponDTOS);
                } catch (Exception e) {
                    log.warn("OrderCommandService[]autoConfirm[]startAutoConfirm[]error[]autoConfirmOrder:{}, e:{}",
                            JSON.toJSONString(autoConfirmOrder), JSON.toJSONString(e));

                    //记录异常订单号
                    abnormalOrderNos.add(orderNo);
                }
            }

            //发送mq 发放满返券
            if (!CollectionUtils.isEmpty(autoOrderSendCouponDTOS)) {
                //过滤异常订单
                if (!CollectionUtils.isEmpty(abnormalOrderNos)) {
                    log.info("OrderCommandService[]autoConfirm[]sendDataToQueue[]abnormalOrderNos:{}", JSON.toJSONString(abnormalOrderNos));
                    autoOrderSendCouponDTOS = autoOrderSendCouponDTOS.stream().filter(autoOrderSendCouponDTO ->
                            !abnormalOrderNos.contains(autoOrderSendCouponDTO.getOrderNo())).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(autoOrderSendCouponDTOS)) {
                    log.info("OrderCommandService[]autoConfirm[]sendDataToQueue[]autoOrderSendCouponDTOS is empty!");
                    return;
                }
                AutoOrderMqInput autoOrderMqInput = new AutoOrderMqInput();
                autoOrderMqInput.setAutoOrderSendCouponDTOS(autoOrderSendCouponDTOS);
                mqOperator.sendDataToQueue(RocketMqConstant.Topic.TOPIC_MARKET_CENTER_NOTICE,
                        RocketMqConstant.SendCouponTag.TAG_AUTO_CONFIRM_SEND_COUPON, autoOrderMqInput);
            }

            if(autoConfirmOrders.size() < pageSize){
                log.info("OrderCommandService[]autoConfirm[]pageIndex:{}", pageIndex);
                break;
            }

            pageIndex++;
        }
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void startAutoConfirm(DeliveryPlanFlatObject autoConfirmOrder, MerchantEntity merchant, OrdersEntity ordersEntity,
                                 List<AutoOrderSendCouponDTO> autoOrderSendCouponDTOS) {
        DeliveryPlanCommandParam commandParam;
        Integer orderType = autoConfirmOrder.getType();
        String orderNo = autoConfirmOrder.getOrderNo();
        if (Objects.equals(orderType, OrderTypeEnum.TIMING.getId())) {
            //查询省心送订单总数量
            Integer orderQuantity = orderItemQueryRepository.selectTimingOrderQuantity(orderNo);

            //查询已经配送完成数量
            Integer completedDeliveryQuantity = deliveryPlanQueryRepository.getDeliveryPlanQuantity(orderNo);

            //重新查询当前订单待配送数量
            Integer waitingDeliveryQuantity = deliveryPlanQueryRepository.getDeliveryPlanQuantityById(autoConfirmOrder.getId());

            //查询售后数量
            Integer afterSaleQuantity = afterSaleOrderQueryRepository.getAfterSaleSuccessQuantity(orderNo);

            log.info("orderNo:{}, orderQuantity:{}, autoConfirmQuantity:{}, afterSaleQuantity:{}, completedDeliveryQuantity:{}", orderNo, orderQuantity,
                    waitingDeliveryQuantity, afterSaleQuantity, completedDeliveryQuantity);
            if (completedDeliveryQuantity + waitingDeliveryQuantity + afterSaleQuantity == orderQuantity) {
                log.info("OrderCommandService[]startAutoConfirm[]orderNo:{} is completed!", orderNo);

                //确认收货修改对应表状态
                ordersCommandDomainService.orderConfirm(orderNo);

                //更新会员积分
                //updateScore(merchant, ordersEntity);

                //省心送订单退款删除
                timeCommandDomainService.deleteTimeOrderRefund(orderNo);
            }

            //更新配送状态
            commandParam = new DeliveryPlanCommandParam();
            commandParam.setId(autoConfirmOrder.getId());
            commandParam.setStatus(OrderStatusEnum.RECEIVED.getId());
            deliveryCommonDomainService.updateInfoById(commandParam);

            //首单发券奖励  好友推荐奖励
            firstOrderSendCoupon(merchant, ordersEntity);
        } else if (Objects.equals(orderType, OrderTypeEnum.NORMAL.getId()) || Objects.equals(orderType, OrderTypeEnum.HELP.getId())) {
            if (ordersEntity.getTotalPrice().doubleValue() < 0) {
                log.warn("OrderCommandService[]autoConfirm[]ordersEntity totalPrice less than zero ordersEntity:{}",
                        JSON.toJSONString(ordersEntity));
                return;
            }
            //确认收货修改对应表状态
            ordersCommandDomainService.orderConfirm(orderNo);

            //更新配送状态
            commandParam = new DeliveryPlanCommandParam();
            commandParam.setId(autoConfirmOrder.getId());
            commandParam.setStatus(OrderStatusEnum.RECEIVED.getId());
            deliveryCommonDomainService.updateInfoById(commandParam);

            //更新会员积分
            //updateScore(merchant, ordersEntity);

            //普通订单确认收货根据满返规则发送满返券
            sendMarketRuleCoupon(autoOrderSendCouponDTOS, orderNo, merchant);

            //首单发券奖励  好友推荐奖励
            firstOrderSendCoupon(merchant, ordersEntity);
        }
    }

    /***
     * @author: lzh
     * @description: 普通订单确认收货根据满返规则发送满返券
     * @date: 2024/1/23 18:59
     * @param: [autoOrderSendCouponDTOS, orderNo, merchant]
     * @return: void
     **/
    private void sendMarketRuleCoupon(List<AutoOrderSendCouponDTO> autoOrderSendCouponDTOS, String orderNo, MerchantEntity merchant) {
        //查询是否有子订单，有子订单需要判断子订单是否都确认收货了，是否有过售后单
        List<OrderRelationEntity> orderRelationEntities = orderRelationQueryRepository.selectByOrderNoBatch(Lists.newArrayList(orderNo));
        String masterOrderNo = orderNo;
        boolean flag = true;
        AfterSaleOrderQueryParam param;
        if (CollectionUtils.isEmpty(orderRelationEntities)) {
            //查询售后信息
            param = new AfterSaleOrderQueryParam();
            param.setOrderNo(orderNo);
            param.setStatus(AfterSaleOrderStatusEnum.SUCCESS.getStatus());
            List<AfterSaleOrderFlatObject> afterSaleEntities = afterSaleOrderQueryRepository.selectByOrderNo(param);
            if (!CollectionUtils.isEmpty(afterSaleEntities)) {
                flag = false;
            }
        } else {
            masterOrderNo = orderRelationEntities.get(0).getMasterOrderNo();
            List<String> subOrderNos = orderRelationQueryRepository.selectOrderNoByMasterOrderNo(masterOrderNo);
            List<OrdersEntity> ordersEntities = ordersQueryRepository.batchGetOrderNos(subOrderNos);
            if (!CollectionUtils.isEmpty(ordersEntities)) {
                for (OrdersEntity subOrder : ordersEntities) {
                    String subOrderNo = subOrder.getOrderNo();
                    if (Objects.equals(subOrderNo, masterOrderNo)) {
                        continue;
                    }

                    //子订单还有未确认收货的
                    if (!Objects.equals(OrderStatusEnum.RECEIVED.getId(), subOrder.getStatus())) {
                        flag = false;
                        break;
                    }

                    //查询售后信息
                    param = new AfterSaleOrderQueryParam();
                    param.setOrderNo(subOrderNo);
                    List<AfterSaleOrderFlatObject> afterSaleEntities = afterSaleOrderQueryRepository.selectByOrderNo(param);
                    if (!CollectionUtils.isEmpty(afterSaleEntities)) {
                        flag = false;
                        break;
                    }
                }
            }
        }

        //存在售后 不再满返
        if (!(Global.BIG_MERCHANT.equals(merchant.getSize())) && flag) {
            AutoOrderSendCouponDTO autoOrderSendCouponDTO = new AutoOrderSendCouponDTO();
            autoOrderSendCouponDTO.setOrderNo(orderNo);
            autoOrderSendCouponDTO.setMasterOrderNo(masterOrderNo);
            autoOrderSendCouponDTO.setMId(merchant.getMId());
            autoOrderSendCouponDTO.setOpenId(merchant.getOpenid());
            autoOrderSendCouponDTOS.add(autoOrderSendCouponDTO);
        }
    }

    /***
     * @author: lzh
     * @description: 首单发券奖励 好友推荐奖励
     * @date: 2024/1/23 17:05
     * @param: [merchant, ordersEntity]
     * @return: void
     **/
    private void firstOrderSendCoupon(MerchantEntity merchant, OrdersEntity ordersEntity) {
        if (!OrderTypeEnum.isAllowInviteFirstOrderType(ordersEntity.getType())) {
            log.warn("OrderCommandService[]autoConfirm[]firstOrderSendCoupon[]isAllowInviteFirstOrderType[]ordersEntity:{}",
                    JSON.toJSONString(ordersEntity));
            return;
        }

        //大客户或者非首单不发放奖励
        int isNewHand = ordersQueryRepository.selectCountByMId(merchant.getMId());
        if (Global.BIG_MERCHANT.equals(merchant.getSize()) || isNewHand > 1) {
            return;
        }

        if (StringUtils.isNotBlank(merchant.getInviterChannelCode())) {
            MerchantQueryParam param = new MerchantQueryParam();
            param.setChannelCode(merchant.getInviterChannelCode());
            MerchantEntity inviter = merchantQueryRepository.selectByParam(param);
            if (Objects.isNull(inviter)) {
                log.warn("OrderCommandService[]autoConfirm[]firstOrderSendCoupon[]inviter is null param:{}",
                        JSON.toJSONString(param));
                return;
            }
            if (Objects.equals(merchant.getMId(), inviter.getMId())) {
                log.warn("OrderCommandService[]autoConfirm[]firstOrderSendCoupon[]inviter is equals param:{}",
                        JSON.toJSONString(inviter));
                return;
            }

            //发放邀请新人券
            MQData mqData = new MQData(MType.INVITE_COUPON.name());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mId", inviter.getMId());
            jsonObject.put("type", "couponSendMsg200");
            jsonObject.put("money", "0");
            jsonObject.put("triggerTime", BaseDateUtils.localDateTimeToString(LocalDateTime.now()));
            jsonObject.put("senderType",  CouponSenderSetupSenderTypeEnum.RECOMMENDED_ORDER);
            jsonObject.put("areaNo", inviter.getAreaNo());
            mqData.setData(jsonObject);
            mqOperator.sendDataToQueue(RocketMqMessageConstant.MANAGE_LIST,null, JSON.toJSONString(mqData));
        }
    }

    @Override
    public void deleteOrderFullReturnCoupon(String orderNo, String sku, Long mId) {
        log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]start[]order:{}, sku:{}", orderNo, sku);
        OrderItemQueryParam param = new OrderItemQueryParam();
        param.setOrderNo(orderNo);
        param.setSku(sku);
        List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.selectByCondition(param);
        if (CollectionUtils.isEmpty(orderItemEntities)) {
            log.error("OrderCommandService[]deleteOrderFullReturnCoupon[]orderItemEntities is empty! param:{}", JSON.toJSONString(param));
            return;
        }

        //查询当前订单满返优惠明细记录
        OrderItemEntity orderItemEntity = orderItemEntities.get(0);
        OrderItemPreferentialQueryParam orderItemPreferentialQueryParam = new OrderItemPreferentialQueryParam();
        orderItemPreferentialQueryParam.setType(OrderItemPreferentialTypeEnum.FULL_RETURN.getCode());
        orderItemPreferentialQueryParam.setOrderNo(orderNo);
        orderItemPreferentialQueryParam.setOrderItemId(orderItemEntity.getId());
        List<OrderItemPreferentialEntity> orderItemPreferentialEntities = orderItemPreferentialQueryRepository.selectByCondition(orderItemPreferentialQueryParam);
        if (CollectionUtils.isEmpty(orderItemPreferentialEntities)) {
            log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]orderItemPreferentialEntities is empty! param:{}", JSON.toJSONString(param));
            return;
        }


        //查询满返记录状态是否已撤销
        OrderRelationQueryParam orderRelationQueryParam = new OrderRelationQueryParam();
        orderRelationQueryParam.setOrderNo(orderNo);
        List<OrderRelationEntity> orderRelationEntities = orderRelationQueryRepository.selectByCondition(orderRelationQueryParam);
        if (CollectionUtils.isEmpty(orderRelationEntities)) {
            log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]orderRelationEntities is empty! orderRelationQueryParam:{}"
                    , JSON.toJSONString(orderRelationQueryParam));
            return;
        }
        String masterOrderNo = orderRelationEntities.get(0).getMasterOrderNo();
        MarketRuleHistoryQueryParam historyQueryParam = new MarketRuleHistoryQueryParam();
        historyQueryParam.setOrderNo(masterOrderNo);
        historyQueryParam.setType(MarketRuleHistoryTypeEnum.FULL_RETURN.getCode());
        List<MarketRuleHistoryEntity> marketRuleHistoryEntities = marketRuleHistoryQueryRepository.selectByCondition(historyQueryParam);
        if (CollectionUtils.isEmpty(marketRuleHistoryEntities)) {
            log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]marketRuleHistoryEntities is empty! historyQueryParam:{}"
                    , JSON.toJSONString(historyQueryParam));
            return;
        }
        Map<Integer, List<MarketRuleHistoryEntity>> historyEntityMap = marketRuleHistoryEntities.stream()
                .collect(Collectors.groupingBy(MarketRuleHistoryEntity::getMarketRuleId));

        //判断是否支付完才返券
        for (OrderItemPreferentialEntity orderItemPreferentialEntity : orderItemPreferentialEntities) {
            String detailSnapshot = orderItemPreferentialEntity.getDiscountsDetailSnapshot();
            if (StringUtils.isBlank(detailSnapshot)) {
                log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]detailSnapshot is null! orderItemPreferentialEntity:{}",
                        JSON.toJSONString(orderItemPreferentialEntity));
                continue;
            }

            MarketRuleEntity marketRule = JSON.parseObject(detailSnapshot, MarketRuleEntity.class);
            if (Objects.equals(marketRule.getCouponRule(), MarketRuleCouponRuleEnum.PAYMENT_COMPLETION.getCode())) {
                List<MarketRuleHistoryEntity> ruleHistoryEntities = historyEntityMap.get(marketRule.getId());
                for (MarketRuleHistoryEntity marketRuleHistoryEntity : ruleHistoryEntities) {
                    //校验是否已撤回
                    if (Objects.equals(marketRuleHistoryEntity.getSendStatus(), MarketRuleHistorySendStatusEnum.WITHDRAWN.getCode())) {
                        log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]sendStatus is withdrawn! marketRuleHistoryEntity:{}",
                                JSON.toJSONString(marketRuleHistoryEntity));
                        continue;
                    }

                    //校验是否已发放
                    if (!Objects.equals(marketRuleHistoryEntity.getSendStatus(), MarketRuleHistorySendStatusEnum.ISSUED.getCode())) {
                        log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]sendStatus not is issued! marketRuleHistoryEntity:{}",
                                JSON.toJSONString(marketRuleHistoryEntity));
                        continue;
                    }

                    //删除未使用的优惠券
                    MerchantCouponCommandParam merchantCouponCommandParam = new MerchantCouponCommandParam();
                    merchantCouponCommandParam.setCouponId(orderItemPreferentialEntity.getRelatedId().intValue());
                    merchantCouponCommandParam.setMId(mId);
                    merchantCouponCommandParam.setReceiveType(CouponReceiveTypeEnum.FULL_RETURN.getCode());
                    merchantCouponCommandParam.setRelatedId(marketRuleHistoryEntity.getId().longValue());
                    merchantCouponCommandParam.setUsed(CommonStatus.NO.getCode());
                    int i = merchantCouponCommandDomainService.deleteMerchantCoupon(merchantCouponCommandParam);
                    log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]deleteMerchantCoupon[]merchantCouponCommandParam:{}, i:{}"
                            , JSON.toJSONString(merchantCouponCommandParam), i);

                    //修改满返记录状态为已撤回
                    if (i <= 0) {
                        log.info("OrderCommandService[]deleteOrderFullReturnCoupon[]deleteMerchantCoupon[]fail! merchantCouponCommandParam:{}"
                                , JSON.toJSONString(merchantCouponCommandParam));
                        continue;
                    }
                    MarketRuleHistoryCommandParam historyCommandParam = new MarketRuleHistoryCommandParam();
                    historyCommandParam.setId(marketRuleHistoryEntity.getId());
                    historyCommandParam.setSendStatus(MarketRuleHistorySendStatusEnum.WITHDRAWN.getCode());
                    marketRuleHistoryCommandDomainService.update(historyCommandParam);
                }
            }
        }
    }

    /***
     * @author: lzh
     * @description: 确认收货-更新会员积分及等级
     * @date: 2024/1/23 15:38
     * @param: [mId, ordersEntity]
     * @return: void
     **/
    private void updateScore(MerchantEntity merchant, OrdersEntity ordersEntity) {
        MerchantCommandParam merchantCommandParam;
        OrdersQueryParam ordersQueryParam;
        BigDecimal newAmount = ordersEntity.getTotalPrice().add(merchant.getMemberIntegral() == null ?
                BigDecimal.valueOf(0) : merchant.getMemberIntegral());

        //普通用户更新用户等级 查询月消费额度对比当前确认收货的订单总金额
        Integer grade = null;
        if (!Global.BIG_MERCHANT.equals(merchant.getSize())) {
            LocalDateTime startTime = Global.getMonthStartTime(LocalDateTime.now()).
                    with(TemporalAdjusters.firstDayOfMonth()).minusDays(1);
            LocalDateTime endTime = startTime.plusDays(1).with(TemporalAdjusters.lastDayOfMonth());
            ordersQueryParam = new OrdersQueryParam();
            ordersQueryParam.setMId(merchant.getMId());
            ordersQueryParam.setStartTime(startTime);
            ordersQueryParam.setEndTime(endTime);
            BigDecimal lastAmount = ordersQueryRepository.selectTotalPriceByMonth(ordersQueryParam);
            lastAmount = lastAmount == null ? BigDecimal.ZERO : lastAmount;
            if (newAmount.compareTo(lastAmount) == 1) {
                Area area = areaQueryRepository.selectByAreaNo(merchant.getAreaNo());
                List<MemberDTO> memberVOS = JSON.parseArray(area.getMemberRule(), MemberDTO.class);
                if (!CollectionUtils.isEmpty(memberVOS)) {
                    memberVOS = memberVOS.stream().sorted((o1,o2) -> o2.getGrade().compareTo(o1.getGrade()))
                            .collect(Collectors.toList());
                    for (MemberDTO memberVO: memberVOS){
                        if (newAmount.compareTo(memberVO.getThreshold()) >= 0){
                            grade =  memberVO.getGrade();
                            break;
                        }
                    }
                }
            }
        }

        //更新用户会员信息
        merchantCommandParam = new MerchantCommandParam();
        merchantCommandParam.setMemberIntegral(newAmount);
        merchantCommandParam.setMId(merchant.getMId());
        merchantCommandParam.setGrade(grade);
        merchantDomainService.updateMemberScore(merchantCommandParam);
    }

    @Override
    public void autoCompleteReceipt(String orderNoParam) {
        int pageIndex = 1;
        int pageSize = 200;
        while (true) {
            int pageStart = pageSize * (pageIndex - 1);
            List<OrdersEntity> ordersEntities = ordersQueryRepository.getPendingOrders(orderNoParam, pageStart, pageSize);
            log.info("OrderCommandService[]autoCompleteReceipt[]ordersEntities[]size:{}", ordersEntities.size());
            if (CollectionUtils.isEmpty(ordersEntities)) {
                break;
            }
            List<String> allOrderNos = ordersEntities.stream().map(OrdersEntity::getOrderNo).collect(Collectors.toList());

            List<String> normalOrderNos = ordersEntities.stream().filter(e -> !Objects.equals(e.getType(), OrderTypeEnum.TIMING.getId()))
                    .map(OrdersEntity::getOrderNo).collect(Collectors.toList());


            //获取当前订单的所有配送信息
            List<DeliveryPlanFlatObject> deliveryPlanFlatObjects = deliveryPlanQueryRepository.getWaitingDeliveryPlanQuantity(allOrderNos);
            Map<String, List<DeliveryPlanFlatObject>> allDeliveryPlanMap = deliveryPlanFlatObjects.stream().collect(Collectors.groupingBy(DeliveryPlanFlatObject::getOrderNo));

            //普通订单 未到货售后信息
            Map<String, List<AfterSaleOrderEntity>> normalAfterSaleMap = null;
            if (!CollectionUtils.isEmpty(normalOrderNos)) {
                List<AfterSaleOrderEntity> afterSaleOrderEntities = afterSaleOrderQueryRepository.getAfterSaleInfoByOrderNos(normalOrderNos);
                normalAfterSaleMap = afterSaleOrderEntities.stream().collect(Collectors.groupingBy(AfterSaleOrderEntity::getOrderNo));
            }

            //订单获取订单项信息
            Map<String, List<OrderItemEntity>> allOrderItemMap = null;
            if (!CollectionUtils.isEmpty(allOrderNos)) {
                List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.selectBatchTimingOrderQuantity(allOrderNos);
                allOrderItemMap = orderItemEntities.stream().collect(Collectors.groupingBy(OrderItemEntity::getOrderNo));
            }

            OrdersCommandParam ordersCommandParam;
            for (OrdersEntity ordersEntity : ordersEntities) {
                String orderNo = ordersEntity.getOrderNo();

                try {
                    //省心送确认收货 查询配送数量 + 售后成功数量 = 下单数量
                    if (Objects.equals(ordersEntity.getType(), OrderTypeEnum.TIMING.getId())) {

                        //查询省心送订单总数量
                        if (CollectionUtils.isEmpty(allOrderItemMap) || !allOrderItemMap.containsKey(orderNo)) {
                            log.info("OrderCommandService[]autoCompleteReceipt[]allOrderItemMap is null orderNo:{}", orderNo);
                            continue;
                        }
                        int orderQuantity = allOrderItemMap.get(orderNo).stream().mapToInt(OrderItemEntity::getAmount).sum();

                        //查询已经配送完成数量
                        if (CollectionUtils.isEmpty(allDeliveryPlanMap) || !allDeliveryPlanMap.containsKey(orderNo)) {
                            log.info("OrderCommandService[]autoCompleteReceipt[]allDeliveryPlanMap is null orderNo:{}", orderNo);
                            continue;
                        }
                        int deliveryQuantity = allDeliveryPlanMap.get(orderNo).stream().filter(e -> Objects.equals(OrderStatusEnum.RECEIVED.getId(), e.getStatus()))
                                .mapToInt(DeliveryPlanFlatObject::getQuantity).sum();

                        //查询售后数量
                        Integer afterSaleQuantity = afterSaleOrderQueryRepository.getAfterSaleSuccessQuantity(orderNo);
                        log.info("OrderCommandService[]autoCompleteReceipt[]orderNo:{}, orderQuantity:{}, afterSaleQuantity:{}, deliveryQuantity:{}",
                                orderNo, orderQuantity, afterSaleQuantity, deliveryQuantity);
                        if (deliveryQuantity + afterSaleQuantity == orderQuantity) {
                            log.info("OrderCommandService[]autoCompleteReceipt[]orderNo:{}", orderNo);

                            //确认收货修改对应表状态
                            ordersCommandDomainService.orderConfirm(orderNo);

                            //省心送订单退款删除
                            timeCommandDomainService.deleteTimeOrderRefund(orderNo);
                        }
                    } else {
                        //存在待配送计划不确认收货
                        if (!CollectionUtils.isEmpty(allOrderItemMap) && allOrderItemMap.containsKey(orderNo)) {
                            List<OrderItemEntity> orderItemEntities = allOrderItemMap.get(orderNo);
                            boolean anyMatch = orderItemEntities.stream().anyMatch(e ->  Objects.equals(OrderStatusEnum.DELIVERING.getId(), e.getStatus()));
                            if (anyMatch) {
                                log.info("OrderCommandService[]autoCompleteReceipt[]exist delivery product orderNo:{}, orderItemEntities:{}",
                                        orderNo, JSON.toJSONString(orderItemEntities));
                                continue;
                            }
                        }

                        //是否存在未到货完成售后信息 不存在就不确认收货反之
                        if (CollectionUtils.isEmpty(normalAfterSaleMap) || !normalAfterSaleMap.containsKey(orderNo)) {
                            log.info("OrderCommandService[]autoCompleteReceipt[]exist delivery product orderNo:{}", orderNo);
                            continue;
                        }

                        List<AfterSaleOrderEntity> saleOrderEntityList = normalAfterSaleMap.get(orderNo);
                        boolean review = saleOrderEntityList.stream().anyMatch(e -> !Objects.equals(e.getStatus(), AfterSaleOrderStatusEnum.SUCCESS.getStatus()));
                        if (review) {
                            log.info("OrderCommandService[]autoCompleteReceipt[]to be reviewed orderNo:{}", orderNo);
                            continue;
                        }

                        //确认收货修改对应表状态
                        log.info("OrderCommandService[]autoCompleteReceipt[]orderNo:{}", orderNo);
                        ordersCommandParam = new OrdersCommandParam();
                        ordersCommandParam.setOrderId(ordersEntity.getOrderId());
                        ordersCommandParam.setStatus(OrderStatusEnum.RECEIVED.getId());
                        ordersCommandDomainService.update(ordersCommandParam);
                    }
                } catch (Exception e) {
                    log.warn("OrderCommandService[]autoCompleteReceipt[]error! orderNo:{}, cause:{}", orderNo, e);
                    continue;
                }
            }
            if(ordersEntities.size() < pageSize){
                log.info("OrderCommandService[]autoCompleteReceipt[]pageIndex:{}", pageIndex);
                break;
            }
            pageIndex++;
        }
    }
}
