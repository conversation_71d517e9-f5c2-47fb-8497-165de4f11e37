package net.summerfarm.manage.application.inbound.provider.area.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.client.resp.area.AreaSimpleResp;
import net.summerfarm.manage.application.service.area.dto.AreaSimpleDTO;

/**
 * @author: <EMAIL>
 * @create: 2023/12/11
 */
public class AreaConverter {


    private AreaConverter() {
        // 无需实现
    }

    public static List<AreaSimpleResp> toAreaSimpleRespList(List<AreaSimpleDTO> areaSimpleDTOList) {
        if (areaSimpleDTOList == null) {
            return Collections.emptyList();
        }
        List<AreaSimpleResp> areaSimpleRespList = new ArrayList<>();
        for (AreaSimpleDTO areaSimpleDTO : areaSimpleDTOList) {
            areaSimpleRespList.add(toAreaSimpleResp(areaSimpleDTO));
        }
        return areaSimpleRespList;
    }

    public static AreaSimpleResp toAreaSimpleResp(AreaSimpleDTO areaSimpleDTO) {
        if (areaSimpleDTO == null) {
            return null;
        }
        AreaSimpleResp areaSimpleResp = new AreaSimpleResp();
        areaSimpleResp.setAreaNo(areaSimpleDTO.getAreaNo());
        areaSimpleResp.setAreaName(areaSimpleDTO.getAreaName());
        areaSimpleResp.setAreaStatus(areaSimpleDTO.getAreaStatus());
        areaSimpleResp.setLargeAreaNo(areaSimpleDTO.getLargeAreaNo());
        areaSimpleResp.setLargeAreaName(areaSimpleDTO.getLargeAreaName());
        areaSimpleResp.setLargeAreaStatus(areaSimpleDTO.getLargeAreaStatus());
        return areaSimpleResp;
    }

    public static List<AreaSimpleDTO> toAreaSimpleDTOList(List<AreaSimpleResp> areaSimpleRespList) {
        if (areaSimpleRespList == null) {
            return Collections.emptyList();
        }
        List<AreaSimpleDTO> areaSimpleDTOList = new ArrayList<>();
        for (AreaSimpleResp areaSimpleResp : areaSimpleRespList) {
            areaSimpleDTOList.add(toAreaSimpleDTO(areaSimpleResp));
        }
        return areaSimpleDTOList;
    }

    public static AreaSimpleDTO toAreaSimpleDTO(AreaSimpleResp areaSimpleResp) {
        if (areaSimpleResp == null) {
            return null;
        }
        AreaSimpleDTO areaSimpleDTO = new AreaSimpleDTO();
        areaSimpleDTO.setAreaNo(areaSimpleResp.getAreaNo());
        areaSimpleDTO.setAreaName(areaSimpleResp.getAreaName());
        areaSimpleDTO.setAreaStatus(areaSimpleResp.getAreaStatus());
        areaSimpleDTO.setLargeAreaNo(areaSimpleResp.getLargeAreaNo());
        areaSimpleDTO.setLargeAreaName(areaSimpleResp.getLargeAreaName());
        areaSimpleDTO.setLargeAreaStatus(areaSimpleResp.getLargeAreaStatus());
        return areaSimpleDTO;
    }
}
