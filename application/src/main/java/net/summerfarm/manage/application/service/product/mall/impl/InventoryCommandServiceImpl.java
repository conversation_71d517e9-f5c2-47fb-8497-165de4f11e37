package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.RegConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.manage.application.inbound.controller.product.assembler.InventoryAssembler;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.inbound.controller.product.input.command.SkuBaseInfoCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ItemLabelVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemListVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO;
import net.summerfarm.manage.application.service.product.command.PopSkuUpdateCommand;
import net.summerfarm.manage.application.service.product.converter.*;
import net.summerfarm.manage.application.service.product.handler.PopGoodsHandler;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.summerfarm.manage.application.service.product.validator.InventoryValidator;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.enums.InventoryExtTypeEnum;
import net.summerfarm.manage.common.enums.SubTypeEnum;
import net.summerfarm.manage.common.enums.products.ProductsEnum;
import net.summerfarm.manage.common.enums.products.ProductsPropertTypeEnum;
import net.summerfarm.manage.common.enums.products.SkuQuoteTypeEnum;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.command.*;
import net.summerfarm.manage.domain.product.param.query.InventoryBindQueryParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.ProductsPropertyValueQueryParam;
import net.summerfarm.manage.domain.product.repository.*;
import net.summerfarm.manage.domain.product.service.*;
import net.summerfarm.manage.facade.market.MarketItemLabelFacade;
import net.summerfarm.manage.facade.market.dto.MarketItemLabelDTO;
import net.summerfarm.manage.facade.market.input.ItemLabelSaveInput;
import net.summerfarm.manage.facade.market.input.MarketItemLabelInsertInput;
import net.summerfarm.manage.facade.market.input.MarketItemLabelQueryLabel;
import net.summerfarm.manage.facade.wms.TenantQueryFacade;
import net.summerfarm.manage.facade.wms.dto.TenantDTO;
import net.summerfarm.repository.other.enums.CategoryTypeEnum;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminId;
import static net.summerfarm.manage.application.util.UserInfoHolder.getAdminName;
import static net.summerfarm.manage.common.constants.Global.XM_TENANTID;
import static net.summerfarm.manage.common.enums.InventoryExtTypeEnum.BROKEN_BAG;
import static net.summerfarm.manage.common.enums.InventoryExtTypeEnum.TEMPORARY_INSURANCE;

/**
 * @ClassName InventoryCommandServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 11:13 2024/5/6
 * @Version 1.0
 **/
@Slf4j
@Service
public class InventoryCommandServiceImpl implements InventoryCommandService {

    @Resource
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;

    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Resource
    private ProductsQueryRepository productsQueryRepository;

    @Resource
    private TenantQueryFacade tenantQueryFacade;

    @Resource
    private InventoryBindQueryRepository inventoryBindQueryRepository;

    @Resource
    private InventoryBindCommandDomainService inventoryBindCommandDomainService;

    @Resource
    private InventoryRecordCommandDomainService recordCommandDomainService;

    @Resource
    private InventoryCommandDomainService inventoryDomainService;

    @Resource
    private ProductsPropertyValueCommandDomainService propertyValueCommandDomainService;

    @Resource
    private CategoryQueryRepository categoryQueryRepository;

    @Resource
    private ProductLabelValueCommandDomainService labelValueCommandDomainService;

    @Resource
    private MarketItemLabelFacade marketItemLabelFacade;

    @Resource
    private InventoryValidator inventoryValidator;

    @Resource
    private PopGoodsHandler popGoodsHandler;

    @Resource
    private ProductsCommandDomainService productsCommandDomainService;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateSkuInfo(InventoryUpdateInput input) {
        inventoryValidator.checkPopSkuPermission(input.getSku());
        //体积校验
        if (StringUtils.isNotBlank(input.getVolume())) {
            if (!input.getVolume().matches(RegConstant.VOLUME_REG)) {
                throw new BizException("体积字段格式不正确");
            }
        }

        //销售属性校验
        List<ProductsPropertyValueQueryParam> params = ProductsPropertyValueConverter.inputToParam(input.getSaleValueList());
        List<String> name = productsPropertyValueQueryRepository.updateCheckValueFormat(params);
        if (!CollectionUtils.isEmpty(name)) {
            log.warn("销售属性填写格式错误:{}", name);
            throw new BizException("销售属性填写格式错误：" + name.get(0));
        }

        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(input.getSku());
        InventoryEntity old = inventoryQueryRepository.querySelectOne(queryParam);

        //审核通过不允许修改
        if (!ProductsEnum.Outdated.CREATING.getCode().equals(old.getOutdated())
                && Objects.nonNull(old.getAdminId())) {
            if (!old.getAdminId().equals(input.getAdminId())) {
                throw new BizException("代仓所属不允许修改");
            }
            List<TenantDTO> tenantRespList = tenantQueryFacade.listSaasTenant();
            List<Long> saasAdminIdList = tenantRespList.stream().map(TenantDTO::getAdminId).collect(Collectors.toList());
            // 帆台所属代仓商品不允许修改
            if (saasAdminIdList.contains(old.getAdminId().longValue())) {
                throw new BizException("帆台所属代仓商品不允许修改");
            }
        }
        // pop类型，买手必填
        if (Objects.equals(SubTypeEnum.POP.getExtType(), input.getSubType())) {
            if(SkuQuoteTypeEnum.JIN.getType().equals(input.getQuoteType()) && input.getMinAutoAfterSaleThreshold() == null) {
                throw new BizException("pop商品，按斤报价时，自动补差阈值必填");
            }
        }
        // pop类型，如果按斤报价，售后阈值必填
        if (Objects.equals(SubTypeEnum.POP.getExtType(), input.getSubType()) && Objects.isNull(input.getBuyerId())) {
            throw new BizException("pop商品，买手信息必填");
        }
        // 已上新商品不支持修改买手信息
        if (Objects.equals(SubTypeEnum.POP.getExtType(), old.getSubType()) && !ProductsEnum.Outdated.CREATING.getCode().equals(old.getOutdated())) {
            // 买手信息校验
            if (Objects.nonNull(input.getBuyerId()) && !Objects.equals(old.getBuyerId(), input.getBuyerId())) {
                log.warn("仅上新中商品支持修改买手信息 old:{}, input:{}", JSON.toJSONString(old), JSON.toJSONString(input));
                throw new BizException("仅上新中商品支持修改买手信息");
            }
        }

        Integer extType = input.getExtType();
        if (Objects.equals(TEMPORARY_INSURANCE.type(), extType) || Objects.equals(BROKEN_BAG.type(), extType)) {
            String bindSku = input.getBindSku();
            if (StringUtils.isEmpty(bindSku)) {
                throw new BizException("请选择一个常规SKU进行绑定");
            }
            InventoryBindQueryParam bindQueryParam = new InventoryBindQueryParam();
            bindQueryParam.setPdId(input.getPdId());
            bindQueryParam.setSku(input.getBindSku());
            InventoryBindEntity inventoryBind = inventoryBindQueryRepository.selectOneByCondition(bindQueryParam);
            if (inventoryBind != null && !bindSku.equals(inventoryBind.getBindSku())) {
                throw new BizException("绑定SKU不允许修改");
            }
            checkBindSku(input, false);
        }

        //记录销售属性修改
        List<InventoryRecordCommandParam> recordCommandParams = new ArrayList<>();
        List<ProductsPropertyValueEntity> valueList = productsPropertyValueQueryRepository.selectSaleValueBySku(input.getSku());
        for (ProductsPropertyValueEntity valueVO : valueList) {
            for (ProductsPropertyValueInput value : input.getSaleValueList()) {
                if (Objects.equals(valueVO.getProductsPropertyId(), value.getProductsPropertyId())) {
                    if (!Objects.equals(valueVO.getProductsPropertyValue(), value.getProductsPropertyValue())) {
                        InventoryRecordCommandParam inventoryRecord = new InventoryRecordCommandParam();
                        inventoryRecord.setChangeField(valueVO.getName());
                        inventoryRecord.setSku(input.getSku());
                        inventoryRecord.setRecorder(getAdminName());
                        inventoryRecord.setOldValue(valueVO.getProductsPropertyValue());
                        inventoryRecord.setNewValue(value.getProductsPropertyValue());
                        inventoryRecord.setNewValue(value.getProductsPropertyValue());
                        inventoryRecord.setAddtime(LocalDateTime.now());
                        recordCommandParams.add(inventoryRecord);
                    }
                    break;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(recordCommandParams)) {
            recordCommandDomainService.saveBatch(recordCommandParams);
        }

        //审核状态
        input.setAuditStatus(null);
        if (input.getAuditFlag() != null) {
            //是否能够审核
            if (!ProductsEnum.Outdated.CREATING.getCode().equals(old.getOutdated())) {
                throw new BizException("当前SKU不可审核");
            }
            input.setAuditor(getAdminId());
            input.setAuditTime(LocalDateTime.now());
            if (input.getAuditFlag()) {
                input.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                input.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                input.setRefuseReason("");
            } else {
                input.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
            }
        }
        // pop视频上传，如果传入视频和原视频不一致，则需要更新上传时间和上传用户
        if (Objects.equals(SubTypeEnum.POP.getExtType(), input.getSubType())
                && !Objects.equals(input.getVideoUrl(), old.getVideoUrl())) {
            input.setVideoUploadUser(getAdminName());
            input.setVideoUploadTime(LocalDateTime.now());
        }
        log.info("updateSpuAndSku Operator:{},:{}", getAdminName(), JSON.toJSONString(input));

        //更新
        InventoryCommandParam inventoryCommandParam = InventoryConverter.inputToParma(input);
        int rs = inventoryDomainService.updateWithNull(inventoryCommandParam);
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.UPDATE_FAILED);
        }

        //修改标签--老逻辑
        List<ProductLabelValueInput> productLabelValueVos = input.getProductLabelValueVos();
        if (!CollectionUtils.isEmpty(productLabelValueVos)) {
            ProductLabelValueCommandParam labelValueCommandParam;
            for (ProductLabelValueInput productLabelValueVo : productLabelValueVos) {
                labelValueCommandParam = ProductLabelValueConverter.inputToCommonParam(productLabelValueVo);
                labelValueCommandDomainService.update(labelValueCommandParam);
            }
        }

        //更新销售属性
        if (!CollectionUtils.isEmpty(input.getSaleValueList())) {
            List<ProductsPropertyValueCommandParam> commandParams =  ProductsPropertyValueConverter.inputToCommonParam(input.getSaleValueList());
            propertyValueCommandDomainService.addSalePropertyValue(old.getSku(), old.getPdId(), commandParams);
        }
        //更新sku规格
        updateWeight4sku(input.getSku(), input.getWeight());

        // 临保/破袋与常规SKU建立绑定关系
        if (Objects.equals(TEMPORARY_INSURANCE.type(), extType) || Objects.equals(BROKEN_BAG.type(), extType)) {
            createBindSku(input, input.getSku());
        }

        // 更新商品标签--仅有有效中sku才可以编辑（目前商品中心只同步了有效中sku）
        if (StringUtils.isNotBlank(input.getItemLabel()) && Objects.equals(old.getOutdated(), ProductsEnum.Outdated.VALID.getCode())) {
            updateLabel(input);
        }
    }

    @Override
    public void updateSkuVideo(InventoryVideoInput input) {
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(input.getSku());
        InventoryEntity old = inventoryQueryRepository.querySelectOne(queryParam);
        if(old == null){
            throw new BizException ("sku不存在！");
        }
        input.setVideoUploadUser (getAdminName());
        //更新
        InventoryCommandParam inventoryCommandParam = InventoryConverter.videoInputToParma(input);
        int rs = inventoryDomainService.update(inventoryCommandParam);
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.UPDATE_FAILED);
        }
    }

    @Override
    public PageInfo<MarketItemListVO> pageSku(MarketBaseQueryInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            input.setPageIndex(1);
            input.setPageSize(10);
        }
        ProductPageQueryParam param = new ProductPageQueryParam ();
        param.setSku (input.getItemCode ());
        param.setBuyerId (input.getBuyerId ());
        param.setName (input.getSpuTitleLike ());
        param.setPageIndex (input.getPageIndex ());
        param.setPageSize (input.getPageSize ());
        param.setSubType (SubTypeEnum.POP.getExtType ());
        //先处理类目id,前端传过来的类目id,可能是1,2,3级都包含
        if (param != null && CollectionUtil.isNotEmpty(input.getCategoryIds())) {
            List<CategoryLevelEntity> categoryLevelList = categoryQueryRepository.listCategoryLevel(input.getCategoryIds());
            List<Long> categoryIds = org.apache.commons.compress.utils.Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(categoryLevelList)) {
                List<Long> firstLevelList = categoryLevelList.stream().filter(x -> x.getFirstLevel() != null).map(t -> t.getFirstLevel()).distinct()
                        .collect(Collectors.toList());
                List<Long> secondLevelList = categoryLevelList.stream().filter(x -> x.getSecondLevel() != null).map(t -> t.getSecondLevel())
                        .distinct().collect(Collectors.toList());
                List<Long> thirdLevelList = categoryLevelList.stream().filter(x -> x.getThirdLevel() != null).map(t -> t.getThirdLevel()).distinct()
                        .collect(Collectors.toList());
                categoryIds.addAll(firstLevelList);
                categoryIds.addAll(secondLevelList);
                categoryIds.addAll(thirdLevelList);
            }
            param.setCategoryIds(categoryIds);
        }
        PageInfo pageInfo = inventoryQueryRepository.listByQuery(param);

        if (pageInfo == null || CollectionUtil.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        List<MarketItemListVO> list = MarketResultConverter.INSTANCE
                .productEntityListToMarketItemListVOList(pageInfo.getList());
        pageInfo.setList (list);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePopSkuInfo(PopSkuUpdateCommand updateCommand) {
        // 校验商品信息
        String sku = updateCommand.getSku();
        InventoryEntity inventoryEntity = inventoryQueryRepository.queryBySku(sku);
        ExceptionUtil.checkAndThrow(Objects.nonNull(inventoryEntity), "商品sku不存在");
        ExceptionUtil.checkAndThrow(Objects.equals(inventoryEntity.getSubType(), SubTypeEnum.POP.getExtType()), "只支持pop商品修改");
        // 更新视频
        popGoodsHandler.handlePopVideo(updateCommand, inventoryEntity);
        // 更新图片
        ProductsEntity productsEntity = productsQueryRepository.selectById(inventoryEntity.getPdId());
        ExceptionUtil.checkAndThrow(Objects.nonNull(productsEntity), "商品spu不存在");
        popGoodsHandler.handlePopPicture(updateCommand, productsEntity);
    }

    @Override
    public void updateSkuBaseInfo(SkuBaseInfoCommandInput input) {
        inventoryValidator.checkSkuBaseInfoUpdate(input);
        InventoryCommandParam commandParam = InventoryAssembler.convert(input);
        inventoryDomainService.update(commandParam);
    }

    private void updateLabel(InventoryUpdateInput input) {
        ItemLabelSaveInput saveInput = new ItemLabelSaveInput();
        saveInput.setItemCode(input.getSku());
        saveInput.setItemLabel(input.getItemLabel());
        saveInput.setTenantId(XM_TENANTID);
        saveInput.setOutId(input.getInvId());
        marketItemLabelFacade.saveItemLabel(saveInput);
    }

    private void updateWeight4sku(String sku, String weight) {
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(sku);
        InventoryEntity old = inventoryQueryRepository.querySelectOne(queryParam);

        List<ProductsPropertyValueEntity> propertyValue = productsPropertyValueQueryRepository.listBySkuAndPdid(old.getSku(), old.getPdId());

        String weightDb = old.getWeight ();

        String newWeight = "";

        //saas侧过滤自定义属性 避免重复拼接
        if (!XM_TENANTID.equals(old.getTenantId())) {
            propertyValue = propertyValue.stream().filter(el ->
                    Objects.equals(el.getName(), Global.CUSTOM_PROPERTY_NAME)).collect(Collectors.toList());
        }
        String propertyStr;

        //过滤 "原料/成品" 属性不参与规格拼接
        if (!CollectionUtils.isEmpty(propertyValue)) {
            propertyValue = propertyValue.stream().filter(el ->
                    !Objects.equals(el.getName(), Global.FILTRATION_PROPERTY)).collect(Collectors.toList());
            propertyValue = propertyValue.stream().filter(el -> !Objects.equals(el.getName(), ProductsPropertyEnum.REUSE.getDesc())).collect(Collectors.toList());
            propertyValue = propertyValue.stream().filter(el -> !Objects.equals(el.getName(), ProductsPropertyEnum.AUTO_OUTBOUND.getDesc())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(propertyValue)) {
            throw new BizException("sku规格不能为空（不包括-原料/成品属性）！");
        }

        //水果大类的sku，销售属性：级别（productsPropertyId=14） 属性需要拼接到weight第二位
        CategoryEntity categoryEntity = categoryQueryRepository.selectInfoByPdId(old.getPdId());
        if (Objects.equals(categoryEntity.getType(), CategoryTypeEnum.FRUIT.getType())) {
            ProductsPropertyValueQueryParam productsProperty = new ProductsPropertyValueQueryParam();
            productsProperty.setName(Global.LEVEL_PROPERTY_NAME);
            productsProperty.setType(ProductsPropertTypeEnum.SALE_PROPERTY.getType());

            //获取销售属性：级别 的productsPropertyId
            ProductsPropertyEntity property = productsPropertyValueQueryRepository.selectOneConditions(productsProperty);
            Integer levelPropertyId = null;
            if (property != null) {
                levelPropertyId = property.getId();
            }

            int count = 1;
            String propertyHead = "";
            String propertyTail = "";
            for (ProductsPropertyValueEntity vo : propertyValue) {
                if (count == 1 || Objects.equals(vo.getProductsPropertyId(), levelPropertyId)) {
                    propertyHead += vo.getProductsPropertyValue() + Global.SLASH;
                    count = 0;
                    continue;
                }
                propertyTail += vo.getProductsPropertyValue() + Global.SLASH;
            }
            String propertyCombine = propertyHead + propertyTail;
            propertyStr = propertyCombine.substring(0, propertyCombine.length()-1);

        } else {
            propertyStr = propertyValue
                    .stream()
                    .map(ProductsPropertyValueEntity::getProductsPropertyValue)
                    .collect(Collectors.joining(Global.SLASH));
        }

        newWeight += propertyStr;

        //规格说明处理
        if(Objects.nonNull(weight) && weight.contains(Global.LEFT_BRACKET)){
            String weightDesc = weight.substring(weight.lastIndexOf(Global.LEFT_BRACKET));
            newWeight += weightDesc;
        }
        if(!weightDb.equals (newWeight)) {
            //更新sku信息
            InventoryCommandParam update = new InventoryCommandParam ();
            update.setSku (old.getSku ());
            update.setWeight (newWeight);
            log.info ("sku更新:{}从原规格 {} 改为:{}", old.getSku (), old.getWeight (), newWeight);
            inventoryDomainService.update (update);
        }
    }
    @Override
    public void updateWeight(InventoryEntity old, String weight) {
        if (old == null) {
            throw new BizException("更新规格的商品信息不能为空！");
        }

        List<ProductsPropertyValueEntity> propertyValue = productsPropertyValueQueryRepository.listBySkuAndPdid(old.getSku(), old.getPdId());

        //weight字段处理
        String newWeight = "";
        if (StringUtils.isEmpty(weight)) {
            weight = old.getWeight();
        }

        //saas侧过滤自定义属性 避免重复拼接
        if (!XM_TENANTID.equals(old.getTenantId())) {
            propertyValue = propertyValue.stream().filter(el ->
                    Objects.equals(el.getName(), Global.CUSTOM_PROPERTY_NAME)).collect(Collectors.toList());
        }
        String propertyStr;

        //过滤 "原料/成品" 属性不参与规格拼接
        if (!CollectionUtils.isEmpty(propertyValue)) {
            propertyValue = propertyValue.stream().filter(el ->
                    !Objects.equals(el.getName(), Global.FILTRATION_PROPERTY)).collect(Collectors.toList());
            propertyValue = propertyValue.stream().filter(el -> !Objects.equals(el.getName(), ProductsPropertyEnum.REUSE.getDesc())).collect(Collectors.toList());
            propertyValue = propertyValue.stream().filter(el -> !Objects.equals(el.getName(), ProductsPropertyEnum.AUTO_OUTBOUND.getDesc())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(propertyValue)) {
            throw new BizException("sku规格不能为空（不包括-原料/成品属性）！");
        }

        //水果大类的sku，销售属性：级别（productsPropertyId=14） 属性需要拼接到weight第二位
        CategoryEntity categoryEntity = categoryQueryRepository.selectInfoByPdId(old.getPdId());
        if (Objects.equals(categoryEntity.getType(), CategoryTypeEnum.FRUIT.getType())) {
            ProductsPropertyValueQueryParam productsProperty = new ProductsPropertyValueQueryParam();
            productsProperty.setName(Global.LEVEL_PROPERTY_NAME);
            productsProperty.setType(ProductsPropertTypeEnum.SALE_PROPERTY.getType());

            //获取销售属性：级别 的productsPropertyId
            ProductsPropertyEntity property = productsPropertyValueQueryRepository.selectOneConditions(productsProperty);
            Integer levelPropertyId = null;
            if (property != null) {
                levelPropertyId = property.getId();
            }

            int count = 1;
            String propertyHead = "";
            String propertyTail = "";
            for (ProductsPropertyValueEntity vo : propertyValue) {
                if (count == 1 || Objects.equals(vo.getProductsPropertyId(), levelPropertyId)) {
                    propertyHead += vo.getProductsPropertyValue() + Global.SLASH;
                    count = 0;
                    continue;
                }
                propertyTail += vo.getProductsPropertyValue() + Global.SLASH;
            }
            String propertyCombine = propertyHead + propertyTail;
            propertyStr = propertyCombine.substring(0, propertyCombine.length()-1);

        } else {
            propertyStr = propertyValue
                    .stream()
                    .map(ProductsPropertyValueEntity::getProductsPropertyValue)
                    .collect(Collectors.joining(Global.SLASH));
        }

        newWeight += propertyStr;

        //规格说明处理
        if(Objects.nonNull(weight) && weight.contains(Global.LEFT_BRACKET)){
            String weightDesc = weight.substring(weight.lastIndexOf(Global.LEFT_BRACKET));
            newWeight += weightDesc;
        }

        //更新sku信息
        InventoryCommandParam update = new InventoryCommandParam();
        update.setSku(old.getSku());
        update.setWeight(newWeight);
        log.info("sku更新:{}从原规格 {} 改为:{}", old.getSku(), old.getWeight(), newWeight);
        inventoryDomainService.update(update);
    }

    @Override
    public List<ItemLabelVO> allItemLabel(ItemLabelQueryInput input) {
        MarketItemLabelQueryLabel queryLabel = new MarketItemLabelQueryLabel();
        queryLabel.setLabelName(input.getLabelName());
        List<MarketItemLabelDTO> marketItemLabelDTOS = marketItemLabelFacade.allItemLabel(queryLabel);
        return InventoryConverter.marketItemLabelDTO2ItemLabelVOS(marketItemLabelDTOS);
    }

    @Override
    public ItemLabelVO insertItemLabel(ItemLabelInput input) {
        MarketItemLabelInsertInput insertInput = new MarketItemLabelInsertInput();
        insertInput.setLabelName(input.getLabelName());
        insertInput.setTenantId(XM_TENANTID);
        MarketItemLabelDTO marketItemLabelDTO = marketItemLabelFacade.insertItemLabel(insertInput);
        if (marketItemLabelDTO == null) {
            throw new BizException("创建商品标签失败");
        }
        return InventoryConverter.marketItemLabelDTO2ItemLabelVO(marketItemLabelDTO);
    }

    /**
     * 创建临保/破袋品与常规品的绑定关系
     *
     * @param inventoryReq 创建的商品信息
     * @param sku          临保或破袋品SKU
     */
    private void createBindSku(InventoryUpdateInput inventoryReq, String sku) {
        Integer extType = inventoryReq.getExtType();
        if (!Objects.equals(TEMPORARY_INSURANCE.type(), extType) && !Objects.equals(BROKEN_BAG.type(), extType)) {
            return;
        }

        String bindSku = inventoryReq.getBindSku();
        Long pdId = inventoryReq.getPdId();
        InventoryBindQueryParam param = new InventoryBindQueryParam();
        param.setPdId(pdId);
        param.setSku(sku);
        param.setBindSku(bindSku);
        InventoryBindEntity inventoryBind = inventoryBindQueryRepository.selectOneByCondition(param);
        if (inventoryBind != null) {
            log.info("当前两个sku已存在绑定关系，不继续建立绑定关系,sku:{}, bindSku:{}", sku, bindSku);
            return;
        }

        InventoryBindCommandParam inventoryCommandParam = new InventoryBindCommandParam();
        inventoryBind.setPdId(pdId);
        inventoryBind.setSku(sku);
        inventoryBind.setBindSku(bindSku);
        inventoryBindCommandDomainService.insert(inventoryCommandParam);
    }

    private void checkBindSku(InventoryUpdateInput inventoryReq, boolean isNewAdd) {
        // 校验当前是否临保、破袋商品，如果是的话，必须要选择绑定的sku
        String bindSku = inventoryReq.getBindSku();
        if (StringUtils.isEmpty(bindSku)) {
            throw new BizException("请选择同包装同销售属性组同性质的常规SKU绑定");
        }

        InventoryBindQueryParam bindQueryParam = new InventoryBindQueryParam();
        bindQueryParam.setPdId(inventoryReq.getPdId());
        bindQueryParam.setBindSku(inventoryReq.getBindSku());
        bindQueryParam.setExtType(inventoryReq.getExtType());
        InventoryBindEntity inventoryBind = inventoryBindQueryRepository.selectByBindSkuAndExtType(bindQueryParam);
        if (isNewAdd && inventoryBind != null) {
            throw new BizException("当前常规品SKU已被绑定，请重新选择常规品SKU进行绑定");
        }

        if (!isNewAdd && inventoryBind != null && !inventoryBind.getSku().equals(inventoryReq.getSku())) {
            throw new BizException("当前常规品SKU已被绑定，请重新选择常规品SKU进行绑定");
        }

        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setSku(bindSku);
        InventoryEntity inventory = inventoryQueryRepository.querySelectOne(queryParam);
        if (inventory == null) {
            throw new BizException("绑定的常规SKU不存在");
        }
        Integer extType = inventory.getExtType();
        if (extType != null && !Objects.equals(extType, InventoryExtTypeEnum.NORMAL.type())) {
            throw new BizException("绑定的SKU非常规SKU");
        }
        Integer type = inventory.getType();
        if (type != null && !Objects.equals(type, inventoryReq.getType())) {
            throw new BizException("绑定的常规SKU性质不匹配");
        }
        String unit = inventory.getUnit();
        if (!StringUtils.isEmpty(unit) && !Objects.equals(unit, inventoryReq.getUnit())) {
            throw new BizException("绑定的常规SKU包装不匹配");
        }
        Long pdId = inventoryReq.getPdId();
        ProductsPropertyValueQueryParam param = new ProductsPropertyValueQueryParam();
        param.setPdId(pdId);
        param.setType(ProductsPropertTypeEnum.SALE_PROPERTY.getType());
        param.setSkus(Lists.newArrayList(bindSku));
        List<ProductsPropertyValueEntity> skuPropertyInfoDTOS = productsPropertyValueQueryRepository.listByConditions(param);
        List<String> bindSkus = getCanBindSkus(pdId.intValue(), type, unit, inventoryReq.getExtType(), skuPropertyInfoDTOS, Lists.newArrayList(inventory));
        if (isNewAdd && CollectionUtils.isEmpty(bindSkus)) {
            throw new BizException("无可关联的常规SKU，请检查后重试");
        }

        if (!isNewAdd && inventoryBind == null && CollectionUtils.isEmpty(bindSkus)) {
            throw new BizException("无可关联的常规SKU，请检查后重试");
        }
    }

    public List<String> getCanBindSkus(Integer pdId, Integer type, String unit,
                                       Integer extType,
                                       List<ProductsPropertyValueEntity> saleValueList,
                                       List<InventoryEntity> inventories) {
        List<String> skus = inventories.stream()
                .map(InventoryEntity::getSku).collect(Collectors.toList());
        ProductsPropertyValueQueryParam param = new ProductsPropertyValueQueryParam();
        param.setPdId(pdId.longValue());
        param.setType(ProductsPropertTypeEnum.SALE_PROPERTY.getType());
        param.setSkus(skus);
        List<ProductsPropertyValueEntity> productsPropertyValues =
                productsPropertyValueQueryRepository.listByConditions(param);
        if (CollectionUtils.isEmpty(productsPropertyValues)) {
            return new ArrayList<>();
        }

        // 查询已绑定的inventory数据
        InventoryBindQueryParam bindQueryParam = new InventoryBindQueryParam();
        bindQueryParam.setPdId(pdId.longValue());
        bindQueryParam.setExtType(extType);
        List<InventoryBindEntity> inventoryBinds = inventoryBindQueryRepository.selectByPdIdAndExtType(bindQueryParam);

        // 已被绑定的sku
        Set<String> bindSkus = new HashSet<>();
        if (CollectionUtils.isNotEmpty(inventoryBinds)) {
            bindSkus = inventoryBinds.stream().map(InventoryBindEntity::getBindSku).collect(Collectors.toSet());
        }
        List<String> canBindSkus = new ArrayList<>();

        // 执行属性比较，找到完全符合条件的可绑定的sku
        Map<String, List<ProductsPropertyValueEntity>> listMap = productsPropertyValues.stream()
                .collect(Collectors.groupingBy(ProductsPropertyValueEntity::getSku));
        for (String sku : listMap.keySet()) {
            // 如果当前sku已被绑定，当前sku不能够继续被绑定
            if (bindSkus.contains(sku)) {
                continue;
            }
            // 比较包装和性质
            List<InventoryEntity> sameTypeAndUnitInv = inventories.stream()
                    .filter(item -> compareTypeAndUnit(item, type, unit))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sameTypeAndUnitInv)) {
                continue;
            }
            boolean isCanBind = true;
            List<ProductsPropertyValueEntity> productsPropertyValueEntities = listMap.get(sku);
            for (ProductsPropertyValueEntity productsPropertyValueEntity : productsPropertyValueEntities) {
                Integer productsPropertyId = productsPropertyValueEntity.getProductsPropertyId();
                // 判断是否有同一项属性配置
                Optional<ProductsPropertyValueEntity> optionalProductsPropertyValueDTO =
                        saleValueList.stream().filter(item -> Objects.equals(productsPropertyId, item.getProductsPropertyId()))
                                .findFirst();
                if (!optionalProductsPropertyValueDTO.isPresent()) {
                    isCanBind = false;
                    break;
                }
                // 判断同一项属性的属性值是否相等
                ProductsPropertyValueEntity productsPropertyValueDTO = optionalProductsPropertyValueDTO.get();
                if (!Objects.equals(productsPropertyValueDTO.getProductsPropertyValue(), productsPropertyValueEntity.getProductsPropertyValue())) {
                    isCanBind = false;
                    break;
                }
            }
            if (isCanBind) {
                canBindSkus.add(sku);
            }

        }
        return canBindSkus;
    }

    private boolean compareTypeAndUnit(InventoryEntity inventory, Integer type, String unit) {
        Integer curType = inventory.getType();
        String curUnit = inventory.getUnit();
        return Objects.equals(curType, type) && Objects.equals(curUnit, unit);
    }
}
