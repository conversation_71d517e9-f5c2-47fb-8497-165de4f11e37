package net.summerfarm.manage.application.service.product.mall.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.application.service.product.converter.AreaSkuConverter;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.enums.price.AreaSkuPriceMarkupConfigEnum;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigQueryRepository;
import net.summerfarm.manage.domain.price.service.AreaSkuPriceMarkupConfigCommandDomainService;
import net.summerfarm.manage.common.enums.CloseSaleTypeEnum;
import net.summerfarm.manage.common.enums.OpenSaleTypeEnum;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AreaSkuOnSaleParam;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuCommandRepository;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.facade.inventory.SaleInventoryCenterQueryFacade;
import net.summerfarm.manage.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.inventory.client.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AreaSkuCommandServiceImpl implements AreaSkuCommandService {

    @Resource
    private AreaSkuCommandRepository areaSkuCommandRepository;

    @Resource
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Resource
    private SaleInventoryCenterQueryFacade saleInventoryCenterQueryFacade;
    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;
    @Resource
    private AreaSkuPriceMarkupConfigQueryRepository areaSkuPriceMarkupConfigQueryRepository;
    @Resource
    private AreaSkuPriceMarkupConfigCommandDomainService areaSkuPriceMarkupConfigCommandDomainService;

    @Autowired
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;
    @Override
    public Integer updateOrAddAreaSkuPrice(AreaSkuPriceDTO areaSkuPriceDTO) {
        Integer updatedNum = areaSkuCommandRepository.updateAreaSkuPrice(AreaSkuConverter.areaSkuPriceDTO2CommandParam(areaSkuPriceDTO));

        // 如果area在需要加价的配置中
        List<Integer> popMarkupPriceAreaNos = nacosPropertiesHolder.getPopMarkupPriceAreaNos();
        List<Integer> areaNoList = areaSkuPriceDTO.getAreaNoList();
        // 初始化加价信息
        List<Integer> markupPriceAreaNoList = areaNoList.stream().filter(popMarkupPriceAreaNos::contains).collect(Collectors.toList());
        if(CollUtil.isEmpty(markupPriceAreaNoList)) {
            log.info("无需要加价的运营区域，areaSkuPriceDTO：{}, popMarkupPriceAreaNos:{}", JSON.toJSONString(areaSkuPriceDTO), JSON.toJSON(popMarkupPriceAreaNos));
            return updatedNum;
        }
        markupPriceAreaNoList.forEach(areaNo -> addConfigOrUpdatePrice(areaNo, areaSkuPriceDTO.getSku(), areaSkuPriceDTO.getPrice()));
        return updatedNum;
    }



    private void addConfigOrUpdatePrice(Integer areaNo, String sku, BigDecimal originPrice){
        AreaSkuPriceMarkupConfigQueryParam param = new AreaSkuPriceMarkupConfigQueryParam();
        param.setSku(sku);
        param.setAreaNo(areaNo);
        List<AreaSkuPriceMarkupConfigEntity> configEntities = areaSkuPriceMarkupConfigQueryRepository.selectByCondition(param);
        if(CollUtil.isEmpty(configEntities)) {
            // 新增配置，默认加价金额为0
            log.info("初始化分销商加价配置。areaNo：{}, sku:{}", areaNo, sku);
            AreaSkuPriceMarkupConfigCommandParam markupConfigCommandParam = new AreaSkuPriceMarkupConfigCommandParam();
            markupConfigCommandParam.setSku(sku);
            markupConfigCommandParam.setAreaNo(areaNo);
            markupConfigCommandParam.setMarkupType(AreaSkuPriceMarkupConfigEnum.MarkupType.AMOUNT.getCode());
            markupConfigCommandParam.setMarkupValue(BigDecimal.ZERO);
            markupConfigCommandParam.setCreator("系统默认");
            areaSkuPriceMarkupConfigCommandDomainService.insert(markupConfigCommandParam);
        } else {
            log.info("找到分销商加价配置。areaNo：{}, sku:{}", areaNo, sku);
            AreaSkuPriceMarkupConfigEntity markupConfigEntity = configEntities.get(0);
            BigDecimal markupValue = markupConfigEntity.getMarkupValue();
            if(BigDecimal.ZERO.equals(markupValue)) {
                log.info("目前加价配置为0.无需调整价格。markupConfigEntity:{}", JSON.toJSONString(markupConfigEntity));
                return ;
            }
            // 重新设置价格
            BigDecimal price = originPrice.add(markupValue);
            AreaSkuPriceCommandParam commandParam = new AreaSkuPriceCommandParam();
            commandParam.setPrice(price);
            commandParam.setSku(sku);
            commandParam.setAreaNo(markupConfigEntity.getAreaNo());
            areaSkuCommandRepository.updateAreaSkuPrice(Collections.singletonList(commandParam));
        }
    }



    @Override
    public Integer updateAreaSkuOnSale(String sku, Integer warehouseNo, boolean onsale) {
        AreaSkuOnSaleParam areaSkuOnSaleParam = new AreaSkuOnSaleParam();
        areaSkuOnSaleParam.setSku(sku);
        areaSkuOnSaleParam.setOnSale(onsale);

        // 上架查库存，因为要设置上架方式为【有库存时上架】
        if(onsale){
            WarehouseSkuInventoryDetailResDTO resDTO = saleInventoryCenterQueryFacade.queryWarehouseSkuInventory(warehouseNo, sku);
            if(resDTO != null && resDTO.getOnlineQuantity() != null && resDTO.getOnlineQuantity() > 0){
                areaSkuOnSaleParam.setOnSale(onsale);
                areaSkuOnSaleParam.setOpenSale(null);
                areaSkuOnSaleParam.setShow(1);
            }else{
                // 库存不足仍然是下架状态
                areaSkuOnSaleParam.setOnSale(false);
                areaSkuOnSaleParam.setOpenSale(1);
                areaSkuOnSaleParam.setShow(1);
            }
        } else {
            // 下架方式设置下架
            areaSkuOnSaleParam.setCloseSale(0);
        }

        return areaSkuCommandRepository.updateAreaSkuOnSale(areaSkuOnSaleParam);
    }

    @Override
    public void updateAreaSkuOnSaleBatch(String sku, Set<Integer> areaNos, boolean onsale) {
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice (Collections.singletonList (sku), new ArrayList<> (areaNos), !onsale);
        if(CollectionUtils.isEmpty (areaSkuEntities)){
            log.info ("updateAreaSkuOnSaleBatch，没有需要更新的数据,sku={}",sku);
            return;
        }

        //用围栏状态过滤areano
        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq ();

        List<WarehouseBySkuAreaNoDataReq> areaSkuReqs = new ArrayList<> ();
        WarehouseBySkuAreaNoDataReq reqData = new WarehouseBySkuAreaNoDataReq ();
        reqData.setSku (sku);
        reqData.setAreaNoList (new ArrayList<> (areaNos));
        areaSkuReqs.add (reqData);
        req.setAreaSkuList (areaSkuReqs);

        List<WarehouseBySkuAreaNoResp> resps = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo (req);
        if(CollectionUtils.isEmpty (resps)){
            log.info ("updateAreaSkuOnSaleBatch，queryBySkuAreNo null  没有需要更新的数据,sku={}",sku);
            return;
        }
        Map<Integer, Integer> areaWarhouseMap = resps.stream ()
                .collect (Collectors.toMap (
                        WarehouseBySkuAreaNoResp::getAreaNo,
                        WarehouseBySkuAreaNoResp::getWarehouseNo,
                        (existing, replacement) -> existing
                ));
        // 查询库存信息
        Map<String, Set<Integer>> collect = resps.stream ().collect (Collectors.groupingBy (WarehouseBySkuAreaNoResp::getSku, Collectors.mapping (WarehouseBySkuAreaNoResp::getWarehouseNo, Collectors.toSet ())));
        List<WarehouseSkuInventoryDetailResDTO> skuInventoryDetailResDTOS = saleInventoryCenterQueryFacade.queryWarehouseSkuInventoryList(collect);
        if(CollectionUtils.isEmpty (skuInventoryDetailResDTOS)){
            log.info ("updateAreaSkuOnSaleBatch，skuInventoryDetailResDTOS null  没有需要更新的数据,sku={}",sku);
            return;
        }
        Map<Long, Integer> warehouseStockMap = skuInventoryDetailResDTOS.stream ()
                .collect (Collectors.toMap (
                        WarehouseSkuInventoryDetailResDTO::getWarehouseNo,
                        WarehouseSkuInventoryDetailResDTO::getOnlineQuantity,
                        (existing, replacement) -> existing
                ));

        List<AreaSkuOnSaleParam> update = new ArrayList<> ();
        areaSkuEntities.forEach (item -> {
            // 上架查库存，因为要设置上架方式为【有库存时上架】
            AreaSkuOnSaleParam areaSkuOnSaleParam = new AreaSkuOnSaleParam();
            areaSkuOnSaleParam.setId (Long.valueOf (item.getId()));
            areaSkuOnSaleParam.setOnSale(onsale);
            areaSkuOnSaleParam.setCloseSale (item.getCloseSale ());
            areaSkuOnSaleParam.setOpenSale (item.getOpenSale ());
            Integer warehouseNo = areaWarhouseMap.getOrDefault (item.getAreaNo (),-999);
            Integer onlineQuantity = warehouseStockMap.get (warehouseNo.longValue ());

            if(onsale) {
                if (ObjectUtil.isNotNull (item.getOpenSale ())) {
                    if (Objects.equals (OpenSaleTypeEnum.STOCK_TURN_ON.getType (), item.getOpenSale ())) {
                        if (onlineQuantity != null && onlineQuantity > 0) {
                            areaSkuOnSaleParam.setOnSale (Boolean.TRUE);
                            areaSkuOnSaleParam.setOpenSale (null);
                            areaSkuOnSaleParam.setCloseSale(null);
                        }
                    } else if (Objects.equals (OpenSaleTypeEnum.STOCK_TURN_ON_FOREVER.getType (), item.getOpenSale ())) {
                        if (onlineQuantity != null && onlineQuantity > 0) {
                            areaSkuOnSaleParam.setOnSale (Boolean.TRUE);
                        }
                    }else{
                        return;
                    }
                } else {
                    areaSkuOnSaleParam.setCloseSale(null);
                    areaSkuOnSaleParam.setOnSale (Boolean.TRUE);
                }
            } else {
                if(Objects.equals (item.getCloseSale (),CloseSaleTypeEnum.SALE_OUT_TURN_OFF.getType ())) {
                    if (onlineQuantity != null && onlineQuantity <= 0) {
                        areaSkuOnSaleParam.setOnSale(onsale);
                        areaSkuOnSaleParam.setCloseSale (null);
                    }
                }else{
                    areaSkuOnSaleParam.setOnSale(onsale);
                }
                areaSkuOnSaleParam.setOpenSale (null);
            }
            update.add (areaSkuOnSaleParam);
        });
        if(CollectionUtils.isEmpty (update)){
            return;
        }
        areaSkuCommandRepository.updateAreaSkuOnSaleBatch(update);
    }

    @Override
    public void offSaleByAreaNos(Set<Integer> areaNos) {
        areaSkuCommandRepository.offSaleByAreaNos(areaNos);
    }
}
