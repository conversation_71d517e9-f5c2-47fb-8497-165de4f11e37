package net.summerfarm.manage.application.service.dts;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.service.product.mall.MajorPriceCommandService;
import net.summerfarm.manage.common.dto.DtsModel;
import net.summerfarm.manage.common.dto.XmPair;
import net.summerfarm.manage.common.enums.MajorDirectEnum;
import net.summerfarm.manage.common.enums.MajorPriceStatusEnum;
import net.summerfarm.manage.common.enums.MajorPriceTypeEnum;
import net.summerfarm.manage.common.enums.dts.DtsModelTypeEnum;
import net.summerfarm.manage.domain.product.service.MajorPriceCommandDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CrossBizGoodsMappingDmlImpl extends AbstractDbTableDml {

    @NacosValue(value = "${xm2pop.adminId:121}", autoRefreshed = true)
    private Integer adminId;

    @NacosValue(value = "${xm2pop.largeAreaNos:1}", autoRefreshed = true)
    private String largeAreaNos;

    @Autowired
    private MajorPriceCommandService majorPriceCommandService;

    @Autowired
    private MajorPriceCommandDomainService majorPriceCommandDomainService;
    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        List<Integer> largeAreaNoList = Splitter.on(",").splitToStream(largeAreaNos).map(Integer::valueOf).collect(Collectors.toList());

        if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.INSERT.name())) {
            Map<String, String> data = pair.getKey();
            //=1鲜沐鲜果同步到POP
            if(data.get("biz_type")!=null && Integer.parseInt (data.get("biz_type")) == 1){
                String xmSku = data.get("src_sku");
                //新增报价单
                addMajorPrice (xmSku,adminId,largeAreaNoList);
            }
        }else if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.DELETE.name())) {
            Map<String, String> old = pair.getValue ();
            //=1鲜沐鲜果同步到POP
            if(old.get("biz_type")!=null && Integer.parseInt (old.get("biz_type")) == 1){
                String xmSku = old.get("src_sku");
                //删除报价单
                deleteMajorPrice (xmSku,adminId);
            }
        }
    }

    private void deleteMajorPrice(String xmSku, Integer adminId) {
        majorPriceCommandDomainService.deleteBySku (xmSku,adminId,MajorDirectEnum.PERIOD.getType ());
    }

    private void addMajorPrice(String xmSku, Integer adminId, List<Integer> largeAreaNoList) {
        List<MajorPriceInput> inputs = largeAreaNoList.stream ().map (largeAreaNo -> {
            MajorPriceInput majorPriceInput = new MajorPriceInput ();
            majorPriceInput.setSku (xmSku);
            majorPriceInput.setStatus (MajorPriceStatusEnum.COMMIT.getType ());
            majorPriceInput.setLargeAreaNo (largeAreaNo);
            majorPriceInput.setAdminId (adminId);
            majorPriceInput.setDirect (MajorDirectEnum.PERIOD.getType ());
            majorPriceInput.setValidTime (LocalDateTime.now ());
            majorPriceInput.setInvalidTime (LocalDateTime.now ().plusYears (10));
            majorPriceInput.setMallShow (1);
            majorPriceInput.setPriceType (MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ());
            majorPriceInput.setInterestRate (BigDecimal.ZERO);
            return majorPriceInput;
        }).collect (Collectors.toList ());
        majorPriceCommandService.addMajorPrice (inputs);
    }
}
