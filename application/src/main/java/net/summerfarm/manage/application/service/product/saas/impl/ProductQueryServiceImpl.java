package net.summerfarm.manage.application.service.product.saas.impl;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.provider.product.saas.converter.SkuPriceInfoConverter;
import net.summerfarm.manage.application.service.product.saas.ProductQueryService;
import net.summerfarm.manage.application.service.product.saas.dto.SummerFarmSkuPriceInfoDTO;
import net.summerfarm.manage.common.query.product.SummerFarmSkuPriceInfoInput;
import net.summerfarm.manage.domain.product.entity.SkuPriceInfoEntity;
import net.summerfarm.manage.domain.product.service.ProductDomainService;
import org.springframework.stereotype.Service;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Slf4j
@Service
public class ProductQueryServiceImpl implements ProductQueryService {

    @Resource
    private ProductDomainService productDomainService;

    @Override
    public List<SummerFarmSkuPriceInfoDTO> queryAdminSkuPricingInfo(List<SummerFarmSkuPriceInfoInput> inputList) {
        List<SkuPriceInfoEntity> infoEntityList = productDomainService.queryAdminSkuPriceInfo(
                inputList);
        return SkuPriceInfoConverter.toSummerFarmSkuPriceInfoDTOList(infoEntityList);
    }
}
