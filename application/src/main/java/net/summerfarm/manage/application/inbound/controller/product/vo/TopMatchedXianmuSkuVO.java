package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2024/11/18 17:11
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopMatchedXianmuSkuVO {
    /**
     * 等级
     */
    private String rank;

    /**
     * 鲜沐sku编码
     */
    private String xianmuSkuCode;

    /**
     * 得分
     */
    private String similarityScore;

    /**
     * 匹配理由
     */
    private String matchingReason;

    /**
     * 名称
     */
    private String xianmuProductName;

    /**
     * 价格
     */
    private String xianmuSkuPrice;

    /**
     * 名称
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 主图
     */
    private String mainPicture;
}
