package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * @Date 2025/3/31 15:05
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductsPropertyMappingQueryInput {

    /**
     * 自定义属性类型 0、关键属性
     */
    @NotNull(message = "自定义属性类型不能为空")
    private Integer propertyType;

    /**
     * 映射类型：0、类目 1、spu
     */
    @NotNull(message = "映射类型不能为空")
    private Integer mappingType;

    /**
     * 类目id/pd id
     */
    @NotEmpty(message = "映射id列表不能为空")
    private List<Integer> mappingIdList;

    /**
     * 属性id
     */
    private Integer productsPropertyId;


}
