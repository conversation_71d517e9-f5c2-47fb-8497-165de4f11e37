package net.summerfarm.manage.application.inbound.controller.price.assembler;


import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigBatchUpdateInput;
import net.summerfarm.manage.application.inbound.controller.price.vo.AreaSkuPriceMarkupConfigVO;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigCommandInput;
import net.summerfarm.manage.application.inbound.controller.price.input.query.AreaSkuPriceMarkupConfigQueryInput;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;
import java.util.List;
import java.util.Collections;

import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
 @Mapper
public interface AreaSkuPriceMarkupConfigAssembler {

    AreaSkuPriceMarkupConfigAssembler INSTANCE = Mappers.getMapper(AreaSkuPriceMarkupConfigAssembler.class);

// ------------------------------- request ----------------------------
    AreaSkuPriceMarkupConfigQueryParam toAreaSkuPriceMarkupConfigQueryParam(AreaSkuPriceMarkupConfigQueryInput areaSkuPriceMarkupConfigQueryInput);

    @Named("commandInputToCommandParamForInsert")
    AreaSkuPriceMarkupConfigCommandParam buildCreateParam(AreaSkuPriceMarkupConfigCommandInput areaSkuPriceMarkupConfigCommandInput);

    @IterableMapping(qualifiedByName = "commandInputToCommandParamForInsert")
    List<AreaSkuPriceMarkupConfigCommandParam> buildCreateParamList(List<AreaSkuPriceMarkupConfigCommandInput> areaSkuPriceMarkupConfigCommandInput);

    AreaSkuPriceMarkupConfigQueryParam buildQueryParam(AreaSkuPriceMarkupConfigBatchUpdateInput input);

    AreaSkuPriceMarkupConfigCommandParam buildUpdateParam(AreaSkuPriceMarkupConfigCommandInput areaSkuPriceMarkupConfigCommandInput);


// ------------------------------- response ----------------------------

    List<AreaSkuPriceMarkupConfigVO> toAreaSkuPriceMarkupConfigVOList(List<AreaSkuPriceMarkupConfigEntity> areaSkuPriceMarkupConfigEntityList);

    AreaSkuPriceMarkupConfigVO toAreaSkuPriceMarkupConfigVO(AreaSkuPriceMarkupConfigEntity areaSkuPriceMarkupConfigEntity);

}
