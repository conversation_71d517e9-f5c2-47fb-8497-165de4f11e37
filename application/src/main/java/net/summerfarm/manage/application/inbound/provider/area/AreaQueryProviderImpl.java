package net.summerfarm.manage.application.inbound.provider.area;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.area.AreaQueryProvider;
import net.summerfarm.client.resp.area.AreaSimpleResp;
import net.summerfarm.manage.application.inbound.provider.area.converter.AreaConverter;
import net.summerfarm.manage.application.service.area.AreaQueryService;
import net.summerfarm.manage.application.service.area.dto.AreaSimpleDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/8
 */
@Slf4j
@DubboService
@Component
public class AreaQueryProviderImpl implements AreaQueryProvider {

    @Resource
    private AreaQueryService areaQueryService;

    @Override
    public DubboResponse<List<AreaSimpleResp>> batchQueryByAreaNos(List<Integer> areaNos) {
        List<AreaSimpleResp> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(areaNos)) {
            log.warn("invoke AreaQueryProvider.batchQueryByAreaNos,areaNos is empty");
            return DubboResponse.getOK(list);
        }
        List<AreaSimpleDTO> simpleDTOList = areaQueryService.batchQueryByAreaNos(areaNos);
        if (CollectionUtil.isNotEmpty(simpleDTOList)) {
            list = AreaConverter.toAreaSimpleRespList(simpleDTOList);
        }
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse<List<AreaSimpleResp>> batchQueryByLargeAreaNos(List<Integer> largeAreaNos) {
        List<AreaSimpleResp> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(largeAreaNos)) {
            log.warn("invoke AreaQueryProvider.batchQueryByLargeAreaNos,largeAreaNos is empty");
            return DubboResponse.getOK(list);
        }
        List<AreaSimpleDTO> simpleDTOList = areaQueryService.batchQueryByLargeAreaNos(largeAreaNos);
        if (CollectionUtil.isNotEmpty(simpleDTOList)) {
            list = AreaConverter.toAreaSimpleRespList(simpleDTOList);
        }
        return DubboResponse.getOK(list);
    }
}
