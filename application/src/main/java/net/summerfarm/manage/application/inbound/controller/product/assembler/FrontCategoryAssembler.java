package net.summerfarm.manage.application.inbound.controller.product.assembler;


import net.summerfarm.manage.application.inbound.controller.product.vo.FrontCategoryVO;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.command.FrontCategoryCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.input.query.FrontCategoryQueryInput;
import net.summerfarm.manage.domain.product.param.query.FrontCategoryQueryParam;
import net.summerfarm.manage.domain.product.param.command.FrontCategoryCommandParam;
import java.util.List;
import java.util.Collections;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
 @Mapper
public interface FrontCategoryAssembler {

    FrontCategoryAssembler INSTANCE = Mappers.getMapper(FrontCategoryAssembler.class);

// ------------------------------- request ----------------------------
    FrontCategoryQueryParam toFrontCategoryQueryParam(FrontCategoryQueryInput frontCategoryQueryInput);

    FrontCategoryCommandParam buildCreateParam(FrontCategoryCommandInput frontCategoryCommandInput);

    FrontCategoryCommandParam buildUpdateParam(FrontCategoryCommandInput frontCategoryCommandInput);


// ------------------------------- response ----------------------------

    List<FrontCategoryVO> toFrontCategoryVOList(List<FrontCategoryEntity> frontCategoryEntityList);

    FrontCategoryVO toFrontCategoryVO(FrontCategoryEntity frontCategoryEntity);

}
