package net.summerfarm.manage.application.service.admin.impl;


import net.summerfarm.manage.application.service.admin.AdminDataPermissionCommandService;
import net.summerfarm.manage.domain.admin.repository.AdminDataPermissionQueryRepository;
import net.summerfarm.manage.domain.admin.service.AdminDataPermissionCommandDomainService;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import net.summerfarm.manage.domain.admin.param.command.AdminDataPermissionCommandParam;
import net.summerfarm.manage.application.inbound.controller.admin.input.command.AdminDataPermissionCommandInput;
import net.summerfarm.manage.application.inbound.controller.admin.assembler.AdminDataPermissionAssembler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
*
* <AUTHOR>
* @date 2024-06-19 16:33:43
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class AdminDataPermissionCommandServiceImpl implements AdminDataPermissionCommandService {

    @Autowired
    private AdminDataPermissionCommandDomainService adminDataPermissionCommandDomainService;


    @Override
    public AdminDataPermissionEntity insert(AdminDataPermissionCommandInput input) {
        AdminDataPermissionCommandParam param = AdminDataPermissionAssembler.buildCreateParam(input);
        return adminDataPermissionCommandDomainService.insert(param);
    }


    @Override
    public int update(AdminDataPermissionCommandInput input) {
        AdminDataPermissionCommandParam param = AdminDataPermissionAssembler.buildUpdateParam(input);
        return adminDataPermissionCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return adminDataPermissionCommandDomainService.delete(id);
    }
}