package net.summerfarm.manage.application.inbound.controller.merchant.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:33:03
 */
@Data
public class MerchantCancelInsertInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空！")
    private Long mId;

    /**
     * 门店ID
     */
    @NotBlank(message = "申请原因不能为空！")
    private String remake;

    /**
     * 申请凭证-后台申请必填
     */
    @NotBlank(message = "客户确认凭证不能为空！")
    private String certificate;

    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空！")
    private String mName;
}
