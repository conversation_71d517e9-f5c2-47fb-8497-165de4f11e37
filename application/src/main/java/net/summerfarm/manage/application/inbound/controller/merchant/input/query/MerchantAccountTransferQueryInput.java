package net.summerfarm.manage.application.inbound.controller.merchant.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
@Data
public class MerchantAccountTransferQueryInput extends BasePageInput implements Serializable{
	/**
	 * 主门店名称
	 */
	private String mName;
	/**
	 * 主门店id
	 */
	private Long mId;
	/**
	 * 被迁移对么门店id集合
	 */
	private List<Long> transferMids;
	/**
	 * 记录id
	 */
	private Long id;



}