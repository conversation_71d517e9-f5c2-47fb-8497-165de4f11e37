package net.summerfarm.manage.application.service.download.majorprice;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceExcelDataInput;
import net.summerfarm.manage.application.service.major.converter.MajorPriceLogConvert;
import net.summerfarm.manage.application.service.product.mall.AreaSkuQueryService;
import net.summerfarm.manage.application.service.product.mall.MajorPriceCommandService;
import net.summerfarm.manage.common.enums.MajorDirectEnum;
import net.summerfarm.manage.common.enums.MajorPriceStatusEnum;
import net.summerfarm.manage.common.enums.MajorPriceTypeEnum;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.service.AdminDomainService;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.major.param.command.MajorPriceLogCommandParam;
import net.summerfarm.manage.domain.major.service.MajorPriceLogCommandDomainService;
import net.summerfarm.manage.domain.major.utils.PriceCalculator;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.summerfarm.manage.domain.product.repository.MajorPriceQueryRepository;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyValueQueryRepository;
import net.summerfarm.manage.domain.product.service.MajorPriceCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductDomainService;
import net.summerfarm.manage.facade.goods.ProductFacade;
import net.summerfarm.manage.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.manage.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import net.summerfarm.manage.common.enums.download.FileDownloadTypeEnum;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入商品
 */
@Component
@Slf4j
public class MajorPriceImportHandler extends DownloadCenterImportDefaultHandler<MajorPriceExcelDataInput> {

    @Autowired
    private AreaQueryRepository areaQueryRepository;

    @Resource
    private ProductFacade productFacade;

    @Autowired
    private ProductsPropertyValueQueryRepository productsPropertyValueQueryRepository;

    @Resource
    private MajorPriceQueryRepository majorPriceQueryRepository;

    @Autowired
    private AreaSkuQueryRepository areaSkuQueryRepository;

    @Autowired
    private AreaSkuQueryService areaSkuQueryService;

    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    @Resource
    private MajorPriceCommandDomainService majorPriceCommandDomainService;

    @Resource
    private MajorPriceCommandService majorPriceCommandService;

    @Autowired
    private MajorPriceLogCommandDomainService majorPriceLogCommandDomainService;

    @Autowired
    private AdminDomainService adminDomainService;

    @Autowired
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.XIANMU;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.XIANMU_MAJOR_PRICE_BY_AREA_IMPORT.getType();
    }

    @Override
    public void dealExcelData(List<MajorPriceExcelDataInput> list, DownloadCenterDataMsg downloadCenterDataMsg) {

        Set<String> areaNames = list.stream ().map (MajorPriceExcelDataInput::getAreaName).collect (Collectors.toSet ());

        Set<String> skus = list.stream ().map (MajorPriceExcelDataInput::getSku).collect (Collectors.toSet ());

        Set<Long> adminIds = list.stream ().filter (e->Objects.nonNull (e.getAdminId ())).map (e->Long.valueOf (e.getAdminId())).collect (Collectors.toSet ());

        // 查询areano
        Map<String, AreaSimpleEntity> areaMap = getAreaMap(areaNames);
        List<Integer> areaNos = null;
        if(MapUtil.isNotEmpty (areaMap) && CollectionUtil.isNotEmpty (areaNames)) {
            areaNos = areaNames.stream()
                    .filter(Objects::nonNull) // 过滤掉 null 的 areaName
                    .map(areaMap::get)
                    .filter(Objects::nonNull) // 过滤掉 areaMap 返回的 null 值
                    .map(AreaSimpleEntity::getAreaNo)
                    .collect(Collectors.toList());
        }
        //构造sku ， areano list
        Map<String, Set<Integer>> skuAreaNoMap = getSkuAreaNoMap (list,areaMap);
        // 查询货品
        Map<String, ProductSkuBaseResp> skuMap = getSkuMap (skus);
        // 查询所有sku是否常规属性
        Map<String, Boolean> skuNomarMap = getSkuNomarMap (skus);
        // 查询areasku
        Map<String, List<AreaSkuEntity>> areaSkuMap = getAreaSkuMap (skus,areaNos);
        // 查询大客户
        Map<Long, AdminEntity> admins = adminDomainService.getAdminMap(new ArrayList<> (adminIds));
        // 查询仓库
        Map<String, Integer> skuAreaWarehouseNoMap = getSkuAreaWarehouseNoMap(skuAreaNoMap);
        // 查询成本
        Map<String, ProductCostQueryResp> skuCostMap = getSkuCost(skuAreaWarehouseNoMap,skuAreaNoMap);

        // 查询重复
        List<String> repeat = new ArrayList<> ();
        log.info ("MajorPriceImportHandler - list={}",list);
        List<MajorPriceCommandParam> params = new ArrayList<> ();
        List<MajorPriceExcelDataInput> deals = new ArrayList<> ();
        Map<String, MajorPriceExcelDataInput> dealMap;
        for (MajorPriceExcelDataInput excelDataDTO : list) {
            try {
                MajorPriceCommandParam param = validExcelPropertyEmpty (excelDataDTO, areaMap, skuMap, skuNomarMap,areaSkuMap,skuAreaWarehouseNoMap,admins,repeat);
                params.add (param);
                deals.add (excelDataDTO);
            } catch (Exception e){
                log.warn ("ItemImportHandler - error",e);
                excelDataDTO.setErrorMsg (e.getMessage ());
            }
        }
        Map<String,String> failMap = new HashMap<> ();
        if(CollectionUtil.isNotEmpty (params)){
            dealMap = deals.stream().collect(Collectors.toMap(MajorPriceExcelDataInput::getKey,x -> x));

            Map<Integer, List<MajorPriceCommandParam>> adminMap = params.stream().collect(Collectors.groupingBy(MajorPriceCommandParam::getAdminId));
            adminMap.forEach ((adminId, p1) -> {
                Map<Integer, List<MajorPriceCommandParam>> directMap = p1.stream().collect(Collectors.groupingBy(MajorPriceCommandParam::getDirect));
                directMap.forEach ((direct, p2) -> {
                    //查询已经存在的报价单
                    Map<String, List<MajorPriceEntity>> dbMajorPriceMap = getDBMajorPriceMap (direct,adminId,p2);

                    Map<String, List<MajorPriceCommandParam>> skuParamMap = p2.stream().collect(Collectors.groupingBy(MajorPriceCommandParam::getSku));

                    skuParamMap.forEach ((sku, p3) -> {
                        deal(sku,skuCostMap, dbMajorPriceMap.get (sku),areaSkuMap.get (sku),skuAreaWarehouseNoMap,p3,failMap);
                    });
                });
            });
        } else {
            dealMap = null;
        }
        if(CollectionUtil.isNotEmpty (failMap)){
            failMap.forEach ((key,errormsg)->{
                MajorPriceExcelDataInput majorPriceExcelDataInput = dealMap.get (key);
                try {
                    throw new BizException (errormsg);
                } catch (Exception e){
                    log.warn ("ItemImportHandler - error",e);
                    majorPriceExcelDataInput.setErrorMsg (e.getMessage ());
                }
            });
        }
    }

    private Map<String, Set<Integer>> getSkuAreaNoMap(List<MajorPriceExcelDataInput> list, Map<String, AreaSimpleEntity> areaMap) {
        Map<String, Set<Integer>> map = new HashMap<> ();
        if(CollectionUtil.isEmpty (areaMap)){
            return map;
        }
        list.stream().filter (e-> StringUtils.isNotBlank (e.getSku ()) && StringUtils.isNotBlank(e.getAreaName ()) && areaMap.containsKey (e.getAreaName ())).forEach (e->{
            Set<Integer> areaNoList = map.getOrDefault (e.getSku (),new HashSet<> ());
            areaNoList.add (areaMap.get (e.getAreaName ()).getAreaNo ());
            map.put (e.getSku (),areaNoList);
        });
        return map;
    }


    private Map<String, Integer> getSkuAreaWarehouseNoMap(Map<String, Set<Integer>> map) {
        if(CollectionUtil.isNotEmpty (map)) {
            //用围栏状态过滤areano
            WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq ();
            List<WarehouseBySkuAreaNoDataReq> areaSkuReqs = new ArrayList<> ();
            map.forEach ((sku, areaSkuList) -> {
                WarehouseBySkuAreaNoDataReq reqData = new WarehouseBySkuAreaNoDataReq ();
                reqData.setSku (sku);
                reqData.setAreaNoList (new ArrayList<> (areaSkuList));
                areaSkuReqs.add (reqData);
            });
            req.setAreaSkuList (areaSkuReqs);
            List<WarehouseBySkuAreaNoResp> respsList = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo (req);
            if (CollectionUtil.isNotEmpty (respsList)) {
                return respsList.stream ().collect (Collectors.toMap (x -> x.getSku () + "_" + x.getAreaNo (), x -> x.getWarehouseNo (), (existing, replacement) -> existing));
            }
        }
        return Collections.emptyMap ();
    }

//    private Map<String, Integer> getSku_AreaWarehouseNoMap(String sku,Map<String, Set<Integer>> skuAreaNoMap) {
//        Map<String, List<WarehouseBySkuAreaNoResp>> warehouseBySkuAreaNoRespMap = areaSkuQueryService.getWarehouseBySkuAreaNoRespMap (skuAreaNoMap);
//        Map<String, Integer> sku_AreaWarehouseNoMap = new HashMap<>();
//        skuAreaNoMap.forEach ((s, areaNos) -> areaNos.forEach (a->{
//            List<WarehouseBySkuAreaNoResp> respsList = warehouseBySkuAreaNoRespMap.get (sku);
//            WarehouseBySkuAreaNoResp resp = respsList.stream ().filter (r -> r.getAreaNo ().equals (a)).findFirst ().orElse (null);
//            if(resp != null) {
//                sku_AreaWarehouseNoMap.put (sku + "_" + a,resp.getWarehouseNo ());
//            }
//        }));
//        return sku_AreaWarehouseNoMap;
//    }

    // 查询成本
    private Map<String, ProductCostQueryResp> getSkuCost(Map<String, Integer> sku_AreaWarehouseNoMap,Map<String, Set<Integer>> skuAreaNoMap) {
        return productCostQueryFacade.selectMapBySkuAndAreaNosAndWarehouseNoMap(skuAreaNoMap,sku_AreaWarehouseNoMap);
    }


    private void deal(String sku, Map<String, ProductCostQueryResp> skuCostMap, List<MajorPriceEntity> dbPriceList, List<AreaSkuEntity> areaSkuEntities,Map<String, Integer> sku_AreaWarehouseNoMap, List<MajorPriceCommandParam> p3, Map<String, String> failMap) {
        List<Integer> removeIds = new ArrayList<> ();
        List<MajorPriceCommandParam> updateList = new ArrayList<> ();
        List<MajorPriceCommandParam> addList = new ArrayList<> ();
        List<MajorPriceLogCommandParam> logList = new ArrayList<> ();
        for (MajorPriceCommandParam param : p3) {
            try {
                Integer areaNo = param.getAreaNo();
                LocalDateTime validTime = param.getValidTime();
                LocalDateTime invalidTime = param.getInvalidTime();

                AreaSkuEntity areaSku = areaSkuEntities.stream ().filter (a -> a.getAreaNo ().equals (param.getAreaNo ())).findFirst ().get ();

                ProductCostQueryResp productCostQueryResp = skuCostMap.get(sku + "_" + areaNo);

                if(CollectionUtil.isNotEmpty (dbPriceList)){
                    List<MajorPriceEntity> dbPrices = dbPriceList.stream ().filter (majorPriceEntity -> areaNo.equals (majorPriceEntity.getAreaNo ())).collect (Collectors.toList ());

                    if(CollectionUtil.isNotEmpty (dbPrices)){
                        List<MajorPriceEntity> isOverlapList = dbPrices.stream ().filter (e -> isOverlap (e.getValidTime (), e.getInvalidTime (), validTime,invalidTime)).collect (Collectors.toList ());
                        //如果有时间重叠的，则修改第一个 剩下的删除
                        if (CollectionUtil.isNotEmpty (isOverlapList)) {
                            MajorPriceEntity e = isOverlapList.get (0);
                            if(isOverlapList.size () > 1){
                                removeIds.addAll (isOverlapList.stream ().map (MajorPriceEntity::getId).collect (Collectors.toList ()));
                                removeIds.remove (e.getId ());
                            }
                            // 更新
                            param.setId (e.getId ());
                            majorPriceCommandService.fillPriceInfo (param,productCostQueryResp);
                            addLogEntity(param,logList,productCostQueryResp,areaSku);
                            updateList.add (param);
                        }else{
                            //如果有未来生效的则覆盖，保证未来生效的报价单只有一个，提高列表查询效率
                            List<MajorPriceEntity> dbs = dbPriceList.stream ().filter (e -> (e.getValidTime ().isAfter (LocalDateTime.now ())  && validTime.isAfter (LocalDateTime.now ())) || e.getStatus ().equals (MajorPriceStatusEnum.SAVE.getType ())).collect (Collectors.toList ());
                            if(CollectionUtil.isNotEmpty (dbs)){
                                dbs.forEach (e -> {
                                    param.setId (e.getId ());
                                    majorPriceCommandService.fillPriceInfo (param,productCostQueryResp);
                                    addLogEntity(param,logList,productCostQueryResp,areaSku);
                                    updateList.add (param);
                                });
                            }else {
                                majorPriceCommandService.fillPriceInfo (param, productCostQueryResp);
                                addLogEntity (param, logList, productCostQueryResp,areaSku);
                                addList.add (param);
                            }
                        }

                    }else{
                        majorPriceCommandService.fillPriceInfo (param,productCostQueryResp);
                        addLogEntity(param,logList,productCostQueryResp,areaSku);
                        addList.add (param);
                    }
                }else{
                    majorPriceCommandService.fillPriceInfo (param,productCostQueryResp);
                    addLogEntity(param,logList,productCostQueryResp,areaSku);
                    addList.add (param);
                }
            } catch (Exception e){
                failMap.put(param.getAdminId ()+param.getSku ()+param.getAreaName ()+(MajorDirectEnum.valueOfType (param.getDirect ()).getDesc ()),e.getMessage ());
                log.warn ("MajorPriceImportHandler - error",e);
            }
        }


        if(CollectionUtil.isNotEmpty (removeIds)){
            majorPriceCommandDomainService.removeByIds (removeIds);
        }
        if(CollectionUtil.isNotEmpty (updateList)){
            majorPriceCommandDomainService.updateBatch (updateList);
        }
        if(CollectionUtil.isNotEmpty (addList)){
            majorPriceCommandDomainService.insertBatch (addList);
        }
        if(CollectionUtil.isNotEmpty (logList)){
            majorPriceLogCommandDomainService.insertBatch (logList);
        }
    }
    private void addLogEntity(MajorPriceCommandParam param,List<MajorPriceLogCommandParam> logList,ProductCostQueryResp productCostQueryResp,AreaSkuEntity areaSkuEntity) {
        BigDecimal price = null;
        if(areaSkuEntity != null && areaSkuEntity.getPrice ()!=null &&  param.getPriceType () !=null) {
            price = PriceCalculator.calculateMajorPriceByType (param.getPrice (), areaSkuEntity.getPrice (), param.getPriceAdjustmentValue (), param.getPriceType ());
        }

        if(param.getStatus ().equals (MajorPriceStatusEnum.COMMIT.getType ())) {
            MajorPriceLogCommandParam logEntity = MajorPriceLogConvert.INSTANCE.buildMajorPriceLogCommandParam (param);
            if (!Objects.equals(param.getPriceType (), MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ())) {
                if (productCostQueryResp != null && price != null) {
                    logEntity.setInterestRate (price.subtract (productCostQueryResp.getCurrentCost ()).divide (price, 2, RoundingMode.HALF_UP));
                }
            }else{
                logEntity.setInterestRate (param.getInterestRate ());
            }
            logEntity.setPrice (price);
            logList.add (logEntity);
        }
    }
    private static boolean isOverlap(LocalDateTime start1, LocalDateTime end1, LocalDateTime start2, LocalDateTime end2) {
        return (start1.isBefore(end2) || start1.isEqual(end2)) && (end1.isAfter(start2) || end1.isEqual(start2));
    }
    private Map<String, List<AreaSkuEntity>> getAreaSkuMap(Set<String> skus,List<Integer> areaNos) {
        if(CollectionUtil.isEmpty (areaNos)){
            return Collections.emptyMap ();
        }
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice (new ArrayList<> (skus), areaNos, null);

        if(CollectionUtil.isNotEmpty (areaSkuEntities)){
            return areaSkuEntities.stream ().filter (areaSkuEntity -> areaSkuEntity.getPrice () != null).collect(Collectors.groupingBy(AreaSkuEntity::getSku));
        }
        return Collections.emptyMap ();
    }

    private Map<String, List<MajorPriceEntity>> getDBMajorPriceMap(Integer direct, Integer adminId, List<MajorPriceCommandParam> p2) {

        Set<Integer> areaNos = p2.stream ().map (MajorPriceCommandParam::getAreaNo).collect (Collectors.toSet ());

        Set<String> skus = p2.stream ().map (MajorPriceCommandParam::getSku).collect (Collectors.toSet ());

        List<MajorPriceEntity> majorPriceEntities = majorPriceQueryRepository.queryListMajorPriceWithoutTime (direct,Long.valueOf (adminId), skus, areaNos);

        if(CollectionUtil.isNotEmpty (majorPriceEntities)){
            return majorPriceEntities.stream().collect(Collectors.groupingBy(MajorPriceEntity::getSku));
        }
        return Collections.emptyMap ();
    }
    private MajorPriceCommandParam validExcelPropertyEmpty(MajorPriceExcelDataInput input, Map<String, AreaSimpleEntity> areaMap, Map<String, ProductSkuBaseResp> skuMap , Map<String, Boolean> skuNomarMap,Map<String, List<AreaSkuEntity>> areaSkuMap,Map<String, Integer> skuAreaWarehouseNoMap,Map<Long, AdminEntity> admins,List<String> repeat) {
        if (StringUtils.isBlank(input.getSku ()) || StringUtils.isBlank(input.getAreaName ()) || StringUtils.isBlank(input.getValidTime ()) || StringUtils.isBlank(input.getInvalidTime ()) || StringUtils.isBlank(input.getPriceTypeString ()) || StringUtils.isBlank(input.getStatus ())|| StringUtils.isBlank(input.getAdminId ())|| StringUtils.isBlank(input.getMallShowString ())|| StringUtils.isBlank(input.getDirectString ())) {
            throw new BizException("必填项不能为空");
        }
        if(CollectionUtil.isEmpty (admins)){
            admins = Collections.emptyMap ();
        }
        if(!admins.containsKey (Long.valueOf (input.getAdminId ()))){
            throw new BizException ("大客户不存在");
        }
        if (repeat.contains (input.getKey ())) {
            throw new BizException("数据重复");
        }else{
            repeat.add (input.getKey ());
        }
        //对时间进行转化并校验
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime validTime;
        LocalDateTime invalidTime;
        try {
            validTime = LocalDateTime.parse (input.getValidTime (), pattern);
        } catch (Exception e) {
            throw new BizException ("生效时间的格式错误，请仔细核对");
        }
        try {
            invalidTime  = LocalDateTime.parse (input.getInvalidTime (), pattern);
        } catch (Exception e) {
            throw new BizException ("截止时间的格式错误，请仔细核对");
        }
        //失效日期小于等于生效日期
        if (invalidTime.isBefore(validTime) || invalidTime.isEqual(validTime)) {
            throw new BizException("截止时间不能早于或等于生效日期");
        }
        if (validTime.isBefore(invalidTime) && invalidTime.isBefore(LocalDateTime.now())) {
            throw new BizException("生效时间要晚于现在时刻");
        }
        String mallShowString = input.getMallShowString ();
        Integer mallShow = null;
        if(mallShowString.equals ("是")){
            mallShow = 1;
        }else if(mallShowString.equals ("否")){
            mallShow = 0;
        }
        if(mallShow == null){
            throw new BizException("门店可见该商品字段错误");
        }
        AreaSimpleEntity areaSimpleEntity = areaMap.get (input.getAreaName ());
        if(areaSimpleEntity == null){
            throw new BizException("城市信息错误");
        }

        String priceTypeString = input.getPriceTypeString ();
        MajorPriceTypeEnum majorPriceTypeEnum = MajorPriceTypeEnum.fromValue (priceTypeString);
        if(majorPriceTypeEnum == null){
            throw new BizException("报价方式错误");
        }

        Integer priceType = majorPriceTypeEnum.getCode ();
        if (Objects.equals(priceType, MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ())) {
            BigDecimal interestRate = input.getInterestRate ();
            if (interestRate == null || interestRate.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException("数值不能为空或小于0");
            }
        } else if(!Objects.equals (priceType, MajorPriceTypeEnum.MALL_PRICE.getCode ())){
            BigDecimal amount = input.getAmount ();
            if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException("数值不能为空或小于0");
            }
        }

        String directString = input.getDirectString ();
        MajorDirectEnum majorDirectEnum = MajorDirectEnum.valueOfDesc (directString);
        if(majorDirectEnum == null){
            throw new BizException("报价单类型错误");
        }

        ProductSkuBaseResp skuItem = skuMap.get (input.getSku ());
        if(skuItem == null){
            throw new BizException("sku不存在");
        }


        if(Boolean.TRUE.equals (skuNomarMap.get (input.getSku ()))){
            throw new BizException("非常规sku不可报价");
        }

        if (Objects.equals(skuItem.getAgentType (), AgentTypeEnum.AGENT.getType ()) &&!Objects.equals(skuItem.getOwnerId (), input.getAdminId ())) {
            throw new BizException("不是本大客户代仓品不允许报价");
        }
        MajorPriceStatusEnum majorPriceStatusEnum = MajorPriceStatusEnum.valueOfDesc (input.getStatus ());
        if(majorPriceStatusEnum == null){
            throw new BizException("状态错误");
        }

        MajorPriceCommandParam param = buildParam (areaSimpleEntity, skuItem, majorDirectEnum.getType (), priceType, majorPriceStatusEnum.getType (), invalidTime, validTime, input);

        Optional<AreaSkuEntity> any = areaSkuMap.getOrDefault (param.getSku (), new ArrayList<> ()).stream ().filter (e -> e.getAreaNo ()!=null && e.getAreaNo ().equals (areaSimpleEntity.getAreaNo ())).findAny ();
        if((!any.isPresent ()) || any.get ().getPrice ()==null){
            throw new BizException("该城市无法报价");
        }

        Integer integer = skuAreaWarehouseNoMap.get (input.getSku() + "_" + areaSimpleEntity.getAreaNo ());
        if(integer == null) {
            throw new BizException ("当前sku:" + param.getSku() + "不在" + param.getAreaName());
        }

        return param;
    }
    private MajorPriceCommandParam buildParam(AreaSimpleEntity areaSimpleEntity,ProductSkuBaseResp skuItem, Integer direct,Integer majorPriceType,Integer status,LocalDateTime invalidTime,LocalDateTime validTime,MajorPriceExcelDataInput input) {
        MajorPriceCommandParam param = new MajorPriceCommandParam ();
        param.setSku(input.getSku ());
        String pdName = skuItem.getTitle ();
        //获取padName
        if (Objects.equals(skuItem.getAgentType (), AgentTypeEnum.AGENT.getType ())) {
            if (skuItem.getTitle () != null && skuItem.getTitle ().contains("-")) {
                String[] split = skuItem.getTitle ().split("-");
                pdName = split[0];
            }
        }
        param.setPdName(pdName);
        param.setWeight(skuItem.getSpecification ());
        param.setAreaNo(areaSimpleEntity.getAreaNo ());
        param.setAdminId(input.getAdminId ());
        param.setAreaName(areaSimpleEntity.getAreaName ());
        param.setDirect(direct);
        param.setPayMethod(input.getPayMethod ());
        param.setValidTime(validTime);
        param.setInvalidTime(invalidTime);
        param.setMallShow(input.getMallShowString ().equals ("是")?0:1);
        param.setPriceType(Objects.requireNonNull (MajorPriceTypeEnum.fromValue (input.getPriceTypeString ())).getCode ());
        param.setLargeAreaNo(areaSimpleEntity.getLargeAreaNo ());
        param.setPriceType (majorPriceType);

        if (Objects.equals(majorPriceType, MajorPriceTypeEnum.CONTRACT_PRICE_MARGIN.getCode ())) {
            if(input.getInterestRate () ==  null || (!isValid(input.getInterestRate ()))){
                throw new BizException("毛利率应该在0.01-99.99之间");
            }
            if(input.getInterestRate ().scale () > 2){
                throw new BizException("毛利率只能保留2位小数");
            }
            param.setInterestRate(input.getInterestRate ());
            if(input.getFixedPrice () != null){
                if(input.getFixedPrice ().compareTo (new  BigDecimal("0.01")) <0){
                    throw new BizException("毛利率固定值不能为空或小于0.01");
                }
                if(input.getFixedPrice ().scale () > 2){
                    throw new BizException("毛利率固定值只能保留2位小数");
                }
            }
            param.setFixedPrice(input.getFixedPrice ());
        } else if (Objects.equals(majorPriceType, MajorPriceTypeEnum.CONTRACT_PRICE_SPECIFIED.getCode ())) {
            if(input.getAmount () ==  null || input.getAmount ().compareTo (new  BigDecimal("0.01")) <0){
                throw new BizException("数值不能为空或小于0.01");
            }
            if(input.getAmount ().scale () > 2){
                throw new BizException("数值只能保留2位小数");
            }
            param.setPrice (input.getAmount ());
        } else if (!Objects.equals(majorPriceType, MajorPriceTypeEnum.MALL_PRICE.getCode ())) {
            if (Objects.equals (majorPriceType, MajorPriceTypeEnum.MALL_PRICE_ADD_RATE.getCode ()) || Objects.equals (majorPriceType, MajorPriceTypeEnum.MALL_PRICE_SUB_RATE.getCode ())){
                if (!isValid(input.getAmount ())) {
                    throw new BizException ("数值应该在0.01-99.99之间");
                }
            }else if(Objects.equals (majorPriceType, MajorPriceTypeEnum.MALL_PRICE_SUB_PRICE.getCode ()) || Objects.equals (majorPriceType, MajorPriceTypeEnum.MALL_PRICE_ADD_PRICE.getCode ())){
                if(input.getAmount ().scale () > 2){
                    throw new BizException("数值只能保留2位小数");
                }
            }
            param.setPriceAdjustmentValue (input.getAmount ());
        }
        param.setStatus(status);
        return param;
    }
    private boolean isValid(BigDecimal value) {
        BigDecimal min = new BigDecimal("0.01");
        BigDecimal max = new BigDecimal("99.99");

        return value.compareTo(min) >= 0 && value.compareTo(max) <= 0;
    }
    private Map<String, Boolean> getSkuNomarMap(Set<String> skus) {
        Map<String, Boolean> skuNomarMap = new HashMap<> ();
        if(CollectionUtil.isEmpty (skus)) {
            return skuNomarMap;
        }
        List<ProductsPropertyValueEntity> productsPropertyValueEntities = productsPropertyValueQueryRepository.selectSaleValueBySkuListAndPropertyIds (skus, Collections.singletonList (ProductDomainService.NOMAL));
        if(CollectionUtil.isNotEmpty (productsPropertyValueEntities)) {
            Map<String, List<ProductsPropertyValueEntity>> proMap = productsPropertyValueEntities.stream().collect(Collectors.groupingBy(ProductsPropertyValueEntity::getSku));

            skus.forEach (sku -> {
                skuNomarMap.put (sku,Boolean.FALSE);
                List<ProductsPropertyValueEntity> l = proMap.get (sku);
                if(CollectionUtil.isNotEmpty (l) && l.stream ().anyMatch (productsPropertyValue -> !Objects.equals (productsPropertyValue.getProductsPropertyValue (), "常规"))){
                    skuNomarMap.put (sku,Boolean.TRUE);
                }
            });
        }else{
            skus.forEach (sku -> skuNomarMap.put (sku, Boolean.FALSE));
        }
        return skuNomarMap;
    }

    private Map<String, ProductSkuBaseResp> getSkuMap(Set<String> skus) {
        if(!CollectionUtils.isEmpty (skus)) {
            List<ProductSkuBaseResp> skuList = productFacade.querySkuInfoBySkuList (new ArrayList<> (skus));
            if (!CollectionUtils.isEmpty (skuList)) {
                return skuList.stream ().collect (Collectors.toMap (ProductSkuBaseResp::getSku, Function.identity ()));
            }
        }
        return Collections.emptyMap ();
    }

    private Map<String, AreaSimpleEntity> getAreaMap(Set<String> areaNames) {
        // 查询areano
        List<AreaSimpleEntity> areaSimpleEntityList = areaQueryRepository.batchQueryByAreaNames (areaNames);
        if(CollectionUtils.isEmpty (areaSimpleEntityList)){
            return Collections.emptyMap ();
        }
        return areaSimpleEntityList.stream().collect(Collectors.toMap(AreaSimpleEntity::getAreaName,x -> x));
    }


    @Override
    protected File generateErrDataExcelFile(List<MajorPriceExcelDataInput> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        File file = new File(UUID.randomUUID().toString() + ".xlsx");
        EasyExcel.write(file, MajorPriceExcelDataInput.class).sheet().registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(errorDataList);
        return file;
    }
}
