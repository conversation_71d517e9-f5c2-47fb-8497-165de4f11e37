package net.summerfarm.manage.application.service.price;

import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigBatchUpdateInput;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigCommandInput;

import java.util.List;


/**
 * @date 2025-03-26 13:59:07
 * @version 1.0
 */
public interface AreaSkuPriceMarkupConfigCommandService {

    /**
     * @description: 新增
     * @return AreaSkuPriceMarkupConfigEntity
     **/
    AreaSkuPriceMarkupConfigEntity insert(AreaSkuPriceMarkupConfigCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(AreaSkuPriceMarkupConfigCommandInput input);



    /**
     * @description: 更新
     * @return:
     **/
    int batchUpdatePrice(AreaSkuPriceMarkupConfigBatchUpdateInput input);


    int batchInsert(List<AreaSkuPriceMarkupConfigCommandInput> inputList);




    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}