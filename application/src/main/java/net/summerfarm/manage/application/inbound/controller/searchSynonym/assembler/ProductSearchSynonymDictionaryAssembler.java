package net.summerfarm.manage.application.inbound.controller.searchSynonym.assembler;


import net.summerfarm.manage.application.inbound.controller.searchSynonym.vo.ProductSearchSynonymDictionaryVO;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.command.ProductSearchSynonymDictionaryCommandInput;
import net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query.ProductSearchSynonymDictionaryQueryInput;
import net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
public class ProductSearchSynonymDictionaryAssembler {

    private ProductSearchSynonymDictionaryAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static ProductSearchSynonymDictionaryQueryParam toProductSearchSynonymDictionaryQueryParam(ProductSearchSynonymDictionaryQueryInput productSearchSynonymDictionaryQueryInput) {
        if (productSearchSynonymDictionaryQueryInput == null) {
            return null;
        }
        ProductSearchSynonymDictionaryQueryParam productSearchSynonymDictionaryQueryParam = new ProductSearchSynonymDictionaryQueryParam();
        productSearchSynonymDictionaryQueryParam.setId(productSearchSynonymDictionaryQueryInput.getId());
        productSearchSynonymDictionaryQueryParam.setSynonymTerms(productSearchSynonymDictionaryQueryInput.getSynonymTerms());
        productSearchSynonymDictionaryQueryParam.setCreatedBy(productSearchSynonymDictionaryQueryInput.getCreatedBy());
        productSearchSynonymDictionaryQueryParam.setUpdatedBy(productSearchSynonymDictionaryQueryInput.getUpdatedBy());
        productSearchSynonymDictionaryQueryParam.setCreatedAt(productSearchSynonymDictionaryQueryInput.getCreatedAt());
        productSearchSynonymDictionaryQueryParam.setUpdatedAt(productSearchSynonymDictionaryQueryInput.getUpdatedAt());
        productSearchSynonymDictionaryQueryParam.setPageIndex(productSearchSynonymDictionaryQueryInput.getPageIndex());
        productSearchSynonymDictionaryQueryParam.setPageSize(productSearchSynonymDictionaryQueryInput.getPageSize());
        return productSearchSynonymDictionaryQueryParam;
    }





    public static ProductSearchSynonymDictionaryCommandParam buildCreateParam(ProductSearchSynonymDictionaryCommandInput productSearchSynonymDictionaryCommandInput) {
        if (productSearchSynonymDictionaryCommandInput == null) {
            return null;
        }
        ProductSearchSynonymDictionaryCommandParam productSearchSynonymDictionaryCommandParam = new ProductSearchSynonymDictionaryCommandParam();
        productSearchSynonymDictionaryCommandParam.setId(productSearchSynonymDictionaryCommandInput.getId());
        productSearchSynonymDictionaryCommandParam.setSynonymTerms(productSearchSynonymDictionaryCommandInput.getSynonymTerms());
        productSearchSynonymDictionaryCommandParam.setCreatedBy(productSearchSynonymDictionaryCommandInput.getCreatedBy());
        productSearchSynonymDictionaryCommandParam.setUpdatedBy(productSearchSynonymDictionaryCommandInput.getUpdatedBy());
        productSearchSynonymDictionaryCommandParam.setCreatedAt(productSearchSynonymDictionaryCommandInput.getCreatedAt());
        productSearchSynonymDictionaryCommandParam.setUpdatedAt(productSearchSynonymDictionaryCommandInput.getUpdatedAt());
        return productSearchSynonymDictionaryCommandParam;
    }


    public static ProductSearchSynonymDictionaryCommandParam buildUpdateParam(ProductSearchSynonymDictionaryCommandInput productSearchSynonymDictionaryCommandInput) {
        if (productSearchSynonymDictionaryCommandInput == null) {
            return null;
        }
        ProductSearchSynonymDictionaryCommandParam productSearchSynonymDictionaryCommandParam = new ProductSearchSynonymDictionaryCommandParam();
        productSearchSynonymDictionaryCommandParam.setId(productSearchSynonymDictionaryCommandInput.getId());
        productSearchSynonymDictionaryCommandParam.setSynonymTerms(productSearchSynonymDictionaryCommandInput.getSynonymTerms());
        productSearchSynonymDictionaryCommandParam.setCreatedBy(productSearchSynonymDictionaryCommandInput.getCreatedBy());
        productSearchSynonymDictionaryCommandParam.setUpdatedBy(productSearchSynonymDictionaryCommandInput.getUpdatedBy());
        productSearchSynonymDictionaryCommandParam.setCreatedAt(productSearchSynonymDictionaryCommandInput.getCreatedAt());
        productSearchSynonymDictionaryCommandParam.setUpdatedAt(productSearchSynonymDictionaryCommandInput.getUpdatedAt());
        return productSearchSynonymDictionaryCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<ProductSearchSynonymDictionaryVO> toProductSearchSynonymDictionaryVOList(List<ProductSearchSynonymDictionaryEntity> productSearchSynonymDictionaryEntityList) {
        if (productSearchSynonymDictionaryEntityList == null) {
            return Collections.emptyList();
        }
        List<ProductSearchSynonymDictionaryVO> productSearchSynonymDictionaryVOList = new ArrayList<>();
        for (ProductSearchSynonymDictionaryEntity productSearchSynonymDictionaryEntity : productSearchSynonymDictionaryEntityList) {
            productSearchSynonymDictionaryVOList.add(toProductSearchSynonymDictionaryVO(productSearchSynonymDictionaryEntity));
        }
        return productSearchSynonymDictionaryVOList;
}


   public static ProductSearchSynonymDictionaryVO toProductSearchSynonymDictionaryVO(ProductSearchSynonymDictionaryEntity productSearchSynonymDictionaryEntity) {
       if (productSearchSynonymDictionaryEntity == null) {
            return null;
       }
       ProductSearchSynonymDictionaryVO productSearchSynonymDictionaryVO = new ProductSearchSynonymDictionaryVO();
       productSearchSynonymDictionaryVO.setId(productSearchSynonymDictionaryEntity.getId());
       productSearchSynonymDictionaryVO.setSynonymTerms(productSearchSynonymDictionaryEntity.getSynonymTerms());
       productSearchSynonymDictionaryVO.setCreatedBy(productSearchSynonymDictionaryEntity.getCreatedBy());
       productSearchSynonymDictionaryVO.setUpdatedBy(productSearchSynonymDictionaryEntity.getUpdatedBy());
       productSearchSynonymDictionaryVO.setCreatedAt(productSearchSynonymDictionaryEntity.getCreatedAt());
       productSearchSynonymDictionaryVO.setUpdatedAt(productSearchSynonymDictionaryEntity.getUpdatedAt());
       return productSearchSynonymDictionaryVO;
   }

}
