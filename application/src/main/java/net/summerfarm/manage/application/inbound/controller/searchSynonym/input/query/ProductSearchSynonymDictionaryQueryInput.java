package net.summerfarm.manage.application.inbound.controller.searchSynonym.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
@Data
public class ProductSearchSynonymDictionaryQueryInput extends BasePageInput implements Serializable{
	/**
	 * 自增主键，唯一标识每条同义词记录
	 */
	private Long id;

	/**
	 * 同义词条目，存储同义词组或同义词映射关系，用于扩展搜索范围
	 */
	private String synonymTerms;

	/**
	 * 记录创建人，用于追踪谁添加了此同义词条目
	 */
	private String createdBy;

	/**
	 * 记录最后更新人，用于追踪谁最后修改了此同义词条目
	 */
	private String updatedBy;

	/**
	 * 记录创建时间，用于追踪同义词条目的添加时间
	 */
	private LocalDateTime createdAt;

	/**
	 * 记录最后更新时间，用于追踪同义词条目的最后修改时间
	 */
	private LocalDateTime updatedAt;



}