package net.summerfarm.manage.application.service.product.saas.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Data
public class SummerFarmSkuPriceInfoDTO implements Serializable {

    private static final long serialVersionUID = 8585658008640608058L;

    private Long citySupplyPriceId;

    private Long skuId;

    private BigDecimal minPrice;

    private BigDecimal maxPrice;


}
