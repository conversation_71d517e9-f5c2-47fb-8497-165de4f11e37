package net.summerfarm.manage.application.inbound.controller.marketItem;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.vo.MarketItemAiExtVO;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.query.MarketItemAiExtQueryInput;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtQueryService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Title 商品AI扩展信息表
 * @Description 商品AI扩展信息表功能模块
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 */
@RestController
@RequestMapping(value="/marketItemAiExt")
public class MarketItemAiExtController{

	@Autowired
	private MarketItemAiExtCommandService marketItemAiExtCommandService;
	@Autowired
	private MarketItemAiExtQueryService marketItemAiExtQueryService;

	@Autowired
	private InventoryQueryRepository inventoryQueryRepository;


	/**
	 * @Description 商品AI扩展信息表列表
	 * @return MarketItemAiExtVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<MarketItemAiExtVO>> getPage(@RequestBody MarketItemAiExtQueryInput input){
		PageInfo<MarketItemAiExtEntity> page = marketItemAiExtQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, MarketItemAiExtAssembler::toMarketItemAiExtVO));
	}

	/**
	* @Description 获取详情
	* @return MarketItemAiExtVO
	*/
	@PostMapping(value = "/query/detail/{id}")
	public CommonResult<MarketItemAiExtVO> detail(@PathVariable Long id){
		return CommonResult.ok(MarketItemAiExtAssembler.toMarketItemAiExtVO(marketItemAiExtQueryService.getDetail(id)));
	}


	/**
	 * @Description 新增
	 * @return MarketItemAiExtVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<MarketItemAiExtVO> insert(@RequestBody MarketItemAiExtCommandInput input) {
		return CommonResult.ok(MarketItemAiExtAssembler.toMarketItemAiExtVO(marketItemAiExtCommandService.insert(input)));
	}

	/**
	 * @Description 修改
	 * @return
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Integer> update(@RequestBody MarketItemAiExtCommandInput input){
		return CommonResult.ok(marketItemAiExtCommandService.update(input));
	}

	/**
	* @Description 删除
	* @return
	*/
	@PostMapping(value = "/upsert/delete/{id}")
	public CommonResult<Integer> delete(@PathVariable Long id){
		return CommonResult.ok(marketItemAiExtCommandService.delete(id));
	}

	/**
	 * @Description 批量初始化商品相关问题
	 * @param skus SKU编码列表，如果为空则自动查询需要处理的SKU
	 * @return 批量处理结果
	 */
	@PostMapping(value = "/init/questions")
	public CommonResult<BatchInitResult> batchInitQuestions(@RequestBody(required = false) List<String> skus) {
		return CommonResult.ok(marketItemAiExtCommandService.batchInitMarketItemQuestions(skus));
	}

	/**
	 * @Description 刷新商品相关问题（强制重新生成）
	 * @param skus SKU编码列表
	 * @return 批量处理结果
	 */
	@PostMapping(value = "/refresh/questions")
	public CommonResult<BatchInitResult> refreshQuestions(@RequestBody List<String> skus) {
		return CommonResult.ok(marketItemAiExtCommandService.refreshMarketItemQuestions(skus));
	}

	/**
	 * @Description 分页查询需要生成AI问题的SKU列表
	 * @param offset 偏移量，默认0
	 * @param limit 限制数量，默认100，最大500
	 * @return SKU列表
	 */
	@GetMapping(value = "/query/skus-need-questions")
	public CommonResult<List<String>> querySkusNeedQuestions(
			@RequestParam(defaultValue = "0") int offset,
			@RequestParam(defaultValue = "100") int limit) {
		// 限制最大查询数量，避免一次性返回过多数据
		if (limit > 500) {
			limit = 500;
		}
		return CommonResult.ok(inventoryQueryRepository.querySkusNeedAiQuestionsWithPage(offset, limit));
	}

	/**
	 * @Description 统计需要生成AI问题的SKU总数
	 * @return 总数量
	 */
	@GetMapping(value = "/count/skus-need-questions")
	public CommonResult<Integer> countSkusNeedQuestions() {
		return CommonResult.ok(inventoryQueryRepository.countSkusNeedAiQuestions());
	}

}

