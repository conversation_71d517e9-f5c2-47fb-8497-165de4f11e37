package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName MarketItemQuestionInitProcessor
 * @Description 商品相关问题初始化处理器
 * <AUTHOR>
 * @Date 2025-07-07
 * @Version 1.0
 **/
@Component
@Slf4j
public class MarketItemQuestionInitProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;

    private static final String SKUS_KEY = "skus";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("商品相关问题初始化 start :{}", LocalDateTime.now());

        try {
            if (StringUtils.isBlank(context.getInstanceParameters())) {
                log.warn("商品相关问题初始化: 参数为空");
                return new ProcessResult(false, "参数为空");
            }

            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());

            if (!jsonObject.containsKey(SKUS_KEY)) {
                log.warn("商品相关问题初始化: 未找到skus参数");
                return new ProcessResult(false, "未找到skus参数");
            }

            return processBatchSkus(jsonObject);

        } catch (Exception e) {
            log.error("商品相关问题初始化异常", e);
            return new ProcessResult(false, "初始化异常: " + e.getMessage());
        } finally {
            log.info("商品相关问题初始化 end :{}", LocalDateTime.now());
        }
    }

    /**
     * 处理批量SKU
     */
    private ProcessResult processBatchSkus(JSONObject jsonObject) {
        try {
            JSONArray skuArray = jsonObject.getJSONArray(SKUS_KEY);
            if (skuArray == null || skuArray.isEmpty()) {
                log.warn("商品相关问题初始化: SKU列表为空");
                return new ProcessResult(false, "SKU列表为空");
            }

            List<String> skus = skuArray.toJavaList(String.class);
            log.info("商品相关问题初始化: 开始批量处理, 总数量: {}", skus.size());

            BatchInitResult result = marketItemAiExtCommandService.batchInitMarketItemQuestions(skus);

            String message = String.format("批量处理完成: 总数=%d, 成功=%d, 失败=%d, 跳过=%d, 耗时=%dms, 成功率=%.2f%%",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailCount(),
                    result.getSkipCount(), result.getProcessingTimeMs(), result.getSuccessRate());

            log.info("商品相关问题初始化: {}", message);

            // 如果有失败的，返回部分成功
            if (result.getFailCount() > 0) {
                return new ProcessResult(true, message + ", 部分失败: " + result.getFailSkus());
            } else {
                return new ProcessResult(true, message);
            }

        } catch (Exception e) {
            log.error("商品相关问题初始化: 批量处理异常", e);
            return new ProcessResult(false, "批量处理异常: " + e.getMessage());
        }
    }


}
