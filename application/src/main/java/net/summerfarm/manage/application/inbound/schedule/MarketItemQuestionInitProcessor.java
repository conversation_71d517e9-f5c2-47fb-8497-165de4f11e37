package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName MarketItemQuestionInitProcessor
 * @Description 商品相关问题初始化处理器
 * <AUTHOR>
 * @Date 2025-07-07
 * @Version 1.0
 **/
@Component
@Slf4j
public class MarketItemQuestionInitProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;
    @Resource
    private InventoryQueryRepository inventoryQueryRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("商品相关问题初始化 start :{}", LocalDateTime.now());

        try {
            // 分页获取待处理的SKU问题
            log.info("商品相关问题初始化: 未指定SKU列表，将自动查询需要处理的SKU");

            int maxPageNo = 100;
            int pageNo = 1;
            Integer pageSize = 200;
            ProcessResult processResult = new ProcessResult(true, "未查询到需要处理的SKU");
            for (pageNo = 1; pageNo <= maxPageNo; pageNo++) {
                List<String> skus = inventoryQueryRepository.querySkusNeedAiQuestions(1, pageSize);
                if (CollectionUtils.isEmpty(skus)) {
                    log.info("商品相关问题初始化: 未查询到需要处理的SKU");
                    return processResult;
                }

                BatchInitResult result = marketItemAiExtCommandService.batchInitMarketItemQuestions(skus);
                String message = String.format("批量处理完成: 总数=%d, 成功=%d, 失败=%d, 跳过=%d, 耗时=%dms, 成功率=%.2f%%",
                        result.getTotalCount(), result.getSuccessCount(), result.getFailCount(),
                        result.getSkipCount(), result.getProcessingTimeMs(), result.getSuccessRate());
                log.info("商品相关问题初始化: {}", message);
                // 如果有失败的，返回部分成功
                if (result.getFailCount() == skus.size()) {
                    return new ProcessResult(true, message + ", 部分失败: " + result.getFailSkus());
                }
            }
            return processResult;
        } catch (Exception e) {
            log.error("商品相关问题初始化异常", e);
            return new ProcessResult(false, "初始化异常: " + e.getMessage());
        } finally {
            log.info("商品相关问题初始化 end :{}", LocalDateTime.now());
        }
    }
}
