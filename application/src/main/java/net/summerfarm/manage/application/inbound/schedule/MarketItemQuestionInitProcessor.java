package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @ClassName MarketItemQuestionInitProcessor
 * @Description 商品相关问题初始化处理器
 * <AUTHOR>
 * @Date 2025-07-07
 * @Version 1.0
 **/
@Component
@Slf4j
public class MarketItemQuestionInitProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;

    private static final String SKU_KEY = "sku";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("商品相关问题初始化 start :{}", LocalDateTime.now());
        
        String sku = null;
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());
            if (jsonObject.containsKey(SKU_KEY)) {
                sku = jsonObject.getString(SKU_KEY);
            }
        }
        
        if (StringUtils.isBlank(sku)) {
            log.warn("商品相关问题初始化: SKU参数为空");
            return new ProcessResult(false, "SKU参数为空");
        }
        
        try {
            boolean success = marketItemAiExtCommandService.initMarketItemQuestions(sku);
            if (success) {
                log.info("商品相关问题初始化成功, sku: {}", sku);
                return new ProcessResult(true, "初始化成功");
            } else {
                log.warn("商品相关问题初始化失败, sku: {}", sku);
                return new ProcessResult(false, "初始化失败");
            }
        } catch (Exception e) {
            log.error("商品相关问题初始化异常, sku: {}", sku, e);
            return new ProcessResult(false, "初始化异常: " + e.getMessage());
        } finally {
            log.info("商品相关问题初始化 end :{}", LocalDateTime.now());
        }
    }
}
