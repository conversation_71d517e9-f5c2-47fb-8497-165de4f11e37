package net.summerfarm.manage.application.inbound.controller.price;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.price.assembler.AreaSkuPriceMarkupConfigAssembler;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigBatchUpdateInput;
import net.summerfarm.manage.application.inbound.controller.price.vo.AllowMarkupConfigAreaVO;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.repository.AreaQueryRepository;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigCommandInput;
import net.summerfarm.manage.application.inbound.controller.price.vo.AreaSkuPriceMarkupConfigVO;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.application.inbound.controller.price.input.query.AreaSkuPriceMarkupConfigQueryInput;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.application.service.price.AreaSkuPriceMarkupConfigCommandService;
import net.summerfarm.manage.application.service.price.AreaSkuPriceMarkupConfigQueryService;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Title pop城市分销商商品加价配置表
 * @Description pop城市分销商商品加价配置表功能模块
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 */
@RestController
@RequestMapping(value="/areaSkuPriceMarkupConfig")
public class AreaSkuPriceMarkupConfigController{

	@Autowired
	private AreaSkuPriceMarkupConfigCommandService areaSkuPriceMarkupConfigCommandService;
	@Autowired
	private AreaSkuPriceMarkupConfigQueryService areaSkuPriceMarkupConfigQueryService;
	@Resource
	private AreaQueryRepository areaQueryRepository;
	@Resource
	private NacosPropertiesHolder nacosPropertiesHolder;


	/**
	 * pop城市分销商商品加价配置表列表
	 * @return AreaSkuPriceMarkupConfigVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<AreaSkuPriceMarkupConfigVO>> getPage(@RequestBody AreaSkuPriceMarkupConfigQueryInput input){
		PageInfo<AreaSkuPriceMarkupConfigEntity> page = areaSkuPriceMarkupConfigQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, AreaSkuPriceMarkupConfigAssembler.INSTANCE::toAreaSkuPriceMarkupConfigVO));
	}

	/**
	* 获取详情
	* @return AreaSkuPriceMarkupConfigVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<AreaSkuPriceMarkupConfigVO> detail(@RequestBody AreaSkuPriceMarkupConfigQueryInput input){
		if(input == null || input.getId() == null){
			throw new ParamsException("请求参数缺失:缺少id字段");
		}
		return CommonResult.ok(AreaSkuPriceMarkupConfigAssembler.INSTANCE.toAreaSkuPriceMarkupConfigVO(areaSkuPriceMarkupConfigQueryService.getDetail(input.getId())));
	}


	/**
	 * 新增
	 * @return AreaSkuPriceMarkupConfigVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<AreaSkuPriceMarkupConfigVO> insert(@RequestBody AreaSkuPriceMarkupConfigCommandInput input) {
		return CommonResult.ok(AreaSkuPriceMarkupConfigAssembler.INSTANCE.toAreaSkuPriceMarkupConfigVO(areaSkuPriceMarkupConfigCommandService.insert(input)));
	}

	/**
	 * 修改
	 * @return
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Integer> update(@Validated @RequestBody AreaSkuPriceMarkupConfigCommandInput input){
		return CommonResult.ok(areaSkuPriceMarkupConfigCommandService.update(input));
	}


	/**
	 * 批量修改金额
	 * @return
	 */
	@PostMapping(value = "/upsert/batch-update-price")
	public CommonResult<Integer> batchUpdatePrice(@Validated @RequestBody AreaSkuPriceMarkupConfigBatchUpdateInput input){
		return CommonResult.ok(areaSkuPriceMarkupConfigCommandService.batchUpdatePrice(input));
	}


	/**
	 * 批量新增
	 * @return
	 */
	@PostMapping(value = "/upsert/batch-insert")
	public CommonResult<Integer> batchInsert(@Validated @Size(max = 100) @RequestBody List<AreaSkuPriceMarkupConfigCommandInput> input){
		return CommonResult.ok(areaSkuPriceMarkupConfigCommandService.batchInsert(input));
	}

	/**
	* 删除
	* @return
	*/
	@PostMapping(value = "/upsert/delete")
	public CommonResult<Integer> delete(@RequestBody AreaSkuPriceMarkupConfigCommandInput input){
		if(input == null || input.getId() == null){
			throw new ParamsException("请求参数缺失:缺少id字段");
		}
		return CommonResult.ok(areaSkuPriceMarkupConfigCommandService.delete(input.getId()));
	}


	/**
	 * pop城市分销商开放加价的城市
	 * @return AreaSkuPriceMarkupConfigVO
	 */
	@GetMapping(value="/query/allow-markup-area")
	public CommonResult<AllowMarkupConfigAreaVO> allowMarkupArea(){
		List<Integer> popMarkupPriceAreaNos = nacosPropertiesHolder.getPopMarkupPriceAreaNos();
		AllowMarkupConfigAreaVO vo = new AllowMarkupConfigAreaVO();
		if(CollUtil.isEmpty(popMarkupPriceAreaNos)) {
			vo.setLargeAreaNo(new ArrayList<>());
			return CommonResult.ok(vo);
		}

		List<AreaSimpleEntity> areaSimpleEntities = areaQueryRepository.batchQueryByAreaNos(popMarkupPriceAreaNos);
		vo.setLargeAreaNo(areaSimpleEntities.stream().map(AreaSimpleEntity::getLargeAreaNo).distinct().collect(Collectors.toList()));
		return CommonResult.ok(vo);
	}


}

