package net.summerfarm.manage.application.service.marketItem.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 商品AI相关配置
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@Component
@ConfigurationProperties(prefix = "market-item.ai")
public class MarketItemAiConfig {
    
    /**
     * 自动查询时的分页大小
     */
    private int autoQueryPageSize = 500;
    
    /**
     * 自动查询时的最大处理数量
     */
    private int autoQueryMaxTotal = 5000;
    
    /**
     * HTTP接口查询时的最大限制
     */
    private int httpQueryMaxLimit = 500;
    
    /**
     * HTTP接口查询时的默认限制
     */
    private int httpQueryDefaultLimit = 100;
    
    /**
     * 批量处理时的批次大小
     */
    private int batchProcessSize = 50;
    
    /**
     * 已存在数据检查的批次大小
     */
    private int existCheckBatchSize = 100;
}
