package net.summerfarm.manage.application.service.marketItem.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.service.MarketItemAiExtCommandDomainService;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.product.entity.ProductBasicInfoEntity;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.ai.OpenAIChatFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MarketItemAiExtCommandServiceImpl implements MarketItemAiExtCommandService {

    @Autowired
    private MarketItemAiExtCommandDomainService marketItemAiExtCommandDomainService;

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    @Autowired
    private OpenAIChatFacade openAIChatFacade;


    @Override
    public MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildCreateParam(input);
        return marketItemAiExtCommandDomainService.insert(param);
    }


    @Override
    public int update(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildUpdateParam(input);
        return marketItemAiExtCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return marketItemAiExtCommandDomainService.delete(id);
    }

    @Override
    public boolean initMarketItemQuestions(String sku) {
        if (StringUtils.isBlank(sku)) {
            log.warn("initMarketItemQuestions: sku is blank");
            return false;
        }

        try {
            // 1. 检查是否已存在商品相关问题数据
            List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.selectBySkuAndExtType(sku, 1);
            if (!existingData.isEmpty()) {
                log.info("initMarketItemQuestions: sku {} already has related questions, skip", sku);
                return true;
            }

            // 2. 查询商品基础数据
            List<ProductBasicInfoEntity> productInfoList = inventoryQueryRepository.queryProductBasicInfoForAI(sku);
            if (productInfoList.isEmpty()) {
                log.warn("initMarketItemQuestions: no product info found for sku {}", sku);
                return false;
            }

            ProductBasicInfoEntity productInfo = productInfoList.get(0);

            // 3. 构建商品信息文本
            String productInfoText = buildProductInfoText(productInfo);

            // 4. 调用AI生成商品相关问题
            String prompt = "请根据以下商品信息，生成5-10个客户可能会询问的关于该商品的问题。问题应该涵盖商品的特性、用途、保存方法、营养价值等方面。请以JSON数组格式返回，每个元素是一个问题字符串。";
            String aiResponse = openAIChatFacade.chat(productInfoText, prompt);

            if (StringUtils.isBlank(aiResponse)) {
                log.warn("initMarketItemQuestions: AI response is blank for sku {}", sku);
                return false;
            }

            // 5. 解析AI响应并保存
            List<String> questions = parseAIResponse(aiResponse);
            if (questions.isEmpty()) {
                log.warn("initMarketItemQuestions: no questions parsed from AI response for sku {}", sku);
                return false;
            }

            // 6. 保存到数据库
            MarketItemAiExtCommandParam param = new MarketItemAiExtCommandParam();
            param.setSku(sku);
            param.setPdId(productInfo.getPdId());
            param.setExtType(1); // 商品相关问题
            param.setExtValue(JSON.toJSONString(questions));
            param.setTenantId(1L);
            param.setCreateTime(LocalDateTime.now());
            param.setUpdateTime(LocalDateTime.now());

            marketItemAiExtCommandDomainService.insert(param);
            log.info("initMarketItemQuestions: successfully saved {} questions for sku {}", questions.size(), sku);
            return true;

        } catch (Exception e) {
            log.error("initMarketItemQuestions: error processing sku {}", sku, e);
            return false;
        }
    }

    /**
     * 构建商品信息文本
     */
    private String buildProductInfoText(ProductBasicInfoEntity productInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品名称：").append(productInfo.getPdName()).append("\n");

        if (StringUtils.isNotBlank(productInfo.getSpecification())) {
            sb.append("规格：").append(productInfo.getSpecification()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getOrigin())) {
            sb.append("产地：").append(productInfo.getOrigin()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getBrandName())) {
            sb.append("品牌：").append(productInfo.getBrandName()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getFirstCategoryName())) {
            sb.append("类目：").append(productInfo.getFirstCategoryName());
            if (StringUtils.isNotBlank(productInfo.getSecondCategoryName())) {
                sb.append(" > ").append(productInfo.getSecondCategoryName());
            }
            if (StringUtils.isNotBlank(productInfo.getThirdCategoryName())) {
                sb.append(" > ").append(productInfo.getThirdCategoryName());
            }
            sb.append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getPdDetail())) {
            sb.append("商品描述：").append(productInfo.getPdDetail()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getStorageArea())) {
            sb.append("仓储区域：").append(productInfo.getStorageArea()).append("\n");
        }

        // 添加产品属性
        appendAttributeIfNotBlank(sb, "储藏温度", productInfo.getProductAttrStorageTemp());
        appendAttributeIfNotBlank(sb, "商品形态", productInfo.getProductAttrGoodsForm());
        appendAttributeIfNotBlank(sb, "成分", productInfo.getProductAttrIngredient());
        appendAttributeIfNotBlank(sb, "口味", productInfo.getProductAttrTaste());
        appendAttributeIfNotBlank(sb, "使用方法", productInfo.getProductAttrUsageMethod());

        return sb.toString();
    }

    private void appendAttributeIfNotBlank(StringBuilder sb, String label, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(label).append("：").append(value).append("\n");
        }
    }

    /**
     * 解析AI响应，提取问题列表
     */
    private List<String> parseAIResponse(String aiResponse) {
        try {
            // 尝试直接解析JSON数组
            return JSON.parseArray(aiResponse, String.class);
        } catch (Exception e) {
            log.warn("parseAIResponse: failed to parse as JSON array, trying to extract manually", e);

            // 如果JSON解析失败，尝试手动提取问题
            String[] lines = aiResponse.split("\n");
            return Arrays.stream(lines)
                    .filter(line -> line.trim().length() > 0 && (line.contains("?") || line.contains("？")))
                    .map(String::trim)
                    .filter(line -> line.length() > 5) // 过滤太短的行
                    .collect(Collectors.toList());
        }
    }
}