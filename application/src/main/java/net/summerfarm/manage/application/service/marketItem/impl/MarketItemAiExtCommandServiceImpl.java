package net.summerfarm.manage.application.service.marketItem.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.config.MarketItemAiConfig;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.service.MarketItemAiExtCommandDomainService;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.product.entity.ProductBasicInfoEntity;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.ai.OpenAIChatFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MarketItemAiExtCommandServiceImpl implements MarketItemAiExtCommandService {

    @Autowired
    private MarketItemAiExtCommandDomainService marketItemAiExtCommandDomainService;

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    @Autowired
    private OpenAIChatFacade openAIChatFacade;

    @Autowired
    private MarketItemAiConfig marketItemAiConfig;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;


    @Override
    public MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildCreateParam(input);
        return marketItemAiExtCommandDomainService.insert(param);
    }


    @Override
    public int update(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildUpdateParam(input);
        return marketItemAiExtCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return marketItemAiExtCommandDomainService.delete(id);
    }

    /**
     * 构建商品信息文本
     */
    private String buildProductInfoText(ProductBasicInfoEntity productInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append("商品名称：").append(productInfo.getPdName()).append("\n");

        if (StringUtils.isNotBlank(productInfo.getSpecification())) {
            sb.append("规格：").append(productInfo.getSpecification()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getOrigin())) {
            sb.append("产地：").append(productInfo.getOrigin()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getBrandName())) {
            sb.append("品牌：").append(productInfo.getBrandName()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getFirstCategoryName())) {
            sb.append("类目：").append(productInfo.getFirstCategoryName());
            if (StringUtils.isNotBlank(productInfo.getSecondCategoryName())) {
                sb.append(" > ").append(productInfo.getSecondCategoryName());
            }
            if (StringUtils.isNotBlank(productInfo.getThirdCategoryName())) {
                sb.append(" > ").append(productInfo.getThirdCategoryName());
            }
            sb.append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getPdDetail())) {
            sb.append("商品描述：").append(productInfo.getPdDetail()).append("\n");
        }

        if (StringUtils.isNotBlank(productInfo.getStorageArea())) {
            sb.append("仓储区域：").append(productInfo.getStorageArea()).append("\n");
        }

        // 添加产品属性
        appendAttributeIfNotBlank(sb, "储藏温度", productInfo.getProductAttrStorageTemp());
        appendAttributeIfNotBlank(sb, "商品形态", productInfo.getProductAttrGoodsForm());
        appendAttributeIfNotBlank(sb, "成分", productInfo.getProductAttrIngredient());
        appendAttributeIfNotBlank(sb, "口味", productInfo.getProductAttrTaste());
        appendAttributeIfNotBlank(sb, "使用方法", productInfo.getProductAttrUsageMethod());

        return sb.toString();
    }

    private void appendAttributeIfNotBlank(StringBuilder sb, String label, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(label).append("：").append(value).append("\n");
        }
    }

    /**
     * 解析AI响应，提取问题列表
     */
    private List<String> parseAIResponse(String aiResponse) {
        try {
            // 尝试直接解析JSON数组
            return JSON.parseArray(aiResponse, String.class);
        } catch (Exception e) {
            log.warn("parseAIResponse: failed to parse as JSON array, trying to extract manually", e);

            // 如果JSON解析失败，尝试手动提取问题
            String[] lines = aiResponse.split("\n");
            return Arrays.stream(lines)
                    .filter(line -> line.trim().length() > 0 && (line.contains("?") || line.contains("？")))
                    .map(String::trim)
                    .filter(line -> line.length() > 5) // 过滤太短的行
                    .collect(Collectors.toList());
        }
    }

    @Override
    public BatchInitResult batchInitMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, false);
    }

    @Override
    public BatchInitResult refreshMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, true);
    }

    /**
     * 处理批量初始化或刷新
     * @param skus SKU列表
     * @param forceRefresh 是否强制刷新（跳过已存在检查）
     * @return 处理结果
     */
    private BatchInitResult processBatchInit(List<String> skus, boolean forceRefresh) {
        long startTime = System.currentTimeMillis();

        // 如果SKU列表为空，自动查询需要处理的SKU
        if (CollectionUtils.isEmpty(skus)) {
            log.info("processBatchInit: sku list is empty, querying skus that need AI questions");
            skus = querySkusNeedAiQuestionsWithPagination();
            log.info("processBatchInit: found {} skus need AI questions", skus.size());
        }

        BatchInitResult result = new BatchInitResult(skus.size());
        result.setSuccessSkus(Lists.newArrayList());
        result.setFailSkus(Lists.newArrayList());
        result.setSkipSkus(Lists.newArrayList());

        if (CollectionUtils.isEmpty(skus)) {
            log.warn("processBatchInit: no skus to process");
            result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
            return result;
        }

        try {
            List<String> filteredSkus;

            if (forceRefresh) {
                // 强制刷新模式：删除已存在的数据，然后处理所有SKU
                log.info("processBatchInit: force refresh mode, will delete existing data");
                deleteExistingQuestions(skus, result);
                filteredSkus = skus;
            } else {
                // 1. 批量检查已存在的数据，过滤掉已有问题的SKU
                filteredSkus = filterExistingSkus(skus, result);
                if (filteredSkus.isEmpty()) {
                    log.info("processBatchInit: all skus already have questions, skip processing");
                    result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                    return result;
                }
            }

            // 2. 批量查询商品基础数据
            Map<String, ProductBasicInfoEntity> productInfoMap = batchQueryProductInfo(filteredSkus, result);
            if (productInfoMap.isEmpty()) {
                log.warn("processBatchInit: no valid product info found");
                result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                return result;
            }

            // 3. 批量生成AI问题并保存
            batchGenerateAndSaveQuestions(productInfoMap, result);

        } catch (Exception e) {
            log.error("processBatchInit: error processing batch", e);
            result.setErrorMessage("批量处理异常: " + e.getMessage());
        }

        result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        log.info("processBatchInit: completed, mode={}, total={}, success={}, fail={}, skip={}, time={}ms",
                forceRefresh ? "REFRESH" : "INIT", result.getTotalCount(), result.getSuccessCount(),
                result.getFailCount(), result.getSkipCount(), result.getProcessingTimeMs());

        return result;
    }

    /**
     * 分页查询需要处理的SKU，避免一次性加载大量数据
     */
    private List<String> querySkusNeedAiQuestionsWithPagination() {
        // 从配置获取参数
        final int PAGE_SIZE = marketItemAiConfig.getAutoQueryPageSize();
        final int MAX_TOTAL = marketItemAiConfig.getAutoQueryMaxTotal();

        List<String> allSkus = Lists.newArrayList();
        int offset = 0;
        int totalCount = 0;

        try {
            // 先获取总数，用于日志记录
            totalCount = inventoryQueryRepository.countSkusNeedAiQuestions();
            log.info("querySkusNeedAiQuestionsWithPagination: total {} skus need AI questions", totalCount);

            // 如果总数超过最大限制，记录警告
            if (totalCount > MAX_TOTAL) {
                log.warn("querySkusNeedAiQuestionsWithPagination: total count {} exceeds max limit {}, will process first {} skus",
                        totalCount, MAX_TOTAL, MAX_TOTAL);
            }

            // 分页查询
            while (allSkus.size() < Math.min(totalCount, MAX_TOTAL)) {
                List<String> pageSkus = inventoryQueryRepository.querySkusNeedAiQuestionsWithPage(offset, PAGE_SIZE);

                if (pageSkus.isEmpty()) {
                    log.info("querySkusNeedAiQuestionsWithPagination: no more skus found, break pagination");
                    break;
                }

                allSkus.addAll(pageSkus);
                offset += PAGE_SIZE;

                log.debug("querySkusNeedAiQuestionsWithPagination: loaded page {}, current total: {}",
                        offset / PAGE_SIZE, allSkus.size());

                // 达到最大限制时停止
                if (allSkus.size() >= MAX_TOTAL) {
                    allSkus = allSkus.subList(0, MAX_TOTAL);
                    log.warn("querySkusNeedAiQuestionsWithPagination: reached max limit {}, stop loading", MAX_TOTAL);
                    break;
                }
            }

        } catch (Exception e) {
            log.error("querySkusNeedAiQuestionsWithPagination: error during pagination query", e);
            // 如果分页查询失败，回退到原始查询方法，但限制数量
            try {
                List<String> fallbackSkus = inventoryQueryRepository.querySkusNeedAiQuestions();
                if (fallbackSkus.size() > MAX_TOTAL) {
                    fallbackSkus = fallbackSkus.subList(0, MAX_TOTAL);
                    log.warn("querySkusNeedAiQuestionsWithPagination: fallback query returned {} skus, limited to {}",
                            fallbackSkus.size(), MAX_TOTAL);
                }
                return fallbackSkus;
            } catch (Exception ex) {
                log.error("querySkusNeedAiQuestionsWithPagination: fallback query also failed", ex);
                return Lists.newArrayList();
            }
        }

        log.info("querySkusNeedAiQuestionsWithPagination: loaded {} skus for processing (total available: {})",
                allSkus.size(), totalCount);
        return allSkus;
    }

    /**
     * 删除已存在的问题数据（用于强制刷新）
     */
    private void deleteExistingQuestions(List<String> skus, BatchInitResult result) {
        try {
            // 分批查询并删除已存在的数据
            int batchSize = 100;
            for (int i = 0; i < skus.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, skus.size());
                List<String> batchSkus = skus.subList(i, endIndex);

                List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(batchSkus, 1);
                for (MarketItemAiExtEntity entity : existingData) {
                    marketItemAiExtCommandDomainService.delete(entity.getId());
                    log.debug("deleteExistingQuestions: deleted existing data for sku {}", entity.getSku());
                }
            }
            log.info("deleteExistingQuestions: deleted existing data for {} skus", skus.size());
        } catch (Exception e) {
            log.error("deleteExistingQuestions: error deleting existing data", e);
        }
    }

    /**
     * 过滤已存在问题的SKU
     */
    private List<String> filterExistingSkus(List<String> skus, BatchInitResult result) {
        List<String> filteredSkus = Lists.newArrayList();

        // 分批查询，避免IN条件过长
        int batchSize = 100;
        for (int i = 0; i < skus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skus.size());
            List<String> batchSkus = skus.subList(i, endIndex);

            try {
                // 使用批量查询优化性能
                List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(batchSkus, 1);
                Set<String> existingSkus = existingData.stream()
                        .map(MarketItemAiExtEntity::getSku)
                        .collect(Collectors.toSet());

                for (String sku : batchSkus) {
                    if (existingSkus.contains(sku)) {
                        result.getSkipSkus().add(sku);
                        result.setSkipCount(result.getSkipCount() + 1);
                    } else {
                        filteredSkus.add(sku);
                    }
                }
            } catch (Exception e) {
                log.error("filterExistingSkus: error querying batch {}", batchSkus, e);
                // 如果批量查询失败，将这批SKU标记为失败
                for (String sku : batchSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }
        }

        return filteredSkus;
    }

    /**
     * 批量查询商品基础信息
     */
    private Map<String, ProductBasicInfoEntity> batchQueryProductInfo(List<String> skus, BatchInitResult result) {
        Map<String, ProductBasicInfoEntity> productInfoMap = new HashMap<>();

        // 分批查询，避免SQL过长
        int batchSize = 50;
        for (int i = 0; i < skus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skus.size());
            List<String> batchSkus = skus.subList(i, endIndex);

            try {
                List<ProductBasicInfoEntity> productInfoList = inventoryQueryRepository.batchQueryProductBasicInfoForAI(batchSkus);
                for (ProductBasicInfoEntity productInfo : productInfoList) {
                    productInfoMap.put(productInfo.getSku(), productInfo);
                }
            } catch (Exception e) {
                log.error("batchQueryProductInfo: error querying batch {}", batchSkus, e);
                // 将这批SKU标记为失败
                for (String sku : batchSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }
        }

        // 检查哪些SKU没有查到数据
        for (String sku : skus) {
            if (!productInfoMap.containsKey(sku) && !result.getFailSkus().contains(sku)) {
                result.getFailSkus().add(sku);
                result.setFailCount(result.getFailCount() + 1);
                log.warn("batchQueryProductInfo: no product info found for sku {}", sku);
            }
        }

        return productInfoMap;
    }

    /**
     * 批量生成AI问题并保存
     */
    private void batchGenerateAndSaveQuestions(Map<String, ProductBasicInfoEntity> productInfoMap, BatchInitResult result) {
        for (Map.Entry<String, ProductBasicInfoEntity> entry : productInfoMap.entrySet()) {
            String sku = entry.getKey();
            ProductBasicInfoEntity productInfo = entry.getValue();

            try {
                // 构建商品信息文本
                String productInfoText = buildProductInfoText(productInfo);

                // 调用AI生成问题
                String prompt = nacosPropertiesHolder.getGoodsRelationQuestionPrompt();
                String aiResponse = openAIChatFacade.chat(productInfoText, prompt);

                if (StringUtils.isBlank(aiResponse)) {
                    log.warn("batchGenerateAndSaveQuestions: AI response is blank for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 解析AI响应
                List<String> questions = parseAIResponse(aiResponse);
                if (questions.isEmpty()) {
                    log.warn("batchGenerateAndSaveQuestions: no questions parsed from AI response for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 保存到数据库
                MarketItemAiExtCommandParam param = new MarketItemAiExtCommandParam();
                param.setSku(sku);
                param.setPdId(productInfo.getPdId());
                param.setExtType(1); // 商品相关问题
                param.setExtValue(JSON.toJSONString(questions));
                param.setTenantId(1L);
                param.setCreateTime(LocalDateTime.now());
                param.setUpdateTime(LocalDateTime.now());

                marketItemAiExtCommandDomainService.insert(param);

                result.getSuccessSkus().add(sku);
                result.setSuccessCount(result.getSuccessCount() + 1);
                log.debug("batchGenerateAndSaveQuestions: successfully saved {} questions for sku {}", questions.size(), sku);

            } catch (Exception e) {
                log.error("batchGenerateAndSaveQuestions: error processing sku {}", sku, e);
                result.getFailSkus().add(sku);
                result.setFailCount(result.getFailCount() + 1);
            }
        }
    }
}