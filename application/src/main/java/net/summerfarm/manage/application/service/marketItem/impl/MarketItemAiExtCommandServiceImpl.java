package net.summerfarm.manage.application.service.marketItem.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.common.enums.MarketItemAiExtTypeEnum;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.service.MarketItemAiExtCommandDomainService;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.product.entity.ProductBasicInfoEntity;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.ai.OpenAIChatFacade;
import net.summerfarm.manage.facade.wiki.WikiKnowledgeFacade;
import net.summerfarm.manage.facade.wiki.dto.SearchResultItem;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MarketItemAiExtCommandServiceImpl implements MarketItemAiExtCommandService {

    @Autowired
    private MarketItemAiExtCommandDomainService marketItemAiExtCommandDomainService;

    @Autowired
    private MarketItemAiExtQueryRepository marketItemAiExtQueryRepository;

    @Autowired
    private InventoryQueryRepository inventoryQueryRepository;

    @Autowired
    private OpenAIChatFacade openAIChatFacade;

    @Autowired
    private WikiKnowledgeFacade wikiKnowledgeFacade;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;


    @Override
    public MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildCreateParam(input);
        return marketItemAiExtCommandDomainService.insert(param);
    }


    @Override
    public int update(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildUpdateParam(input);
        return marketItemAiExtCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return marketItemAiExtCommandDomainService.delete(id);
    }

    /**
     * 构建商品信息文本（只包含非空字段）
     */
    private String buildProductInfoText(ProductBasicInfoEntity productInfo) {
        StringBuilder sb = new StringBuilder();

        // 商品名称（必填）
        if (StringUtils.isNotBlank(productInfo.getPdName())) {
            sb.append("商品名称：").append(productInfo.getPdName()).append("\n");
        }

        // 基本信息
        appendAttributeIfNotBlank(sb, "规格", productInfo.getSpecification());
        appendAttributeIfNotBlank(sb, "产地", productInfo.getOrigin());
        appendAttributeIfNotBlank(sb, "品牌", productInfo.getBrandName());

        // 类目信息
        if (StringUtils.isNotBlank(productInfo.getFirstCategoryName())) {
            sb.append("类目：").append(productInfo.getFirstCategoryName());
            if (StringUtils.isNotBlank(productInfo.getSecondCategoryName())) {
                sb.append(" > ").append(productInfo.getSecondCategoryName());
            }
            if (StringUtils.isNotBlank(productInfo.getThirdCategoryName())) {
                sb.append(" > ").append(productInfo.getThirdCategoryName());
            }
            sb.append("\n");
        }

        // 商品描述
        appendAttributeIfNotBlank(sb, "商品描述", productInfo.getPdDetail());

        // 物理属性
        appendAttributeIfNotBlank(sb, "体积", productInfo.getVolume());
        appendAttributeIfNotBlank(sb, "重量", productInfo.getWeightNum());
        appendAttributeIfNotBlank(sb, "重量单位", productInfo.getWeightUnit());
        appendAttributeIfNotBlank(sb, "包装单位", productInfo.getPack());
        appendAttributeIfNotBlank(sb, "售后单位", productInfo.getAfterSaleUnit());

        // 仓储信息
        appendAttributeIfNotBlank(sb, "仓储区域", productInfo.getStorageArea());

        // 保质期信息
        appendAttributeIfNotBlank(sb, "保质期时长", productInfo.getQualityTime());
        appendAttributeIfNotBlank(sb, "保质期单位", productInfo.getQualityTimeUnit());
        appendAttributeIfNotBlank(sb, "临期时长", productInfo.getWarnTime());
        appendAttributeIfNotBlank(sb, "临期时长单位", productInfo.getWarnTimeUnit());

        // 售后信息
        appendAttributeIfNotBlank(sb, "可退款原因", productInfo.getRefundType());
        appendAttributeIfNotBlank(sb, "售后类型", productInfo.getAfterSaleType());

        // 产品级属性
        appendAttributeIfNotBlank(sb, "储藏温度", productInfo.getProductAttrStorageTemp());
        appendAttributeIfNotBlank(sb, "商品形态", productInfo.getProductAttrGoodsForm());
        appendAttributeIfNotBlank(sb, "乳脂含量", productInfo.getProductAttrMilkFatContent());
        appendAttributeIfNotBlank(sb, "使用方法", productInfo.getProductAttrUsageMethod());
        appendAttributeIfNotBlank(sb, "成分", productInfo.getProductAttrIngredient());
        appendAttributeIfNotBlank(sb, "面筋含量", productInfo.getProductAttrGlutenContent());
        appendAttributeIfNotBlank(sb, "口味", productInfo.getProductAttrTaste());
        appendAttributeIfNotBlank(sb, "是否含糖", productInfo.getProductAttrContainsSugar());
        appendAttributeIfNotBlank(sb, "湿度", productInfo.getProductAttrHumidity());
        appendAttributeIfNotBlank(sb, "肉类品种", productInfo.getProductAttrMeatVariety());
        appendAttributeIfNotBlank(sb, "蔬菜品种", productInfo.getProductAttrVegetableVariety());
        appendAttributeIfNotBlank(sb, "品种", productInfo.getProductAttrVariety());
        appendAttributeIfNotBlank(sb, "熟度", productInfo.getProductAttrRipeness());
        appendAttributeIfNotBlank(sb, "每100g乳脂含量", productInfo.getProductAttrMilkFatPer100g());
        appendAttributeIfNotBlank(sb, "每100g含蛋白质", productInfo.getProductAttrProteinPer100g());
        appendAttributeIfNotBlank(sb, "其他属性", productInfo.getProductAttrOther());

        // SKU级属性
        appendAttributeIfNotBlank(sb, "SKU规格", productInfo.getSkuAttrSpecification());
        appendAttributeIfNotBlank(sb, "级别", productInfo.getSkuAttrLevel());
        appendAttributeIfNotBlank(sb, "果规", productInfo.getSkuAttrFruitSpec());
        appendAttributeIfNotBlank(sb, "自定义属性", productInfo.getSkuAttrCustom());
        appendAttributeIfNotBlank(sb, "SKU口味", productInfo.getSkuAttrTaste());
        appendAttributeIfNotBlank(sb, "原料/成品", productInfo.getSkuAttrRawMaterialFinished());
        appendAttributeIfNotBlank(sb, "尺寸", productInfo.getSkuAttrSize());
        appendAttributeIfNotBlank(sb, "重复利用", productInfo.getSkuAttrReusable());
        appendAttributeIfNotBlank(sb, "自动出库", productInfo.getSkuAttrAutoOutbound());

        return sb.toString();
    }

    private void appendAttributeIfNotBlank(StringBuilder sb, String label, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(label).append("：").append(value).append("\n");
        }
    }

    /**
     * 解析AI响应，提取问题列表
     */
    private List<String> parseAIResponse(String aiResponse) {
        try {
            // 尝试直接解析JSON数组
            return JSON.parseArray(aiResponse, String.class);
        } catch (Exception e) {
            log.warn("parseAIResponse: failed to parse as JSON array, trying to extract manually", e);

            // 如果JSON解析失败，尝试手动提取问题
            String[] lines = aiResponse.split("\n");
            return Arrays.stream(lines)
                    .filter(line -> line.trim().length() > 0 && (line.contains("?") || line.contains("？")))
                    .map(String::trim)
                    .filter(line -> line.length() > 5) // 过滤太短的行
                    .collect(Collectors.toList());
        }
    }

    @Override
    public BatchInitResult batchInitMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, false);
    }

    @Override
    public BatchInitResult refreshMarketItemQuestions(List<String> skus) {
        return processBatchInit(skus, true);
    }

    /**
     * 处理批量初始化或刷新
     * @param skus SKU列表
     * @param forceRefresh 是否强制刷新（跳过已存在检查）
     * @return 处理结果
     */
    private BatchInitResult processBatchInit(List<String> skus, boolean forceRefresh) {
        long startTime = System.currentTimeMillis();

        // 如果SKU列表为空，自动查询需要处理的SKU
        if (CollectionUtils.isEmpty(skus)) {
            log.info("processBatchInit: sku list is empty, querying skus that need AI questions");
            throw new BizException("sku list is empty");
        }

        BatchInitResult result = new BatchInitResult(skus.size());
        result.setSuccessSkus(Lists.newArrayList());
        result.setFailSkus(Lists.newArrayList());
        result.setSkipSkus(Lists.newArrayList());

        try {
            List<String> filteredSkus;

            if (forceRefresh) {
                // 强制刷新模式：处理所有SKU，更新已存在的记录
                log.info("processBatchInit: force refresh mode, will update existing data");
                filteredSkus = skus;
            } else {
                // 1. 批量检查已存在的数据，过滤掉已有问题的SKU
                filteredSkus = filterExistingSkus(skus, result);
                if (filteredSkus.isEmpty()) {
                    log.info("processBatchInit: all skus already have questions, skip processing");
                    result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                    return result;
                }
            }

            // 2. 批量查询商品基础数据
            Map<String, ProductBasicInfoEntity> productInfoMap = batchQueryProductInfo(filteredSkus, result);
            if (productInfoMap.isEmpty()) {
                log.warn("processBatchInit: no valid product info found");
                result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                return result;
            }

            // 3. 批量生成AI问题并保存
            batchGenerateAndSaveQuestions(productInfoMap, result, forceRefresh);

        } catch (Exception e) {
            log.error("processBatchInit: error processing batch", e);
            result.setErrorMessage("批量处理异常: " + e.getMessage());
        }

        result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        log.info("processBatchInit: completed, mode={}, total={}, success={}, fail={}, skip={}, time={}ms",
                forceRefresh ? "REFRESH" : "INIT", result.getTotalCount(), result.getSuccessCount(),
                result.getFailCount(), result.getSkipCount(), result.getProcessingTimeMs());

        return result;
    }





    /**
     * 过滤已存在问题的SKU
     */
    private List<String> filterExistingSkus(List<String> skus, BatchInitResult result) {
        List<String> filteredSkus = Lists.newArrayList();

        // 分批查询，避免IN条件过长
        int batchSize = 100;
        for (int i = 0; i < skus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skus.size());
            List<String> batchSkus = skus.subList(i, endIndex);

            try {
                // 使用批量查询优化性能
                List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(
                        batchSkus, MarketItemAiExtTypeEnum.RELATION_QUESTION.getCode());
                Set<String> existingSkus = existingData.stream()
                        .map(MarketItemAiExtEntity::getSku)
                        .collect(Collectors.toSet());

                for (String sku : batchSkus) {
                    if (existingSkus.contains(sku)) {
                        result.getSkipSkus().add(sku);
                        result.setSkipCount(result.getSkipCount() + 1);
                    } else {
                        filteredSkus.add(sku);
                    }
                }
            } catch (Exception e) {
                log.error("filterExistingSkus: error querying batch {}", batchSkus, e);
                // 如果批量查询失败，将这批SKU标记为失败
                for (String sku : batchSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }
        }

        return filteredSkus;
    }

    /**
     * 批量查询商品基础信息
     */
    private Map<String, ProductBasicInfoEntity> batchQueryProductInfo(List<String> skus, BatchInitResult result) {
        Map<String, ProductBasicInfoEntity> productInfoMap = new HashMap<>();

        // 分批查询，避免SQL过长
        int batchSize = 50;
        for (int i = 0; i < skus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skus.size());
            List<String> batchSkus = skus.subList(i, endIndex);

            try {
                List<ProductBasicInfoEntity> productInfoList = inventoryQueryRepository.batchQueryProductBasicInfoForAI(batchSkus);
                for (ProductBasicInfoEntity productInfo : productInfoList) {
                    productInfoMap.put(productInfo.getSku(), productInfo);
                }
            } catch (Exception e) {
                log.error("batchQueryProductInfo: error querying batch {}", batchSkus, e);
                // 将这批SKU标记为失败
                for (String sku : batchSkus) {
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                }
            }
        }

        // 检查哪些SKU没有查到数据
        for (String sku : skus) {
            if (!productInfoMap.containsKey(sku) && !result.getFailSkus().contains(sku)) {
                result.getFailSkus().add(sku);
                result.setFailCount(result.getFailCount() + 1);
                log.warn("batchQueryProductInfo: no product info found for sku {}", sku);
            }
        }

        return productInfoMap;
    }

    /**
     * 批量生成AI问题并保存
     */
    private void batchGenerateAndSaveQuestions(Map<String, ProductBasicInfoEntity> productInfoMap, BatchInitResult result, boolean forceRefresh) {
        for (Map.Entry<String, ProductBasicInfoEntity> entry : productInfoMap.entrySet()) {
            String sku = entry.getKey();
            ProductBasicInfoEntity productInfo = entry.getValue();

            try {
                // 构建商品信息文本
                String productInfoText = buildProductInfoText(productInfo);

                // 搜索相关知识库内容
                String knowledgeContext = searchRelatedKnowledge(productInfo);

                // 构建完整的上下文信息
                String fullContext = buildFullContext(productInfoText, knowledgeContext);

                // 调用AI生成问题
                String prompt = nacosPropertiesHolder.getGoodsRelationQuestionPrompt();
                String aiResponse = openAIChatFacade.chat(fullContext, prompt);

                if (StringUtils.isBlank(aiResponse)) {
                    log.warn("batchGenerateAndSaveQuestions: AI response is blank for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 解析AI响应
                List<String> questions = parseAIResponse(aiResponse);
                if (questions.isEmpty()) {
                    log.warn("batchGenerateAndSaveQuestions: no questions parsed from AI response for sku {}", sku);
                    result.getFailSkus().add(sku);
                    result.setFailCount(result.getFailCount() + 1);
                    continue;
                }

                // 保存到数据库
                if (forceRefresh) {
                    // 强制刷新模式：查找已存在的记录并更新
                    List<MarketItemAiExtEntity> existingData = marketItemAiExtQueryRepository.batchSelectBySkusAndExtType(
                            Collections.singletonList(sku), 1);

                    if (!existingData.isEmpty()) {
                        // 更新已存在的记录
                        MarketItemAiExtEntity existingEntity = existingData.get(0);
                        MarketItemAiExtCommandParam updateParam = new MarketItemAiExtCommandParam();
                        updateParam.setId(existingEntity.getId());
                        updateParam.setExtValue(JSON.toJSONString(questions));
                        updateParam.setUpdateTime(LocalDateTime.now());

                        marketItemAiExtCommandDomainService.update(updateParam);
                        log.debug("batchGenerateAndSaveQuestions: updated existing record for sku {}", sku);
                    } else {
                        // 如果没有找到已存在的记录，则插入新记录
                        insertNewRecord(sku, productInfo, questions);
                        log.debug("batchGenerateAndSaveQuestions: inserted new record for sku {} (not found in refresh mode)", sku);
                    }
                } else {
                    // 初始化模式：直接插入新记录
                    insertNewRecord(sku, productInfo, questions);
                }

                result.getSuccessSkus().add(sku);
                result.setSuccessCount(result.getSuccessCount() + 1);
                log.debug("batchGenerateAndSaveQuestions: successfully saved {} questions for sku {}", questions.size(), sku);

            } catch (Exception e) {
                log.error("batchGenerateAndSaveQuestions: error processing sku {}", sku, e);
                result.getFailSkus().add(sku);
                result.setFailCount(result.getFailCount() + 1);
            }
        }
    }

    /**
     * 插入新记录
     */
    private void insertNewRecord(String sku, ProductBasicInfoEntity productInfo, List<String> questions) {
        MarketItemAiExtCommandParam param = new MarketItemAiExtCommandParam();
        param.setSku(sku);
        param.setPdId(productInfo.getPdId());
        param.setExtType(1); // 商品相关问题
        param.setExtValue(JSON.toJSONString(questions));
        param.setTenantId(1L);
        param.setCreateTime(LocalDateTime.now());
        param.setUpdateTime(LocalDateTime.now());

        marketItemAiExtCommandDomainService.insert(param);
    }

    /**
     * 搜索相关知识库内容
     */
    private String searchRelatedKnowledge(ProductBasicInfoEntity productInfo) {
        try {
            // 构建搜索关键词
            StringBuilder searchText = new StringBuilder();

            // 添加商品名称
            if (StringUtils.isNotBlank(productInfo.getPdName())) {
                searchText.append(productInfo.getPdName());
            }

            // 添加品牌信息
            if (StringUtils.isNotBlank(productInfo.getBrandName())) {
                searchText.append(" ").append(productInfo.getBrandName());
            }

            // 添加类目信息
            if (StringUtils.isNotBlank(productInfo.getThirdCategoryName())) {
                searchText.append(" ").append(productInfo.getThirdCategoryName());
            } else if (StringUtils.isNotBlank(productInfo.getSecondCategoryName())) {
                searchText.append(" ").append(productInfo.getSecondCategoryName());
            }

            // 添加成分信息
            if (StringUtils.isNotBlank(productInfo.getProductAttrIngredient())) {
                searchText.append(" ").append(productInfo.getProductAttrIngredient());
            }

            String searchQuery = searchText.toString().trim();
            if (StringUtils.isBlank(searchQuery)) {
                log.warn("searchRelatedKnowledge: 搜索关键词为空, sku={}", productInfo.getSku());
                return "";
            }

            log.debug("searchRelatedKnowledge: 搜索关键词={}", searchQuery);

            // 调用Wiki知识库搜索
            List<SearchResultItem> searchResults = wikiKnowledgeFacade.searchWiki(searchQuery, null, 2000);

            if (searchResults.isEmpty()) {
                log.debug("searchRelatedKnowledge: 未找到相关知识, sku={}", productInfo.getSku());
                return "";
            }

            // 组装知识内容
            StringBuilder knowledgeBuilder = new StringBuilder();
            int maxResults = Math.min(3, searchResults.size()); // 最多取3条结果

            for (int i = 0; i < maxResults; i++) {
                SearchResultItem item = searchResults.get(i);
                if (StringUtils.isNotBlank(item.getA())) {
                    knowledgeBuilder.append("相关知识").append(i + 1).append("：")
                            .append(item.getA()).append("\n");
                }
            }

            String knowledge = knowledgeBuilder.toString();
            log.debug("searchRelatedKnowledge: 找到{}条相关知识, sku={}", maxResults, productInfo.getSku());

            return knowledge;

        } catch (Exception e) {
            log.error("searchRelatedKnowledge: 搜索知识库异常, sku={}", productInfo.getSku(), e);
            return "";
        }
    }

    /**
     * 构建完整的上下文信息
     */
    private String buildFullContext(String productInfoText, String knowledgeContext) {
        StringBuilder contextBuilder = new StringBuilder();

        // 添加商品信息
        contextBuilder.append("=== 商品信息 ===\n");
        contextBuilder.append(productInfoText);

        // 添加相关知识（如果有）
        if (StringUtils.isNotBlank(knowledgeContext)) {
            contextBuilder.append("\n=== 相关专业知识 ===\n");
            contextBuilder.append(knowledgeContext);
        }

        return contextBuilder.toString();
    }

    @Override
    public String testProductInfoText(String sku) {
        if (StringUtils.isBlank(sku)) {
            return "SKU编码不能为空";
        }

        try {
            List<ProductBasicInfoEntity> productInfoList = inventoryQueryRepository.batchQueryProductBasicInfoForAI(
                    Collections.singletonList(sku));

            if (productInfoList.isEmpty()) {
                return "未找到SKU: " + sku + " 的商品信息";
            }

            ProductBasicInfoEntity productInfo = productInfoList.get(0);
            return buildProductInfoText(productInfo);

        } catch (Exception e) {
            log.error("testProductInfoText: error for sku {}", sku, e);
            return "查询商品信息异常: " + e.getMessage();
        }
    }
}