package net.summerfarm.manage.application.service.marketItem.impl;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.service.MarketItemAiExtCommandDomainService;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;
import net.summerfarm.manage.application.inbound.controller.marketItem.assembler.MarketItemAiExtAssembler;
import net.summerfarm.manage.domain.product.entity.ProductBasicInfoEntity;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.facade.ai.OpenAIChatFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class MarketItemAiExtCommandServiceImpl implements MarketItemAiExtCommandService {

    @Autowired
    private MarketItemAiExtCommandDomainService marketItemAiExtCommandDomainService;


    @Override
    public MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildCreateParam(input);
        return marketItemAiExtCommandDomainService.insert(param);
    }


    @Override
    public int update(MarketItemAiExtCommandInput input) {
        MarketItemAiExtCommandParam param = MarketItemAiExtAssembler.buildUpdateParam(input);
        return marketItemAiExtCommandDomainService.update(param);
    }


    @Override
    public int delete(Long id) {
        return marketItemAiExtCommandDomainService.delete(id);
    }
}