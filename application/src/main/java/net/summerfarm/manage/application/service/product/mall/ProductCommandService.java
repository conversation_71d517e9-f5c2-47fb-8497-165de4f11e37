package net.summerfarm.manage.application.service.product.mall;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductUpdateInput;
import net.summerfarm.manage.application.inbound.controller.product.input.command.WarnTimeCommandInput;

import java.util.List;

/**
 * @ClassName ProductCommandService
 * @Description
 * <AUTHOR>
 * @Date 10:56 2024/5/7
 * @Version 1.0
 **/
public interface ProductCommandService {


    void updateProductInfo(ProductUpdateInput input);

    /**
     * 批量更新预警时长
     *
     * <AUTHOR>
     * @date 2025/4/16 15:41
     * @param inputList 输入列表
     */
    void batchUpdateWarnTime(List<WarnTimeCommandInput> inputList);

}
