package net.summerfarm.manage.application.inbound.controller.major;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.manage.application.inbound.controller.major.assembler.MajorPriceLogAssembler;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceCommitInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceMallShowBatchInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceUpdateBatchInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorCustomerQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceDownloadInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceLogQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.input.query.MajorPriceRepeatQueryInput;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorCustomerDetailVO;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLargeAreaVO;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceLogVO;
import net.summerfarm.manage.application.inbound.controller.major.vo.MajorPriceVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.ExcelImportResDTO;
import net.summerfarm.manage.application.service.major.MajorPriceLogQueryService;
import net.summerfarm.manage.application.service.major.impl.MajorCustomerQueryService;
import net.summerfarm.manage.application.service.product.mall.MajorPriceCommandService;
import net.summerfarm.manage.application.service.product.mall.MajorPriceQueryService;
import net.summerfarm.manage.application.util.QuotationExporter;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.constants.Global;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 大客户报价单
 */

@RestController
@RequestMapping(value = "major")
public class MajorPriceController {


    @Autowired
    private MajorPriceCommandService majorPriceCommandService;
    @Autowired
    private MajorPriceQueryService majorPriceQueryService;
    @Autowired
    private MajorPriceLogQueryService majorPriceLogQueryService;
    @Autowired
    private MajorCustomerQueryService majorCustomerQueryService;

    /**
     * 批量修改生效中的 大客户报价单 "门店可见该商品"  字段
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"major:majorprice"}, logical = Logical.OR)
    @RequestMapping(value = "/upload/mall-show-batchupdate", method = RequestMethod.POST)
    public CommonResult<Void> majorPriceMallShowBatchUpdate(@RequestBody @Validated MajorPriceMallShowBatchInput input) {
        majorPriceCommandService.majorPriceMallShowBatchUpdate(input.getMallShow (),input.getDirect (),input.getAdminId ());
        return CommonResult.ok();
    }

    /**
     * 保存/提交报价单
     * @param inputs
     * @return
     */
    @RequiresPermissions(value = {"major:majorprice"}, logical = Logical.OR)
    @RequestMapping(value = "/majorprice/init", method = RequestMethod.POST)
    public CommonResult<Void> addMajorPrice(@RequestBody @Validated List<MajorPriceInput> inputs) {
        majorPriceCommandService.addMajorPrice(inputs);
        return CommonResult.ok();
    }
    /**
     * 批量改价
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"major:majorprice"}, logical = Logical.OR)
    @RequestMapping(value = "/majorprice/updateBatch", method = RequestMethod.POST)
    public CommonResult<Void> updateBatch(@RequestBody @Validated MajorPriceUpdateBatchInput input) {
        majorPriceCommandService.updateBatch(input);
        return CommonResult.ok();
    }

    /**
     * 批量提交报价单
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"major:majorprice"}, logical = Logical.OR)
    @RequestMapping(value = "/majorprice/commitBatch", method = RequestMethod.POST)
    public CommonResult<Void> commitBatch(@RequestBody @Validated MajorPriceCommitInput input) {
        majorPriceCommandService.commitBatch(input.getIds ());
        return CommonResult.ok();
    }
    /**
     * 提交报价单 是否重复查询
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"major:majorprice"}, logical = Logical.OR)
    @RequestMapping(value = "/majorprice/repeatQuery", method = RequestMethod.POST)
    public CommonResult<String> repeatQuery(@RequestBody @Validated MajorPriceRepeatQueryInput input) {
        return CommonResult.ok(majorPriceQueryService.repeatQuery(input));
    }


    /**
     * 报价单列表
     * @param majorPriceInput
     * @return
     */
    @RequestMapping(value = "/majorprice/list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MajorPriceLargeAreaVO>> majorPriceList(@RequestBody @Validated MajorPriceQueryInput majorPriceInput) {
        return CommonResult.ok(majorPriceQueryService.majorPricePage(majorPriceInput));
    }


    /**
     * 报价单列表-失效列表
     * @param majorPriceInput
     * @return
     */
    @RequestMapping(value = "/majorprice/invalid-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MajorPriceVO>> majorPriceInvalidList(@RequestBody @Validated MajorPriceQueryInput majorPriceInput) {
        return CommonResult.ok(majorPriceQueryService.majorPriceCityPage(majorPriceInput));
    }


    /**
     * 查询大客户报价单的变更记录
     * @return MajorPriceLogVO
     */
    @PostMapping(value="/query/log-page")
    public CommonResult<PageInfo<MajorPriceLogVO>> getMajorPriceLogPage(@RequestBody MajorPriceLogQueryInput input){
        PageInfo<MajorPriceLogEntity> page = majorPriceLogQueryService.getPage(input);
        return CommonResult.ok(PageInfoConverter.toPageResp(page, MajorPriceLogAssembler::toMajorPriceLogVO));
    }


    /**
     * 查询大客户详情
     * @return MajorPriceLogVO
     */
    @PostMapping(value="/query/major-customer/detail")
    public CommonResult<MajorCustomerDetailVO> getMajorCustomerDetail(@RequestBody MajorCustomerQueryInput input){
        MajorCustomerDetailVO detail = majorCustomerQueryService.getDetail(input.getAdminId());
        return CommonResult.ok(detail);
    }


    /**
     * 报价单导出
     * @param response
     * @param input
     */
    @PostMapping("/major-price/export")
    public CommonResult<Long> exportQuotation(@Valid @RequestBody MajorPriceDownloadInput input) {
        return CommonResult.ok(majorPriceQueryService.majorPriceDownload(input));
    }

}
