package net.summerfarm.manage.application.inbound.controller.job.input.command;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-18 15:57:19
 * @version 1.0
 *
 */
@Data
public class CrmJobRewardRecordCommandInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 任务Id FK crm_job
	 */
	private Long jobId;

	/**
	 * 奖励类型：0-红包，1-优惠券
	 */
	private Integer rewardType;

	/**
	 * 卡券id
	 */
	private String rewardValue;

	/**
	 * 达成奖励发放的订单号列表
	 */
	private String orderNo;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;



}