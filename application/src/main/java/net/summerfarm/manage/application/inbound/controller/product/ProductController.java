package net.summerfarm.manage.application.inbound.controller.product;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.input.*;

import javax.annotation.Resource;

import net.summerfarm.manage.application.inbound.controller.product.vo.*;
import net.summerfarm.manage.application.service.product.mall.InventoryQueryService;
import net.summerfarm.manage.application.service.product.mall.ProductCommandService;
import net.summerfarm.manage.application.service.product.mall.ProductQueryService;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品通用接口
 * @author: <EMAIL>
 * @create: 2024/1/25
 */
@RestController
@RequestMapping("/product")
public class ProductController {

    @Resource
    private ProductQueryService productQueryService;

    @Resource
    private ProductCommandService productCommandService;

    @Resource
    private InventoryQueryService inventoryQueryService;

    /**
     * 商品分页查询
     *
     * @param input
     */
    @RequestMapping("/query/page")
    public CommonResult<PageInfo<ProductVO>> pageByQuery(@RequestBody ProductPageInput input) {
        PageInfo<ProductVO> pageInfo = productQueryService.pageByQuery(input);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 后台商品管理-列表
     *
     * @param input
     */
    @RequestMapping(value = "/query/selectPage", method = RequestMethod.GET)
    public CommonResult<PageInfo<ProductVO>> selectPage(ProductPageInput input) {
        PageInfo<ProductVO> pageInfo = productQueryService.selectPage(input);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 商品编辑
     *
     * @param input
     */
    @RequestMapping(value = "/upset/info", method = RequestMethod.PUT)
    @RequiresPermissions(value = {"inventory:update", Global.SA},logical = Logical.OR)
    public CommonResult<Boolean> updateProductInfo(@RequestBody @Valid ProductUpdateInput input) {
        productCommandService.updateProductInfo(input);
        return CommonResult.ok();
    }

    /**
     * 商品基本信息查询
     *
     * @param input
     */
    @RequestMapping(value = "/query/info", method = RequestMethod.POST)
    public CommonResult<ProductVO> queryInfo(@RequestBody @Valid ProductInfoInput input) {
        return CommonResult.ok(inventoryQueryService.queryInfo(input));
    }

    /**
     * 查询spu sku组合vo列表
     *
     * @param input
     */
    @RequestMapping("/query/page/agg")
    public CommonResult<PageInfo<MarketItemAggListVO>> pageAgg(@RequestBody MarketBaseQueryInput input) {
        return CommonResult.ok(productQueryService.pageAgg(input));
    }

    /**
     * 查询sku vo列表
     *
     * @param input
     */
    @RequestMapping("/query/page/sku")
    public CommonResult<PageInfo<MarketItemListVO>> pageSku(@RequestBody MarketBaseQueryInput input) {
        return CommonResult.ok(productQueryService.pageSku(input));
    }
}
