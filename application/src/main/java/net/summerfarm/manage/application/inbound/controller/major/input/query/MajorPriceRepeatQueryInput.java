package net.summerfarm.manage.application.inbound.controller.major.input.query;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceInput;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MajorPriceRepeatQueryInput {
    /**
     * 报价单的生效时间
     */
    @NotNull(message = "validTime is not null")
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    @NotNull(message = "invalidTime is not null")
    private LocalDateTime  invalidTime;

    /**
     * 1是账期 2是现结
     */
    @NotNull(message = "direct is not null")
    private Integer direct;
    /**
     * 大客户账号id
     */
    @NotNull(message = "大客户账号id is not null")
    private Integer adminId;
    /**
     * 1=新增场景 0=修改场景
     */
    @NotNull(message = "场景类型 is not null")
    private Integer editType;
    /**
     * 新增场景 必输 sku
     */
    List<MajorPriceInput> inputs;
    /**
     * 修改场景 必输 ids
     */
    private List<Integer> ids;
}
