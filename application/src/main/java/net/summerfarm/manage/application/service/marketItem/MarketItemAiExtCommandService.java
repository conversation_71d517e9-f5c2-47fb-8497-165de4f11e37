package net.summerfarm.manage.application.service.marketItem;

import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;


/**
 * @date 2025-07-03 16:33:54
 * @version 1.0
 */
public interface MarketItemAiExtCommandService {

    /**
     * @description: 新增
     * @return MarketItemAiExtEntity
     **/
    MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(MarketItemAiExtCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

    /**
     * 初始化商品相关问题
     * @param sku SKU编码
     * @return 是否成功
     */
    boolean initMarketItemQuestions(String sku);

}