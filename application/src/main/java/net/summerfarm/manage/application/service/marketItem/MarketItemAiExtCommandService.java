package net.summerfarm.manage.application.service.marketItem;

import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.application.inbound.controller.marketItem.input.command.MarketItemAiExtCommandInput;

import java.util.List;


/**
 * @date 2025-07-03 16:33:54
 * @version 1.0
 */
public interface MarketItemAiExtCommandService {

    /**
     * @description: 新增
     * @return MarketItemAiExtEntity
     **/
    MarketItemAiExtEntity insert(MarketItemAiExtCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    int update(MarketItemAiExtCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

    /**
     * 批量初始化商品相关问题
     * @param skus SKU编码列表
     * @return 处理结果统计
     */
    BatchInitResult batchInitMarketItemQuestions(List<String> skus);

    /**
     * 刷新商品相关问题（强制重新生成）
     * @param skus SKU编码列表
     * @return 处理结果统计
     */
    BatchInitResult refreshMarketItemQuestions(List<String> skus);

    /**
     * 测试商品信息组装（用于验证字段完整性）
     * @param sku SKU编码
     * @return 组装后的商品信息文本
     */
    String testProductInfoText(String sku);

}