# Wiki知识库访问门面 - 使用指南

## 功能概述

`WikiKnowledgeFacade` 提供了与Wiki知识库交互的核心服务功能，用于搜索烘焙和餐饮原材料相关的专业知识。

## 配置参数

在 `application.yml` 中配置以下参数：

```yaml
wiki:
  auth:
    api:
      key: "fastgpt-yQvr3Lhma1HMOSLf57gPcHd5EsjoD1ml9fGDTFJozeqnU1oAqdUfOFWXRDg"
      url:
        prefix: "https://fastgpt-test.summerfarm.net/"
  catering:
    dataset:
      id: "684aa95eaba1d13d05b4fc61"
```

## 使用方式

### 1. 注入Facade

```java
@Autowired
private WikiKnowledgeFacade wikiKnowledgeFacade;
```

### 2. 简单搜索

```java
// 使用默认配置搜索
List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki("日清山茶花适合什么甜品使用？");
```

### 3. 自定义搜索

```java
// 指定数据集ID和返回限制
List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki(
    "植物奶和动物奶油的区别是什么？", 
    "684aa95eaba1d13d05b4fc61", 
    3000
);
```

## HTTP接口

### GET请求搜索

```http
GET /wiki/search?text=安佳淡奶油使用方法&limit=5000
```

### POST请求搜索

```http
POST /wiki/search
Content-Type: application/json

"日清山茶花适合什么甜品使用？"
```

## 搜索结果结构

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "result_id",
      "updateTime": "2025-07-07T10:00:00Z",
      "q": "问题内容",
      "a": "答案内容",
      "chunkIndex": 0,
      "datasetId": "684aa95eaba1d13d05b4fc61",
      "collectionId": "collection_id",
      "sourceId": "source_id",
      "sourceName": "来源名称",
      "score": [
        {
          "type": "similarity",
          "value": 0.85,
          "index": 0
        }
      ],
      "tokens": 150
    }
  ]
}
```

## 搜索示例

### 产品使用方法查询
```java
List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki("安佳淡奶油使用方法");
```

### 产品对比查询
```java
List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki("植物奶和动物奶油的区别是什么？");
```

### 适用场景查询
```java
List<SearchResultItem> results = wikiKnowledgeFacade.searchWiki("日清山茶花适合什么甜品使用？");
```

## 知识内容范围

知识库包含以下类型的专业知识：

### 产品信息
- 产品产地
- 成分类型
- 规格包装

### 使用指导
- 储存方式
- 保质期
- 使用建议

### 产品特性
- 口感描述
- 适用场景
- 营养价值

### 常见问题
- 使用方法
- 注意事项
- 替代方案

## 错误处理

1. **配置缺失**：抛出 `IllegalStateException`
2. **网络异常**：返回空列表，记录错误日志
3. **API错误**：抛出 `RuntimeException`
4. **解析异常**：跳过错误项，继续处理其他结果

## 性能优化

1. **连接超时**：10秒
2. **读取超时**：30秒
3. **默认限制**：5000字符
4. **批量处理**：支持单次查询多个结果

## 日志记录

- **INFO级别**：记录请求和响应状态
- **DEBUG级别**：记录详细的请求参数
- **ERROR级别**：记录异常和错误信息
- **WARN级别**：记录警告信息

## 注意事项

1. **API密钥安全**：不要在代码中硬编码API密钥
2. **网络稳定性**：确保网络连接稳定
3. **数据集ID**：确认使用正确的数据集ID
4. **搜索文本**：使用清晰、具体的搜索关键词
