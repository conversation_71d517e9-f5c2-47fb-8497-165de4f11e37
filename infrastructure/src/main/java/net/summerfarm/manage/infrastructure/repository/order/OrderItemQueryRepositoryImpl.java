package net.summerfarm.manage.infrastructure.repository.order;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam;
import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.infrastructure.converter.order.OrderItemConverter;
import net.summerfarm.manage.infrastructure.mapper.order.OrderItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-01-18 15:49:07
* @version 1.0
*
*/
@Repository
public class OrderItemQueryRepositoryImpl implements OrderItemQueryRepository {

    @Autowired
    private OrderItemMapper orderItemMapper;


    @Override
    public PageInfo<OrderItemEntity> getPage(OrderItemQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<OrderItemEntity> entities = orderItemMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public OrderItemEntity selectById(Long id) {
        return OrderItemConverter.toOrderItemEntity(orderItemMapper.selectById(id));
    }


    @Override
    public List<OrderItemEntity> selectByCondition(OrderItemQueryParam param) {
        return OrderItemConverter.toOrderItemEntityList(orderItemMapper.selectByCondition(param));
    }

    @Override
    public Integer selectTimingOrderQuantity(String orderNo) {
        return orderItemMapper.selectTimingOrderQuantity(orderNo);
    }

    @Override
    public List<OrderItemEntity> selectByMasterOrderNo(String masterOrderNo) {
        return  orderItemMapper.selectByMasterOrderNo(masterOrderNo);
    }

    @Override
    public List<OrderItemEntity> selectByOrderNo(String orderNo) {
        return OrderItemConverter.toOrderItemEntityList(orderItemMapper.selectByOrderNo(orderNo));
    }

    @Override
    public List<OrderItemEntity> selectBatchTimingOrderQuantity(List<String> timingOrderNos) {
        return OrderItemConverter.toOrderItemEntityList(orderItemMapper.selectBatchTimingOrderQuantity(timingOrderNos));
    }
}