package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import net.summerfarm.manage.infrastructure.model.merchant.MerchantSubAccount;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-09-19 13:44:23
 * @version 1.0
 *
 */
public class MerchantSubAccountConverter {


    private MerchantSubAccountConverter() {
        // 无需实现
    }

    public static List<MerchantSubAccountEntity> toMerchantSubAccountEntityList(List<MerchantSubAccount> merchantSubAccountList) {
        if (merchantSubAccountList == null) {
            return Collections.emptyList();
        }
        List<MerchantSubAccountEntity> merchantSubAccountEntityList = new ArrayList<>();
        for (MerchantSubAccount merchantSubAccount : merchantSubAccountList) {
            merchantSubAccountEntityList.add(toMerchantSubAccountEntity(merchantSubAccount));
        }
        return merchantSubAccountEntityList;
    }

    public static MerchantSubAccountEntity toMerchantSubAccountEntity(MerchantSubAccount merchantSubAccount) {
        if (merchantSubAccount == null) {
            return null;
        }
        MerchantSubAccountEntity merchantSubAccountEntity = new MerchantSubAccountEntity();
        merchantSubAccountEntity.setAccountId(merchantSubAccount.getAccountId());
        merchantSubAccountEntity.setMId(merchantSubAccount.getMId());
        merchantSubAccountEntity.setType(merchantSubAccount.getType());
        merchantSubAccountEntity.setContact(merchantSubAccount.getContact());
        merchantSubAccountEntity.setPhone(merchantSubAccount.getPhone());
        merchantSubAccountEntity.setUnionid(merchantSubAccount.getUnionid());
        merchantSubAccountEntity.setOpenid(merchantSubAccount.getOpenid());
        merchantSubAccountEntity.setMpOpenid(merchantSubAccount.getMpOpenid());
        merchantSubAccountEntity.setPopView(merchantSubAccount.getPopView());
        merchantSubAccountEntity.setFirstPopView(merchantSubAccount.getFirstPopView());
        merchantSubAccountEntity.setCashAmount(merchantSubAccount.getCashAmount());
        merchantSubAccountEntity.setCashUpdateTime(merchantSubAccount.getCashUpdateTime());
        merchantSubAccountEntity.setLoginTime(merchantSubAccount.getLoginTime());
        merchantSubAccountEntity.setLastOrderTime(merchantSubAccount.getLastOrderTime());
        merchantSubAccountEntity.setStatus(merchantSubAccount.getStatus());
        merchantSubAccountEntity.setDeleteFlag(merchantSubAccount.getDeleteFlag());
        merchantSubAccountEntity.setMInfo(merchantSubAccount.getMInfo());
        merchantSubAccountEntity.setRegisterTime(merchantSubAccount.getRegisterTime());
        merchantSubAccountEntity.setAuditTime(merchantSubAccount.getAuditTime());
        merchantSubAccountEntity.setAuditUser(merchantSubAccount.getAuditUser());
        merchantSubAccountEntity.setUpdateTime(merchantSubAccount.getUpdateTime());
        return merchantSubAccountEntity;
    }

    public static List<MerchantSubAccount> toMerchantSubAccountList(List<MerchantSubAccountEntity> merchantSubAccountEntityList) {
        if (merchantSubAccountEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantSubAccount> merchantSubAccountList = new ArrayList<>();
        for (MerchantSubAccountEntity merchantSubAccountEntity : merchantSubAccountEntityList) {
            merchantSubAccountList.add(toMerchantSubAccount(merchantSubAccountEntity));
        }
        return merchantSubAccountList;
    }

    public static MerchantSubAccount toMerchantSubAccount(MerchantSubAccountEntity merchantSubAccountEntity) {
        if (merchantSubAccountEntity == null) {
            return null;
        }
        MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
        merchantSubAccount.setAccountId(merchantSubAccountEntity.getAccountId());
        merchantSubAccount.setMId(merchantSubAccountEntity.getMId());
        merchantSubAccount.setType(merchantSubAccountEntity.getType());
        merchantSubAccount.setContact(merchantSubAccountEntity.getContact());
        merchantSubAccount.setPhone(merchantSubAccountEntity.getPhone());
        merchantSubAccount.setUnionid(merchantSubAccountEntity.getUnionid());
        merchantSubAccount.setOpenid(merchantSubAccountEntity.getOpenid());
        merchantSubAccount.setMpOpenid(merchantSubAccountEntity.getMpOpenid());
        merchantSubAccount.setPopView(merchantSubAccountEntity.getPopView());
        merchantSubAccount.setFirstPopView(merchantSubAccountEntity.getFirstPopView());
        merchantSubAccount.setCashAmount(merchantSubAccountEntity.getCashAmount());
        merchantSubAccount.setCashUpdateTime(merchantSubAccountEntity.getCashUpdateTime());
        merchantSubAccount.setLoginTime(merchantSubAccountEntity.getLoginTime());
        merchantSubAccount.setLastOrderTime(merchantSubAccountEntity.getLastOrderTime());
        merchantSubAccount.setStatus(merchantSubAccountEntity.getStatus());
        merchantSubAccount.setDeleteFlag(merchantSubAccountEntity.getDeleteFlag());
        merchantSubAccount.setMInfo(merchantSubAccountEntity.getMInfo());
        merchantSubAccount.setRegisterTime(merchantSubAccountEntity.getRegisterTime());
        merchantSubAccount.setAuditTime(merchantSubAccountEntity.getAuditTime());
        merchantSubAccount.setAuditUser(merchantSubAccountEntity.getAuditUser());
        merchantSubAccount.setUpdateTime(merchantSubAccountEntity.getUpdateTime());
        return merchantSubAccount;
    }
}
