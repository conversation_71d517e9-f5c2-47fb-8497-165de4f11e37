package net.summerfarm.manage.infrastructure.mapper.order;

import net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord;
import net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-10-15 17:49:41
 * @version 1.0
 *
 */
@Mapper
public interface WxShippingInfoUploadRecordMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WxShippingInfoUploadRecord record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WxShippingInfoUploadRecord record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WxShippingInfoUploadRecord selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WxShippingInfoUploadRecord> selectByCondition(WxShippingInfoUploadRecordQueryParam param);


    /**
     * @Describe:
     * @param param
     * @return
     */
    WxShippingInfoUploadRecordEntity selectByMasterOrderNo(@Param("masterOrderNo") String masterOrderNo);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WxShippingInfoUploadRecordEntity> getPage(WxShippingInfoUploadRecordQueryParam param);
}

