package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantCancel;
import net.summerfarm.manage.domain.merchant.entity.MerchantCancelEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCancelCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2023-12-27 14:01:49
 * @version 1.0
 *
 */
public class MerchantCancelConverter {

    private MerchantCancelConverter() {
        // 无需实现
    }




    public static List<MerchantCancelEntity> toMerchantCancelEntityList(List<MerchantCancel> merchantCancelList) {
        if (merchantCancelList == null) {
            return Collections.emptyList();
        }
        List<MerchantCancelEntity> merchantCancelEntityList = new ArrayList<>();
        for (MerchantCancel merchantCancel : merchantCancelList) {
            merchantCancelEntityList.add(toMerchantCancelEntity(merchantCancel));
        }
        return merchantCancelEntityList;
}


    public static MerchantCancelEntity toMerchantCancelEntity(MerchantCancel merchantCancel) {
        if (merchantCancel == null) {
             return null;
        }
        MerchantCancelEntity merchantCancelEntity = new MerchantCancelEntity();
        merchantCancelEntity.setId(merchantCancel.getId());
        merchantCancelEntity.setMId(merchantCancel.getMId());
        merchantCancelEntity.setStatus(merchantCancel.getStatus());
        merchantCancelEntity.setRemake(merchantCancel.getRemake());
        merchantCancelEntity.setCertificate(merchantCancel.getCertificate());
        merchantCancelEntity.setCreator(merchantCancel.getCreator());
        merchantCancelEntity.setCreateTime(merchantCancel.getCreateTime());
        merchantCancelEntity.setUpdateTime(merchantCancel.getUpdateTime());
        merchantCancelEntity.setUpdater(merchantCancel.getUpdater());
        merchantCancelEntity.setResource(merchantCancel.getResource());
        merchantCancelEntity.setPhone(merchantCancel.getPhone());
        merchantCancelEntity.setMname(merchantCancel.getMname());
        merchantCancelEntity.setAreaNo(merchantCancel.getAreaNo());
        return merchantCancelEntity;
    }








    public static MerchantCancel toMerchantCancel(MerchantCancelCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantCancel merchantCancel = new MerchantCancel();
        merchantCancel.setId(param.getId());
        merchantCancel.setMId(param.getMId());
        merchantCancel.setStatus(param.getStatus());
        merchantCancel.setRemake(param.getRemake());
        merchantCancel.setCertificate(param.getCertificate());
        merchantCancel.setCreator(param.getCreator());
        merchantCancel.setCreateTime(param.getCreateTime());
        merchantCancel.setUpdateTime(param.getUpdateTime());
        merchantCancel.setUpdater(param.getUpdater());
        merchantCancel.setResource(param.getResource());
        merchantCancel.setPhone(param.getPhone());
        merchantCancel.setMname(param.getMname());
        merchantCancel.setAreaNo(param.getAreaNo());
        return merchantCancel;
    }
}
