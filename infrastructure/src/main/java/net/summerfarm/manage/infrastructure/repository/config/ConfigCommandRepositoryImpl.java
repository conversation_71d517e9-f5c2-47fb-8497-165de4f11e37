package net.summerfarm.manage.infrastructure.repository.config;

import net.summerfarm.manage.infrastructure.model.config.Config;
import net.summerfarm.manage.infrastructure.mapper.config.ConfigMapper;
import net.summerfarm.manage.infrastructure.converter.config.ConfigConverter;
import net.summerfarm.manage.domain.config.repository.ConfigCommandRepository;
import net.summerfarm.manage.domain.config.entity.ConfigEntity;
import net.summerfarm.manage.domain.config.param.command.ConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2024-06-18 23:48:54
* @version 1.0
*
*/
@Repository
public class ConfigCommandRepositoryImpl implements ConfigCommandRepository {

    @Autowired
    private ConfigMapper configMapper;
    @Override
    public ConfigEntity insertSelective(ConfigCommandParam param) {
        Config config = ConfigConverter.toConfig(param);
        configMapper.insertSelective(config);
        return ConfigConverter.toConfigEntity(config);
    }

    @Override
    public int updateSelectiveById(ConfigCommandParam param){
        return configMapper.updateSelectiveById(ConfigConverter.toConfig(param));
    }


    @Override
    public int remove(Long id) {
        return configMapper.remove(id);
    }
}