package net.summerfarm.manage.infrastructure.repository.major;

import net.summerfarm.manage.infrastructure.model.major.MajorPriceLog;
import net.summerfarm.manage.infrastructure.mapper.major.MajorPriceLogMapper;
import net.summerfarm.manage.infrastructure.converter.major.MajorPriceLogConverter;
import net.summerfarm.manage.domain.major.repository.MajorPriceLogCommandRepository;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.command.MajorPriceLogCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-02-19 11:19:11
* @version 1.0
*
*/
@Repository
public class MajorPriceLogCommandRepositoryImpl implements MajorPriceLogCommandRepository {

    @Autowired
    private MajorPriceLogMapper majorPriceLogMapper;
    @Override
    public MajorPriceLogEntity insertSelective(MajorPriceLogCommandParam param) {
        MajorPriceLog majorPriceLog = MajorPriceLogConverter.toMajorPriceLog(param);
        majorPriceLogMapper.insertSelective(majorPriceLog);
        return MajorPriceLogConverter.toMajorPriceLogEntity(majorPriceLog);
    }

    @Override
    public int insertBatch(List<MajorPriceLogCommandParam> list) {
        return majorPriceLogMapper.insertList(MajorPriceLogConverter.toMajorPriceLogList(list));
    }

    @Override
    public int updateSelectiveById(MajorPriceLogCommandParam param){
        return majorPriceLogMapper.updateSelectiveById(MajorPriceLogConverter.toMajorPriceLog(param));
    }


    @Override
    public int remove(Long id) {
        return majorPriceLogMapper.remove(id);
    }
}