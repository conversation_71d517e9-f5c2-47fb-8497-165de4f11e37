package net.summerfarm.manage.infrastructure.mapper.account;

import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.infrastructure.model.account.ContactAdjust;
import net.summerfarm.manage.common.input.account.ContactAdjustQueryInput;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
@Mapper
public interface ContactAdjustMapper{

    int insertSelective(ContactAdjust record);

    int updateByIdSelective(ContactAdjust record);

    int remove(@Param("id") Long id);

    ContactAdjust selectById(@Param("id") Long id);

    List<ContactAdjust> selectByCondition(MerchantQueryInput record);

    ContactAdjust selectOne(ContactAdjust record);

}
