package net.summerfarm.manage.infrastructure.repository.product;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.product.param.command.AreaSkuOnSaleParam;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuCommandRepository;
import net.summerfarm.manage.infrastructure.mapper.product.AreaSkuMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class AreaSkuCommandRepositoryImpl implements AreaSkuCommandRepository {
    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Override
    public Integer updateAreaSkuPrice(List<AreaSkuPriceCommandParam> areaSkuPriceS) {
        return areaSkuMapper.updateAreaSkuPrice(areaSkuPriceS);
    }

    @Override
    public Integer updateAreaSkuOnSale(AreaSkuOnSaleParam areaSkuOnSaleParam) {
        return areaSkuMapper.updateAreaSkuPopOnSale(areaSkuOnSaleParam);
    }

    @Override
    public void updateAreaSkuOnSaleBatch(List<AreaSkuOnSaleParam> areaSkuOnSaleParams) {
        areaSkuMapper.updateAreaSkuOnSaleBatch(areaSkuOnSaleParams);
    }

    @Override
    public void offSaleByAreaNos(Set<Integer> areaNos) {
        areaSkuMapper.offSaleByAreaNos(areaNos);
    }
}
