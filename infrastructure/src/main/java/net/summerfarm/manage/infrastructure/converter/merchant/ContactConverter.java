package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.domain.merchant.entity.ContactEntity;
import net.summerfarm.manage.infrastructure.model.merchant.Contact;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-09-19 16:21:24
 * @version 1.0
 *
 */
public class ContactConverter {


    private ContactConverter() {
        // 无需实现
    }

    public static List<ContactEntity> toContactEntityList(List<Contact> contactList) {
        if (contactList == null) {
            return Collections.emptyList();
        }
        List<ContactEntity> contactEntityList = new ArrayList<>();
        for (Contact contact : contactList) {
            contactEntityList.add(toContactEntity(contact));
        }
        return contactEntityList;
    }

    public static ContactEntity toContactEntity(Contact contact) {
        if (contact == null) {
            return null;
        }
        ContactEntity contactEntity = new ContactEntity();
        contactEntity.setContactId(contact.getContactId());
        contactEntity.setMId(contact.getMId());
        contactEntity.setContact(contact.getContact());
        contactEntity.setPosition(contact.getPosition());
        contactEntity.setGender(contact.getGender());
        contactEntity.setPhone(contact.getPhone());
        contactEntity.setEmail(contact.getEmail());
        contactEntity.setWeixincode(contact.getWeixincode());
        contactEntity.setProvince(contact.getProvince());
        contactEntity.setCity(contact.getCity());
        contactEntity.setArea(contact.getArea());
        contactEntity.setAddress(contact.getAddress());
        contactEntity.setDeliveryCar(contact.getDeliveryCar());
        contactEntity.setStatus(contact.getStatus());
        contactEntity.setRemark(contact.getRemark());
        contactEntity.setIsDefault(contact.getIsDefault());
        contactEntity.setPoiNote(contact.getPoiNote());
        contactEntity.setDistance(contact.getDistance());
        contactEntity.setPath(contact.getPath());
        contactEntity.setHouseNumber(contact.getHouseNumber());
        contactEntity.setCreateTime(contact.getCreateTime());
        contactEntity.setUpdateTime(contact.getUpdateTime());
        contactEntity.setStoreNo(contact.getStoreNo());
        contactEntity.setAcmId(contact.getAcmId());
        contactEntity.setBackStoreNo(contact.getBackStoreNo());
        contactEntity.setDeliveryFrequent(contact.getDeliveryFrequent());
        contactEntity.setDeliveryRule(contact.getDeliveryRule());
        contactEntity.setDeliveryFee(contact.getDeliveryFee());
        contactEntity.setAddressRemark(contact.getAddressRemark());
        return contactEntity;
    }

    public static List<Contact> toContactList(List<ContactEntity> contactEntityList) {
        if (contactEntityList == null) {
            return Collections.emptyList();
        }
        List<Contact> contactList = new ArrayList<>();
        for (ContactEntity contactEntity : contactEntityList) {
            contactList.add(toContact(contactEntity));
        }
        return contactList;
    }

    public static Contact toContact(ContactEntity contactEntity) {
        if (contactEntity == null) {
            return null;
        }
        Contact contact = new Contact();
        contact.setContactId(contactEntity.getContactId());
        contact.setMId(contactEntity.getMId());
        contact.setContact(contactEntity.getContact());
        contact.setPosition(contactEntity.getPosition());
        contact.setGender(contactEntity.getGender());
        contact.setPhone(contactEntity.getPhone());
        contact.setEmail(contactEntity.getEmail());
        contact.setWeixincode(contactEntity.getWeixincode());
        contact.setProvince(contactEntity.getProvince());
        contact.setCity(contactEntity.getCity());
        contact.setArea(contactEntity.getArea());
        contact.setAddress(contactEntity.getAddress());
        contact.setDeliveryCar(contactEntity.getDeliveryCar());
        contact.setStatus(contactEntity.getStatus());
        contact.setRemark(contactEntity.getRemark());
        contact.setIsDefault(contactEntity.getIsDefault());
        contact.setPoiNote(contactEntity.getPoiNote());
        contact.setDistance(contactEntity.getDistance());
        contact.setPath(contactEntity.getPath());
        contact.setHouseNumber(contactEntity.getHouseNumber());
        contact.setCreateTime(contactEntity.getCreateTime());
        contact.setUpdateTime(contactEntity.getUpdateTime());
        contact.setStoreNo(contactEntity.getStoreNo());
        contact.setAcmId(contactEntity.getAcmId());
        contact.setBackStoreNo(contactEntity.getBackStoreNo());
        contact.setDeliveryFrequent(contactEntity.getDeliveryFrequent());
        contact.setDeliveryRule(contactEntity.getDeliveryRule());
        contact.setDeliveryFee(contactEntity.getDeliveryFee());
        contact.setAddressRemark(contactEntity.getAddressRemark());
        return contact;
    }
}
