package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryGroupByLargeAreaParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationCategoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationProductQueryParam;
import net.summerfarm.manage.infrastructure.model.product.Inventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Mapper
public interface InventoryMapper {

    List<Inventory> selectByIds(@Param("invIds") List<Long> invIds);

    List<ProductEntity> listByQuery(ProductPageQueryParam queryParam);

    Inventory queryBySku(@Param("sku") String sku);

    List<ProductEntity> selectPage(ProductPageQueryParam param);

    List<Inventory> queryByPdid(InventoryQueryParam param);

    Inventory querySelectOne(InventoryQueryParam queryParam);

    int insertSelective(Inventory inventory);

    int updateBySku(Inventory paramToInventory);
    int updateWithNull(Inventory paramToInventory);

    List<Inventory> selectByPdId(@Param("pdId") Long pdId);

    List<Inventory> selectList(InventoryQueryParam query);

    List<Inventory> listBySkus(@Param("skus") List<String> skus);

    List<InventoryEntity> queryInfo(InventoryQueryParam param);

    List<InvetoryGroupByLargeAreaNoEntity> listByParamGroupByLargeArea(InventoryQueryGroupByLargeAreaParam param);

    List<String> querySkusByNameLike(@Param("spuTitleLike")String spuTitleLike);

    List<InventoryEntity> selectByCondition(InventoryQueryParam inventoryQueryParam);

    List<PendingAssociationProductEntity> selectPendingAssociationProductList(PendingAssociationProductQueryParam queryParam);

    List<CategoryEntity> listPendingAssociationCategory(PendingAssociationCategoryQueryParam queryParam);

    /**
     * 查询商品基础数据用于AI问题生成
     * @param sku SKU编码
     * @return 商品基础数据
     */
    List<ProductBasicInfoEntity> queryProductBasicInfoForAI(@Param("sku") String sku);

    /**
     * 批量查询商品基础数据用于AI问题生成
     * @param skus SKU编码列表
     * @return 商品基础数据列表
     */
    List<ProductBasicInfoEntity> batchQueryProductBasicInfoForAI(@Param("skus") List<String> skus);
}
