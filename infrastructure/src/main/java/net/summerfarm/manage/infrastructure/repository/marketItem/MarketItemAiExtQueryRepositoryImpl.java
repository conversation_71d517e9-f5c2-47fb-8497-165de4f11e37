package net.summerfarm.manage.infrastructure.repository.marketItem;


import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt;
import net.summerfarm.manage.infrastructure.mapper.marketItem.MarketItemAiExtMapper;
import net.summerfarm.manage.infrastructure.converter.marketItem.MarketItemAiExtConverter;
import net.summerfarm.manage.domain.marketItem.repository.MarketItemAiExtQueryRepository;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-07-03 16:33:54
* @version 1.0
*
*/
@Repository
public class MarketItemAiExtQueryRepositoryImpl implements MarketItemAiExtQueryRepository {

    @Autowired
    private MarketItemAiExtMapper marketItemAiExtMapper;


    @Override
    public PageInfo<MarketItemAiExtEntity> getPage(MarketItemAiExtQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MarketItemAiExtEntity> entities = marketItemAiExtMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MarketItemAiExtEntity selectById(Long id) {
        return MarketItemAiExtConverter.toMarketItemAiExtEntity(marketItemAiExtMapper.selectById(id));
    }


    @Override
    public List<MarketItemAiExtEntity> selectByCondition(MarketItemAiExtQueryParam param) {
        return MarketItemAiExtConverter.toMarketItemAiExtEntityList(marketItemAiExtMapper.selectByCondition(param));
    }

    @Override
    public List<MarketItemAiExtEntity> selectBySkuAndExtType(String sku, Integer extType) {
        if (StringUtils.isEmpty(sku) || extType == null) {
            return Collections.emptyList();
        }

        MarketItemAiExtQueryParam queryParam = new MarketItemAiExtQueryParam();
        queryParam.setSku(sku);
        queryParam.setExtType(extType);
        return selectByCondition(queryParam);
    }

    @Override
    public List<MarketItemAiExtEntity> batchSelectBySkusAndExtType(List<String> skus, Integer extType) {
        if (CollectionUtils.isEmpty(skus) || extType == null) {
            return Collections.emptyList();
        }

        MarketItemAiExtQueryParam queryParam = new MarketItemAiExtQueryParam();
        queryParam.setSkus(skus);
        queryParam.setExtType(extType);
        return selectByCondition(queryParam);
    }

}