package net.summerfarm.manage.infrastructure.mapper.trade;

import net.summerfarm.manage.infrastructure.model.trade.TimingOrderRefundTime;
import net.summerfarm.manage.domain.trade.param.query.TimingOrderRefundTimeQueryParam;
import net.summerfarm.manage.domain.trade.entity.TimingOrderRefundTimeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-22 14:45:54
 * @version 1.0
 *
 */
@Mapper
public interface TimingOrderRefundTimeMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(TimingOrderRefundTime record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(TimingOrderRefundTime record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    TimingOrderRefundTime selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<TimingOrderRefundTime> selectByCondition(TimingOrderRefundTimeQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<TimingOrderRefundTimeEntity> getPage(TimingOrderRefundTimeQueryParam param);

    /**
     * @Describe: 根据订单号删除省心送退款记录
     * @param orderNo
     * @return
     */
    int deleteTimeOrderRefund(String orderNo);
}

