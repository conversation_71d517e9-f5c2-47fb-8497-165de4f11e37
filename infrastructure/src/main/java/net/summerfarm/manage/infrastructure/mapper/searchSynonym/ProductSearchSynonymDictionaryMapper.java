package net.summerfarm.manage.infrastructure.mapper.searchSynonym;

import net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary;
import net.summerfarm.manage.domain.searchSynonym.param.query.ProductSearchSynonymDictionaryQueryParam;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
@Mapper
public interface ProductSearchSynonymDictionaryMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ProductSearchSynonymDictionary record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ProductSearchSynonymDictionary record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Integer id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ProductSearchSynonymDictionary selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<ProductSearchSynonymDictionary> selectByCondition(ProductSearchSynonymDictionaryQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ProductSearchSynonymDictionaryEntity> getPage(ProductSearchSynonymDictionaryQueryParam param);
}

