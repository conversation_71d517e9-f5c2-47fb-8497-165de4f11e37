package net.summerfarm.manage.infrastructure.repository.product;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.domain.product.entity.*;
import net.summerfarm.manage.domain.product.param.ProductPageQueryParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryGroupByLargeAreaParam;
import net.summerfarm.manage.domain.product.param.query.InventoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationCategoryQueryParam;
import net.summerfarm.manage.domain.product.param.query.PendingAssociationProductQueryParam;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.infrastructure.converter.product.InventoryConverter;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryMapper;
import net.summerfarm.manage.infrastructure.model.product.Inventory;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
@Slf4j
@Component
public class InventoryQueryRepositoryImpl implements InventoryQueryRepository {

    @Resource
    private InventoryMapper inventoryMapper;

    public List<InventoryEntity> selectByIds(List<Long> invIds) {
        List<Inventory> inventoryList = inventoryMapper.selectByIds(invIds);
        return InventoryConverter.toInventoryEntityList(inventoryList);
    }

    public PageInfo<ProductEntity> listByQuery(ProductPageQueryParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<ProductEntity> entityList = inventoryMapper.listByQuery(param);
        if (CollectionUtil.isEmpty(entityList)) {
            return new PageInfo<>();
        }
        return new PageInfo<>(entityList);
    }

    @Override
    public List<ProductEntity> listProductEntityByQuery(ProductPageQueryParam param) {
        return inventoryMapper.listByQuery(param);
    }

    @Override
    public InventoryEntity queryBySku(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }
        Inventory inventory = inventoryMapper.queryBySku(sku);
        return InventoryConverter.toInventoryEntity(inventory);
    }

    @Override
    public PageInfo<ProductEntity> selectPage(ProductPageQueryParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<ProductEntity> entityList = inventoryMapper.selectPage(param);
        if (CollectionUtil.isEmpty(entityList)) {
            return new PageInfo<>();
        }

        //封装sku数据
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setOutdated(param.getOutdated());
        queryParam.setSku(param.getSku());
        queryParam.setSubType(param.getSubType());
        queryParam.setType(param.getType());
        queryParam.setBuyerId (param.getBuyerId ());
        List<Long> pdIds = entityList.stream().map(ProductEntity::getPdId).collect(Collectors.toList());
        queryParam.setPdIds(pdIds);
        List<Inventory> inventoryList = inventoryMapper.queryByPdid(queryParam);
        if (!CollectionUtil.isEmpty(inventoryList)) {
            Map<Long, List<Inventory>> inventoryMap = inventoryList.stream().collect(Collectors.groupingBy(Inventory::getPdId));
            entityList.stream().forEach(productEntity -> {
                if (inventoryMap.containsKey(productEntity.getPdId())) {
                    List<InventoryEntity> inventoryEntities = InventoryConverter.toInventoryEntityList(
                            inventoryMap.get(productEntity.getPdId()));
                    productEntity.setInventoryList(inventoryEntities);
                }
            });
        }
        return new PageInfo<>(entityList);
    }

    @Override
    public InventoryEntity querySelectOne(InventoryQueryParam queryParam) {
        Inventory inventory = inventoryMapper.querySelectOne(queryParam);
        InventoryEntity inventoryEntity = InventoryConverter.toInventoryEntity(inventory);
        return inventoryEntity;
    }

    @Override
    public List<InventoryEntity> selectByPdId(Long pdId) {
        List<Inventory> inventoryList = inventoryMapper.selectByPdId(pdId);
        return InventoryConverter.toInventoryEntityList(inventoryList);
    }

    @Override
    public List<InventoryEntity> selectList(InventoryQueryParam query) {
        List<Inventory> inventoryList = inventoryMapper.selectList(query);
        return InventoryConverter.toInventoryEntityList(inventoryList);
    }

    @Override
    public List<InventoryEntity> listBySkus(List<String> skus) {
        List<Inventory> inventoryList = inventoryMapper.listBySkus(skus);
        return InventoryConverter.toInventoryEntityList(inventoryList);
    }

    @Override
    public List<InventoryEntity> queryInfo(InventoryQueryParam param) {
        return inventoryMapper.queryInfo(param);
    }

    @Override
    public PageInfo<InvetoryGroupByLargeAreaNoEntity> listByParamGroupByLargeArea(InventoryQueryGroupByLargeAreaParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<InvetoryGroupByLargeAreaNoEntity> entityList = inventoryMapper.listByParamGroupByLargeArea(param);
        if (CollectionUtil.isEmpty(entityList)) {
            return new PageInfo<>();
        }
        return new PageInfo<>(entityList);
    }

    @Override
    public List<String> querySkusByNameLike(String spuTitleLike) {
        return inventoryMapper.querySkusByNameLike(spuTitleLike);
    }

    @Override
    public List<ProductBasicInfoEntity> batchQueryProductBasicInfoForAI(List<String> skus) {
        if (CollectionUtil.isEmpty(skus)) {
            return Lists.newArrayList();
        }
        return inventoryMapper.batchQueryProductBasicInfoForAI(skus);
    }

    @Override
    public List<String> querySkusNeedAiQuestions(Integer pageNo, Integer pageSize) {
        PageInfo<String> result = PageHelper.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> inventoryMapper.querySkusNeedAiQuestions());
        if (Objects.isNull(result)) {
            return Lists.newArrayList();
        }
        return result.getList();
    }

    @Override
    public PageInfo<InventoryEntity> pageQueryInventory(InventoryQueryParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<InventoryEntity> inventoryEntityList = inventoryMapper.selectByCondition(param);
        if (CollectionUtil.isEmpty(inventoryEntityList)) {
            return new PageInfo<>();
        }
        return new PageInfo<>(inventoryEntityList);
    }

    @Override
    public PageInfo<PendingAssociationProductEntity> pagePendingAssociationProduct(PendingAssociationProductQueryParam queryParam) {
        // 参数校验 queryParam为空，或者pageIndex为空或者pageSize为空，则返回空
        if (queryParam == null || queryParam.getPageIndex() == null
                || queryParam.getPageSize() == null || queryParam.getWarehouseNo() == null) {
            return new PageInfo<>(Lists.newArrayList());
        }
        // 分页查询selectPendingAssociationProductList
        return PageHelper.startPage(queryParam.getPageIndex(), queryParam.getPageSize())
                .doSelectPageInfo(() -> inventoryMapper.selectPendingAssociationProductList(queryParam));
    }

    @Override
    public List<CategoryEntity> listPendingAssociationCategory(PendingAssociationCategoryQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return Collections.emptyList();
        }
        return inventoryMapper.listPendingAssociationCategory(queryParam);
    }
}
