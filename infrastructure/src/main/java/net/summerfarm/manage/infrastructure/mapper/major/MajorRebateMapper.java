package net.summerfarm.manage.infrastructure.mapper.major;

import net.summerfarm.manage.infrastructure.model.major.MajorRebate;
import net.summerfarm.manage.domain.major.param.query.MajorRebateQueryParam;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-02-27 15:22:25
 * @version 1.0
 *
 */
@Mapper
public interface MajorRebateMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MajorRebate record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(MajorRebate record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    MajorRebate selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<MajorRebate> selectByCondition(MajorRebateQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<MajorRebateEntity> getPage(MajorRebateQueryParam param);

    List<MajorRebateEntity> selectList(@Param("adminId") Integer adminId,
                                 @Param("areaNos") List<Integer> areaNo,
                                 @Param("cate") Integer cate,
                                 @Param("sku") String sku);
}

