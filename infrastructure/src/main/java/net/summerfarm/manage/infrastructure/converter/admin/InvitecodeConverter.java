package net.summerfarm.manage.infrastructure.converter.admin;

import net.summerfarm.manage.infrastructure.model.admin.Invitecode;
import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import net.summerfarm.manage.domain.admin.param.command.InvitecodeCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-06-18 13:07:30
 * @version 1.0
 *
 */
public class InvitecodeConverter {

    private InvitecodeConverter() {
        // 无需实现
    }




    public static List<InvitecodeEntity> toInvitecodeEntityList(List<Invitecode> invitecodeList) {
        if (invitecodeList == null) {
            return Collections.emptyList();
        }
        List<InvitecodeEntity> invitecodeEntityList = new ArrayList<>();
        for (Invitecode invitecode : invitecodeList) {
            invitecodeEntityList.add(toInvitecodeEntity(invitecode));
        }
        return invitecodeEntityList;
}


    public static InvitecodeEntity toInvitecodeEntity(Invitecode invitecode) {
        if (invitecode == null) {
             return null;
        }
        InvitecodeEntity invitecodeEntity = new InvitecodeEntity();
        invitecodeEntity.setInviteId(invitecode.getInviteId());
        invitecodeEntity.setInvitecode(invitecode.getInvitecode());
        invitecodeEntity.setAdminId(invitecode.getAdminId());
        invitecodeEntity.setCreateTime(invitecode.getCreateTime());
        invitecodeEntity.setStatus(invitecode.getStatus());
        return invitecodeEntity;
    }








    public static Invitecode toInvitecode(InvitecodeCommandParam param) {
        if (param == null) {
            return null;
        }
        Invitecode invitecode = new Invitecode();
        invitecode.setInviteId(param.getInviteId());
        invitecode.setInvitecode(param.getInvitecode());
        invitecode.setAdminId(param.getAdminId());
        invitecode.setCreateTime(param.getCreateTime());
        invitecode.setStatus(param.getStatus());
        return invitecode;
    }
}
