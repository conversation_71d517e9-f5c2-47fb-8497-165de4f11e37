package net.summerfarm.manage.infrastructure.converter.marketItem;

import net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt;
import net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity;
import net.summerfarm.manage.domain.marketItem.param.command.MarketItemAiExtCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
public class MarketItemAiExtConverter {

    private MarketItemAiExtConverter() {
        // 无需实现
    }




    public static List<MarketItemAiExtEntity> toMarketItemAiExtEntityList(List<MarketItemAiExt> marketItemAiExtList) {
        if (marketItemAiExtList == null) {
            return Collections.emptyList();
        }
        List<MarketItemAiExtEntity> marketItemAiExtEntityList = new ArrayList<>();
        for (MarketItemAiExt marketItemAiExt : marketItemAiExtList) {
            marketItemAiExtEntityList.add(toMarketItemAiExtEntity(marketItemAiExt));
        }
        return marketItemAiExtEntityList;
}


    public static MarketItemAiExtEntity toMarketItemAiExtEntity(MarketItemAiExt marketItemAiExt) {
        if (marketItemAiExt == null) {
             return null;
        }
        MarketItemAiExtEntity marketItemAiExtEntity = new MarketItemAiExtEntity();
        marketItemAiExtEntity.setId(marketItemAiExt.getId());
        marketItemAiExtEntity.setCreateTime(marketItemAiExt.getCreateTime());
        marketItemAiExtEntity.setUpdateTime(marketItemAiExt.getUpdateTime());
        marketItemAiExtEntity.setTenantId(marketItemAiExt.getTenantId());
        marketItemAiExtEntity.setSku(marketItemAiExt.getSku());
        marketItemAiExtEntity.setPdId(marketItemAiExt.getPdId());
        marketItemAiExtEntity.setExtType(marketItemAiExt.getExtType());
        marketItemAiExtEntity.setExtValue(marketItemAiExt.getExtValue());
        return marketItemAiExtEntity;
    }








    public static MarketItemAiExt toMarketItemAiExt(MarketItemAiExtCommandParam param) {
        if (param == null) {
            return null;
        }
        MarketItemAiExt marketItemAiExt = new MarketItemAiExt();
        marketItemAiExt.setId(param.getId());
        marketItemAiExt.setCreateTime(param.getCreateTime());
        marketItemAiExt.setUpdateTime(param.getUpdateTime());
        marketItemAiExt.setTenantId(param.getTenantId());
        marketItemAiExt.setSku(param.getSku());
        marketItemAiExt.setPdId(param.getPdId());
        marketItemAiExt.setExtType(param.getExtType());
        marketItemAiExt.setExtValue(param.getExtValue());
        return marketItemAiExt;
    }
}
