package net.summerfarm.manage.infrastructure.converter.order;

import net.summerfarm.manage.infrastructure.model.order.AfterSaleProof;
import net.summerfarm.manage.domain.order.entity.AfterSaleProofEntity;
import net.summerfarm.manage.domain.order.param.command.AfterSaleProofCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-01-18 16:27:13
 * @version 1.0
 *
 */
public class AfterSaleProofConverter {

    private AfterSaleProofConverter() {
        // 无需实现
    }




    public static List<AfterSaleProofEntity> toAfterSaleProofEntityList(List<AfterSaleProof> afterSaleProofList) {
        if (afterSaleProofList == null) {
            return Collections.emptyList();
        }
        List<AfterSaleProofEntity> afterSaleProofEntityList = new ArrayList<>();
        for (AfterSaleProof afterSaleProof : afterSaleProofList) {
            afterSaleProofEntityList.add(toAfterSaleProofEntity(afterSaleProof));
        }
        return afterSaleProofEntityList;
}


    public static AfterSaleProofEntity toAfterSaleProofEntity(AfterSaleProof afterSaleProof) {
        if (afterSaleProof == null) {
             return null;
        }
        AfterSaleProofEntity afterSaleProofEntity = new AfterSaleProofEntity();
        afterSaleProofEntity.setId(afterSaleProof.getId());
        afterSaleProofEntity.setAfterSaleOrderNo(afterSaleProof.getAfterSaleOrderNo());
        afterSaleProofEntity.setQuantity(afterSaleProof.getQuantity());
        afterSaleProofEntity.setHandleNum(afterSaleProof.getHandleNum());
        afterSaleProofEntity.setProofPic(afterSaleProof.getProofPic());
        afterSaleProofEntity.setAfterSaleType(afterSaleProof.getAfterSaleType());
        afterSaleProofEntity.setRefundType(afterSaleProof.getRefundType());
        afterSaleProofEntity.setHandleType(afterSaleProof.getHandleType());
        afterSaleProofEntity.setHandler(afterSaleProof.getHandler());
        afterSaleProofEntity.setHandleRemark(afterSaleProof.getHandleRemark());
        afterSaleProofEntity.setAuditer(afterSaleProof.getAuditer());
        afterSaleProofEntity.setStatus(afterSaleProof.getStatus());
        afterSaleProofEntity.setApplyRemark(afterSaleProof.getApplyRemark());
        afterSaleProofEntity.setUpdatetime(afterSaleProof.getUpdatetime());
        afterSaleProofEntity.setApplyer(afterSaleProof.getApplyer());
        afterSaleProofEntity.setAuditeRemark(afterSaleProof.getAuditeRemark());
        afterSaleProofEntity.setExtraRemark(afterSaleProof.getExtraRemark());
        afterSaleProofEntity.setAuditetime(afterSaleProof.getAuditetime());
        afterSaleProofEntity.setHandletime(afterSaleProof.getHandletime());
        afterSaleProofEntity.setRecoveryNum(afterSaleProof.getRecoveryNum());
        afterSaleProofEntity.setCreateTime(afterSaleProof.getCreateTime());
        afterSaleProofEntity.setHandleSecondaryRemark(afterSaleProof.getHandleSecondaryRemark());
        afterSaleProofEntity.setApplySecondaryRemark(afterSaleProof.getApplySecondaryRemark());
        afterSaleProofEntity.setProofVideo(afterSaleProof.getProofVideo());
        return afterSaleProofEntity;
    }








    public static AfterSaleProof toAfterSaleProof(AfterSaleProofCommandParam param) {
        if (param == null) {
            return null;
        }
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setId(param.getId());
        afterSaleProof.setAfterSaleOrderNo(param.getAfterSaleOrderNo());
        afterSaleProof.setQuantity(param.getQuantity());
        afterSaleProof.setHandleNum(param.getHandleNum());
        afterSaleProof.setProofPic(param.getProofPic());
        afterSaleProof.setAfterSaleType(param.getAfterSaleType());
        afterSaleProof.setRefundType(param.getRefundType());
        afterSaleProof.setHandleType(param.getHandleType());
        afterSaleProof.setHandler(param.getHandler());
        afterSaleProof.setHandleRemark(param.getHandleRemark());
        afterSaleProof.setAuditer(param.getAuditer());
        afterSaleProof.setStatus(param.getStatus());
        afterSaleProof.setApplyRemark(param.getApplyRemark());
        afterSaleProof.setUpdatetime(param.getUpdatetime());
        afterSaleProof.setApplyer(param.getApplyer());
        afterSaleProof.setAuditeRemark(param.getAuditeRemark());
        afterSaleProof.setExtraRemark(param.getExtraRemark());
        afterSaleProof.setAuditetime(param.getAuditetime());
        afterSaleProof.setHandletime(param.getHandletime());
        afterSaleProof.setRecoveryNum(param.getRecoveryNum());
        afterSaleProof.setCreateTime(param.getCreateTime());
        afterSaleProof.setHandleSecondaryRemark(param.getHandleSecondaryRemark());
        afterSaleProof.setApplySecondaryRemark(param.getApplySecondaryRemark());
        afterSaleProof.setProofVideo(param.getProofVideo());
        return afterSaleProof;
    }
}
