package net.summerfarm.manage.infrastructure.model.payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-10-11 14:22:49
 * @version 1.0
 *
 */
@Data
public class MasterPayment {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 支付类型
	 */
	private String payType;

	/**
	 * 主支付单号
	 */
	private String masterOrderNo;

	/**
	 * 交易号
	 */
	private String transactionNumber;

	/**
	 * 支付金额
	 */
	private BigDecimal money;

	/**
	 * 支付完成时间
	 */
	private LocalDateTime endTime;

	/**
	 * 支付名称
	 */
	private String tradeType;

	/**
	 * 支付方式
	 */
	private String bankType;

	/**
	 * 支付状态
	 */
	private Integer status;

	/**
	 * 支付错误代码
	 */
	private String errCode;

	/**
	 * 支付错误描述
	 */
	private String errCodeDes;

	/**
	 * 支付账号id
	 */
	private Integer companyAccountId;

	/**
	 * Boc支付二维码
	 */
	private String scanCode;

	/**
	 * 支付账号信息
	 */
	private String accountInfo;

	/**
	 * Boc支付类型
	 */
	private String bocPayType;

	/**
	 * 线上支付完成时间
	 */
	private LocalDateTime onlinePayEndTime;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;




}