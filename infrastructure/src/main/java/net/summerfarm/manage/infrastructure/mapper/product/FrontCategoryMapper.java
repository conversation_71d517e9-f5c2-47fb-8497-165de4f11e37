package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.infrastructure.model.product.FrontCategory;
import net.summerfarm.manage.domain.product.param.query.FrontCategoryQueryParam;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-03-27 15:26:47
 * @version 1.0
 *
 */
@Mapper
public interface FrontCategoryMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(FrontCategory record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(FrontCategory record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Integer id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    FrontCategory selectById(@Param("id") Integer id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<FrontCategory> selectByCondition(FrontCategoryQueryParam param);


    List<FrontCategoryEntity> selectAllPopCategory();

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<FrontCategoryEntity> getPage(FrontCategoryQueryParam param);
}

