package net.summerfarm.manage.infrastructure.converter.merchantpool;

import net.summerfarm.manage.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.summerfarm.manage.infrastructure.model.merchantpool.MerchantPoolInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MerchantPoolInfoConverter {

    MerchantPoolInfoConverter INSTANCE = Mappers.getMapper(MerchantPoolInfoConverter.class);

    MerchantPoolInfoEntity toEntity(MerchantPoolInfo merchantPoolInfo);

    List<MerchantPoolInfoEntity> toEntity(List<MerchantPoolInfo> merchantPoolInfoList);
}
