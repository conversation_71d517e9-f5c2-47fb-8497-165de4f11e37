package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.infrastructure.model.product.GoodsLocationDetail;
import net.summerfarm.manage.domain.product.param.query.GoodsLocationDetailQueryParam;
import net.summerfarm.manage.domain.product.entity.GoodsLocationDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07 14:12:49
 * @version 1.0
 *
 */
@Mapper
public interface GoodsLocationDetailMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(GoodsLocationDetail record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(GoodsLocationDetail record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    GoodsLocationDetail selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<GoodsLocationDetail> selectByCondition(GoodsLocationDetailQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<GoodsLocationDetailEntity> getPage(GoodsLocationDetailQueryParam param);

    List<GoodsLocationDetailEntity> selectBySkus(@Param("skus") List<String> skus);
}

