package net.summerfarm.manage.infrastructure.model.offline;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * data_synchronization_information
 * <AUTHOR>
@Data
public class DataSynchronizationInformation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}