package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.MajorPrice;
import net.summerfarm.manage.infrastructure.mapper.product.MajorPriceMapper;
import net.summerfarm.manage.infrastructure.converter.product.MajorPriceConverter;
import net.summerfarm.manage.domain.product.repository.MajorPriceCommandRepository;
import net.summerfarm.manage.domain.product.entity.MajorPriceEntity;
import net.summerfarm.manage.domain.product.param.command.MajorPriceCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-04-08 15:19:21
* @version 1.0
*
*/
@Repository
public class MajorPriceCommandRepositoryImpl implements MajorPriceCommandRepository {

    @Autowired
    private MajorPriceMapper majorPriceMapper;
    @Override
    public MajorPriceEntity insertSelective(MajorPriceCommandParam param) {
        MajorPrice majorPrice = MajorPriceConverter.toMajorPrice(param);
        majorPriceMapper.insertSelective(majorPrice);
        return MajorPriceConverter.toMajorPriceEntity(majorPrice);
    }
    @Override
    public void insertBatch(List<MajorPriceCommandParam> params) {
        majorPriceMapper.insertBatch (params.stream().map (MajorPriceConverter::toMajorPrice).collect(Collectors.toList()));
    }

    @Override
    public void updateBatch(List<MajorPriceCommandParam> updateList) {
        majorPriceMapper.updateBatch(updateList);
    }

    @Override
    public int updateSelectiveById(MajorPriceCommandParam param){
        return majorPriceMapper.updateSelectiveById(MajorPriceConverter.toMajorPrice(param));
    }


    @Override
    public int remove(Long id) {
        return majorPriceMapper.remove(id);
    }

    @Override
    public void removeByIds(List<Integer> ids) {
        majorPriceMapper.removeByIds(ids);
    }

    @Override
    public void commitBatch(List<Long> ids) {
        majorPriceMapper.commitBatch(ids);
    }
}