package net.summerfarm.manage.infrastructure.repository.account;


import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.infrastructure.model.account.ContactAdjust;
import net.summerfarm.manage.infrastructure.mapper.account.ContactAdjustMapper;
import net.summerfarm.manage.infrastructure.converter.account.ContactAdjustConverter;
import net.summerfarm.manage.domain.account.repository.ContactAdjustQueryRepository;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;
import net.summerfarm.manage.common.input.account.ContactAdjustQueryInput;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:20
* @version 1.0
*
*/
@Repository
public class ContactAdjustQueryRepositoryImpl implements ContactAdjustQueryRepository {

    @Autowired
    private ContactAdjustMapper contactAdjustMapper;



    @Override
    public ContactAdjustEntity selectById(Long id) {
        return ContactAdjustConverter.toContactAdjustEntity(contactAdjustMapper.selectById(id));
    }

    @Override
    public List<ContactAdjustEntity> selectByCondition(MerchantQueryInput entity) {
        return ContactAdjustConverter.toContactAdjustEntityList(contactAdjustMapper.selectByCondition(entity));
    }

    @Override
    public ContactAdjustEntity selectOneByEntity(ContactAdjustEntity entity) {
        return ContactAdjustConverter.toContactAdjustEntity(contactAdjustMapper.selectOne(ContactAdjustConverter.toContactAdjust(entity)));
    }

}