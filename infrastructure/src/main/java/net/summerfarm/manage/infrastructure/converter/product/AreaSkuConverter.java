package net.summerfarm.manage.infrastructure.converter.product;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.infrastructure.model.product.AreaSku;

/**
 * @author: <EMAIL>
 * @create: 2023/11/9
 */
public class AreaSkuConverter {


    private AreaSkuConverter() {
        // 无需实现
    }

    public static List<AreaSkuEntity> toAreaSkuEntityList(List<AreaSku> areaSkuList) {
        if (areaSkuList == null) {
            return Collections.emptyList();
        }
        List<AreaSkuEntity> areaSkuEntityList = new ArrayList<>();
        for (AreaSku areaSku : areaSkuList) {
            areaSkuEntityList.add(toAreaSkuEntity(areaSku));
        }
        return areaSkuEntityList;
    }

    public static AreaSkuEntity toAreaSkuEntity(AreaSku areaSku) {
        if (areaSku == null) {
            return null;
        }
        AreaSkuEntity areaSkuEntity = new AreaSkuEntity();
        areaSkuEntity.setId(areaSku.getId());
        areaSkuEntity.setSku(areaSku.getSku());
        areaSkuEntity.setAreaNo(areaSku.getAreaNo());
        areaSkuEntity.setQuantity(areaSku.getQuantity());
        areaSkuEntity.setLockQuantity(areaSku.getLockQuantity());
        areaSkuEntity.setSafeQuantity(areaSku.getSafeQuantity());
        areaSkuEntity.setOriginalPrice(areaSku.getOriginalPrice());
        areaSkuEntity.setPrice(areaSku.getPrice());
        areaSkuEntity.setLadderPrice(areaSku.getLadderPrice());
        areaSkuEntity.setShare(areaSku.getShare());
        areaSkuEntity.setOnSale(areaSku.getOnSale());
        areaSkuEntity.setPriority(areaSku.getPriority());
        areaSkuEntity.setPdPriority(areaSku.getPdPriority());
        areaSkuEntity.setUpdateTime(areaSku.getUpdateTime());
        areaSkuEntity.setAddTime(areaSku.getAddTime());
        areaSkuEntity.setLevel(areaSku.getLevel());
        areaSkuEntity.setLimitedQuantity(areaSku.getLimitedQuantity());
        areaSkuEntity.setSalesMode(areaSku.getSalesMode());
        areaSkuEntity.setAreaName(areaSku.getAreaName());
        areaSkuEntity.setStoreQuantity(areaSku.getStoreQuantity());
        areaSkuEntity.setShareQuantity(areaSku.getShareQuantity());
        areaSkuEntity.setShow(areaSku.getShow());
        areaSkuEntity.setInfo(areaSku.getInfo());
        areaSkuEntity.setMType(areaSku.getMType());
        areaSkuEntity.setShowAdvance(areaSku.getShowAdvance());
        areaSkuEntity.setAdvance(areaSku.getAdvance());
        areaSkuEntity.setOldPrice(areaSku.getOldPrice());
        areaSkuEntity.setCornerStatus(areaSku.getCornerStatus());
        areaSkuEntity.setOpenSale(areaSku.getOpenSale());
        areaSkuEntity.setOpenSaleTime(areaSku.getOpenSaleTime());
        areaSkuEntity.setCloseSale(areaSku.getCloseSale());
        areaSkuEntity.setCloseSaleTime(areaSku.getCloseSaleTime());
        areaSkuEntity.setFixFlag(areaSku.getFixFlag());
        areaSkuEntity.setFixNum(areaSku.getFixNum());
        areaSkuEntity.setInterestRateNew(areaSku.getInterestRateNew());
        areaSkuEntity.setInterestRateOld(areaSku.getInterestRateOld());
        areaSkuEntity.setAutoFlagNew(areaSku.getAutoFlagNew());
        areaSkuEntity.setAutoFlagOld(areaSku.getAutoFlagOld());
        areaSkuEntity.setUpdater(areaSku.getUpdater());
        return areaSkuEntity;
    }

    public static List<AreaSku> toAreaSkuList(List<AreaSkuEntity> areaSkuEntityList) {
        if (areaSkuEntityList == null) {
            return Collections.emptyList();
        }
        List<AreaSku> areaSkuList = new ArrayList<>();
        for (AreaSkuEntity areaSkuEntity : areaSkuEntityList) {
            areaSkuList.add(toAreaSku(areaSkuEntity));
        }
        return areaSkuList;
    }

    public static AreaSku toAreaSku(AreaSkuEntity areaSkuEntity) {
        if (areaSkuEntity == null) {
            return null;
        }
        AreaSku areaSku = new AreaSku();
        areaSku.setId(areaSkuEntity.getId());
        areaSku.setSku(areaSkuEntity.getSku());
        areaSku.setAreaNo(areaSkuEntity.getAreaNo());
        areaSku.setQuantity(areaSkuEntity.getQuantity());
        areaSku.setLockQuantity(areaSkuEntity.getLockQuantity());
        areaSku.setSafeQuantity(areaSkuEntity.getSafeQuantity());
        areaSku.setOriginalPrice(areaSkuEntity.getOriginalPrice());
        areaSku.setPrice(areaSkuEntity.getPrice());
        areaSku.setLadderPrice(areaSkuEntity.getLadderPrice());
        areaSku.setShare(areaSkuEntity.getShare());
        areaSku.setOnSale(areaSkuEntity.getOnSale());
        areaSku.setPriority(areaSkuEntity.getPriority());
        areaSku.setPdPriority(areaSkuEntity.getPdPriority());
        areaSku.setUpdateTime(areaSkuEntity.getUpdateTime());
        areaSku.setAddTime(areaSkuEntity.getAddTime());
        areaSku.setLevel(areaSkuEntity.getLevel());
        areaSku.setLimitedQuantity(areaSkuEntity.getLimitedQuantity());
        areaSku.setSalesMode(areaSkuEntity.getSalesMode());
        areaSku.setAreaName(areaSkuEntity.getAreaName());
        areaSku.setStoreQuantity(areaSkuEntity.getStoreQuantity());
        areaSku.setShareQuantity(areaSkuEntity.getShareQuantity());
        areaSku.setShow(areaSkuEntity.getShow());
        areaSku.setInfo(areaSkuEntity.getInfo());
        areaSku.setMType(areaSkuEntity.getMType());
        areaSku.setShowAdvance(areaSkuEntity.getShowAdvance());
        areaSku.setAdvance(areaSkuEntity.getAdvance());
        areaSku.setOldPrice(areaSkuEntity.getOldPrice());
        areaSku.setCornerStatus(areaSkuEntity.getCornerStatus());
        areaSku.setOpenSale(areaSkuEntity.getOpenSale());
        areaSku.setOpenSaleTime(areaSkuEntity.getOpenSaleTime());
        areaSku.setCloseSale(areaSkuEntity.getCloseSale());
        areaSku.setCloseSaleTime(areaSkuEntity.getCloseSaleTime());
        areaSku.setFixFlag(areaSkuEntity.getFixFlag());
        areaSku.setFixNum(areaSkuEntity.getFixNum());
        areaSku.setInterestRateNew(areaSkuEntity.getInterestRateNew());
        areaSku.setInterestRateOld(areaSkuEntity.getInterestRateOld());
        areaSku.setAutoFlagNew(areaSkuEntity.getAutoFlagNew());
        areaSku.setAutoFlagOld(areaSkuEntity.getAutoFlagOld());
        areaSku.setUpdater(areaSkuEntity.getUpdater());
        return areaSku;
    }
}
