package net.summerfarm.manage.infrastructure.repository.product;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.infrastructure.model.product.Inventory;
import net.summerfarm.manage.infrastructure.mapper.product.InventoryMapper;
import net.summerfarm.manage.infrastructure.converter.product.InventoryConverter;
import net.summerfarm.manage.domain.product.repository.InventoryCommandRepository;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2024-05-06 16:02:27
* @version 1.0
*
*/
@Repository
@Slf4j
public class InventoryCommandRepositoryImpl implements InventoryCommandRepository {

    @Autowired
    private InventoryMapper inventoryMapper;


    @Override
    public InventoryEntity insertSelective(InventoryCommandParam param) {
        Inventory inventory = InventoryConverter.paramToInventory(param);
        inventoryMapper.insertSelective(inventory);
        return InventoryConverter.toInventoryEntity(inventory);
    }

    @Override
    public int updateSelectiveBySku(InventoryCommandParam param){
        if (Objects.isNull(param) || StringUtil.isBlank(param.getSku())) {
            return 0;
        }
        return inventoryMapper.updateBySku(InventoryConverter.paramToInventory(param));
    }

    @Override
    public int updateWithNull(InventoryCommandParam param) {
        return inventoryMapper.updateWithNull(InventoryConverter.paramToInventory(param));
    }
}