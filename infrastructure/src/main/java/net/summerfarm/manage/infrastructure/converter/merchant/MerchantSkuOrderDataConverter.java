package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.infrastructure.model.merchant.MerchantSkuOrderData;
import net.summerfarm.manage.domain.merchant.entity.MerchantSkuOrderDataEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantSkuOrderDataCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-05-22 14:24:47
 * @version 1.0
 *
 */
public class MerchantSkuOrderDataConverter {

    private MerchantSkuOrderDataConverter() {
        // 无需实现
    }




    public static List<MerchantSkuOrderDataEntity> toMerchantSkuOrderDataEntityList(List<MerchantSkuOrderData> merchantSkuOrderDataList) {
        if (merchantSkuOrderDataList == null) {
            return Collections.emptyList();
        }
        List<MerchantSkuOrderDataEntity> merchantSkuOrderDataEntityList = new ArrayList<>();
        for (MerchantSkuOrderData merchantSkuOrderData : merchantSkuOrderDataList) {
            merchantSkuOrderDataEntityList.add(toMerchantSkuOrderDataEntity(merchantSkuOrderData));
        }
        return merchantSkuOrderDataEntityList;
}


    public static MerchantSkuOrderDataEntity toMerchantSkuOrderDataEntity(MerchantSkuOrderData merchantSkuOrderData) {
        if (merchantSkuOrderData == null) {
             return null;
        }
        MerchantSkuOrderDataEntity merchantSkuOrderDataEntity = new MerchantSkuOrderDataEntity();
        merchantSkuOrderDataEntity.setId(merchantSkuOrderData.getId());
        merchantSkuOrderDataEntity.setCreateTime(merchantSkuOrderData.getCreateTime());
        merchantSkuOrderDataEntity.setUpdateTime(merchantSkuOrderData.getUpdateTime());
        merchantSkuOrderDataEntity.setMId(merchantSkuOrderData.getMId());
        merchantSkuOrderDataEntity.setSku(merchantSkuOrderData.getSku());
        merchantSkuOrderDataEntity.setLastOrderTime(merchantSkuOrderData.getLastOrderTime());
        merchantSkuOrderDataEntity.setLastOrderQuantity(merchantSkuOrderData.getLastOrderQuantity());
        merchantSkuOrderDataEntity.setLastThirtyDaysOrderCount(merchantSkuOrderData.getLastThirtyDaysOrderCount());
        merchantSkuOrderDataEntity.setLastSixtyDaysOrderCount(merchantSkuOrderData.getLastSixtyDaysOrderCount());
        merchantSkuOrderDataEntity.setLastTwoYearsOrderCount(merchantSkuOrderData.getLastTwoYearsOrderCount());
        merchantSkuOrderDataEntity.setDayTag(merchantSkuOrderData.getDayTag());
        return merchantSkuOrderDataEntity;
    }








    public static MerchantSkuOrderData toMerchantSkuOrderData(MerchantSkuOrderDataCommandParam param) {
        if (param == null) {
            return null;
        }
        MerchantSkuOrderData merchantSkuOrderData = new MerchantSkuOrderData();
        merchantSkuOrderData.setId(param.getId());
        merchantSkuOrderData.setCreateTime(param.getCreateTime());
        merchantSkuOrderData.setUpdateTime(param.getUpdateTime());
        merchantSkuOrderData.setMId(param.getMId());
        merchantSkuOrderData.setSku(param.getSku());
        merchantSkuOrderData.setLastOrderTime(param.getLastOrderTime());
        merchantSkuOrderData.setLastOrderQuantity(param.getLastOrderQuantity());
        merchantSkuOrderData.setLastThirtyDaysOrderCount(param.getLastThirtyDaysOrderCount());
        merchantSkuOrderData.setLastSixtyDaysOrderCount(param.getLastSixtyDaysOrderCount());
        merchantSkuOrderData.setLastTwoYearsOrderCount(param.getLastTwoYearsOrderCount());
        merchantSkuOrderData.setDayTag(param.getDayTag());
        return merchantSkuOrderData;
    }
}
