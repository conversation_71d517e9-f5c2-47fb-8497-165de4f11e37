<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.marketItem.MarketItemAiExtMapper">
    <!-- 结果集映射 -->
    <resultMap id="marketItemAiExtResultMap" type="net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="pd_id" property="pdId" jdbcType="NUMERIC"/>
		<result column="ext_type" property="extType" jdbcType="TINYINT"/>
		<result column="ext_value" property="extValue" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="marketItemAiExtColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.tenant_id,
          t.sku,
          t.pd_id,
          t.ext_type,
          t.ext_value
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="pdId != null">
                AND t.pd_id = #{pdId}
            </if>
			<if test="extType != null">
                AND t.ext_type = #{extType}
            </if>
			<if test="extValue != null and extValue !=''">
                AND t.ext_value = #{extValue}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="pdId != null">
                    t.pd_id = #{pdId},
                </if>
                <if test="extType != null">
                    t.ext_type = #{extType},
                </if>
                <if test="extValue != null">
                    t.ext_value = #{extValue},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="marketItemAiExtResultMap" >
        SELECT <include refid="marketItemAiExtColumns" />
        FROM market_item_ai_ext t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam"  resultType="net.summerfarm.manage.domain.marketItem.entity.MarketItemAiExtEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.tenant_id tenantId,
            t.sku sku,
            t.pd_id pdId,
            t.ext_type extType,
            t.ext_value extValue
        FROM market_item_ai_ext t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.marketItem.param.query.MarketItemAiExtQueryParam" resultMap="marketItemAiExtResultMap" >
        SELECT <include refid="marketItemAiExtColumns" />
        FROM market_item_ai_ext t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO market_item_ai_ext
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="pdId != null">
				  pd_id,
              </if>
              <if test="extType != null">
				  ext_type,
              </if>
              <if test="extValue != null">
				  ext_value,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="pdId != null">
				#{pdId,jdbcType=NUMERIC},
              </if>
              <if test="extType != null">
				#{extType,jdbcType=TINYINT},
              </if>
              <if test="extValue != null">
				#{extValue,jdbcType=LONGVARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt" >
        UPDATE market_item_ai_ext t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.marketItem.MarketItemAiExt" >
        DELETE FROM market_item_ai_ext t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM market_item_ai_ext t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>



</mapper>