<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.merchant.MerchantMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.merchant.Merchant">
        <result column="m_id" property="mId" jdbcType="NUMERIC"/>
        <result column="role_id" property="roleId" jdbcType="INTEGER"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="mcontact" property="mcontact" jdbcType="VARCHAR"/>
        <result column="openid" property="openid" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="islock" property="islock" jdbcType="INTEGER"/>
        <result column="rank_id" property="rankId" jdbcType="INTEGER"/>
        <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="invitecode" property="invitecode" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="inviter_channel_code" property="inviterChannelCode" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_user" property="auditUser" jdbcType="INTEGER"/>
        <result column="business_license" property="businessLicense" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="shop_sign" property="shopSign" jdbcType="VARCHAR"/>
        <result column="other_proof" property="otherProof" jdbcType="VARCHAR"/>
        <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="trade_area" property="tradeArea" jdbcType="VARCHAR"/>
        <result column="trade_group" property="tradeGroup" jdbcType="VARCHAR"/>
        <result column="unionid" property="unionid" jdbcType="VARCHAR"/>
        <result column="mp_openid" property="mpOpenid" jdbcType="VARCHAR"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="direct" property="direct" jdbcType="INTEGER"/>
        <result column="server" property="server" jdbcType="INTEGER"/>
        <result column="pop_view" property="popView" jdbcType="INTEGER"/>
        <result column="member_integral" property="memberIntegral" jdbcType="DOUBLE"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="sku_show" property="skuShow" jdbcType="INTEGER"/>
        <result column="recharge_amount" property="rechargeAmount" jdbcType="DOUBLE"/>
        <result column="cash_amount" property="cashAmount" jdbcType="DOUBLE"/>
        <result column="cash_update_time" property="cashUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="show_price" property="showPrice" jdbcType="INTEGER"/>
        <result column="merge_admin" property="mergeAdmin" jdbcType="VARCHAR"/>
        <result column="merge_time" property="mergeTime" jdbcType="TIMESTAMP"/>
        <result column="first_login_pop" property="firstLoginPop" jdbcType="INTEGER"/>
        <result column="change_pop" property="changePop" jdbcType="INTEGER"/>
        <result column="pull_black_remark" property="pullBlackRemark" jdbcType="VARCHAR"/>
        <result column="pull_black_operator" property="pullBlackOperator" jdbcType="VARCHAR"/>
        <result column="house_number" property="houseNumber" jdbcType="VARCHAR"/>
        <result column="company_brand" property="companyBrand" jdbcType="VARCHAR"/>
        <result column="clue_pool" property="cluePool" jdbcType="TINYINT"/>
        <result column="merchant_type" property="merchantType" jdbcType="VARCHAR"/>
        <result column="enterprise_scale" property="enterpriseScale" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="examine_type" property="examineType" jdbcType="INTEGER"/>
        <result column="display_button" property="displayButton" jdbcType="INTEGER"/>
        <result column="operate_status" property="operateStatus" jdbcType="TINYINT"/>
        <result column="updater" property="updater" jdbcType="INTEGER"/>
        <result column="door_pic" property="doorPic" jdbcType="VARCHAR"/>
        <result column="pre_register_flag" property="preRegisterFlag" jdbcType="INTEGER"/>
    </resultMap>


    <sql id="Base_Column_List">
    m_id, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,openid,inviter_channel_code,
    invitecode, audit_time, audit_user, province, city, area, address,remark,area_no,size,type,recharge_amount,door_pic,
    trade_area,trade_group,admin_id,direct,server,member_integral,grade,house_number,enterprise_scale,company_brand,merchant_type,show_price,poi_note
  </sql>


    <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.merchant.Merchant" useGeneratedKeys="true" keyProperty="mId">
        insert into merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="mname != null">
                mname,
            </if>
            <if test="mcontact != null">
                mcontact,
            </if>
            <if test="openid != null">
                openid,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="islock != null">
                islock,
            </if>
            <if test="rankId != null">
                rank_id,
            </if>
            <if test="registerTime != null">
                register_time,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="invitecode != null">
                invitecode,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="auditUser != null">
                audit_user,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="size != null">
                size ,
            </if>
            <if test="type != null">
                type ,
            </if>
            <if test="tradeArea != null">
                trade_area ,
            </if>
            <if test="tradeGroup != null">
                trade_group ,
            </if>
            <if test="adminId != null">
                admin_id ,
            </if>
            <if test="direct != null">
                direct ,
            </if>
            <if test="server != null">
                server ,
            </if>
            <if test="houseNumber != null">
                house_number ,
            </if>
            <if test="enterpriseScale != null">
                enterprise_scale ,
            </if>
            <if test="companyBrand != null">
                company_brand ,
            </if>
            <if test ="cluePool != null">
                clue_pool,
            </if>
            <if test="merchantType != null">
                merchant_type,
            </if>
            <if test="poiNote != null">
                poi_note ,
            </if>
            <if test="skuShow != null">
                sku_show,
            </if>
            <if test="examineType != null">
                examine_type,
            </if>
            <if test="displayButton != null">
                display_button,
            </if>
            <if test="preRegisterFlag != null">
                pre_register_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="mname != null">
                #{mname,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null">
                #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="islock != null">
                #{islock,jdbcType=BIT},
            </if>
            <if test="rankId != null">
                #{rankId,jdbcType=TINYINT},
            </if>
            <if test="registerTime != null">
                #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invitecode != null">
                #{invitecode,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUser != null">
                #{auditUser,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                #{size},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="tradeArea != null">
                #{tradeArea},
            </if>
            <if test="tradeGroup != null">
                #{tradeGroup},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="direct != null">
                #{direct},
            </if>
            <if test="server != null">
                server = #{server},
            </if>
            <if test="houseNumber != null">
                #{houseNumber ,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseScale != null">
                #{enterpriseScale} ,
            </if>
            <if test="companyBrand != null">
                #{companyBrand},
            </if>
            <if test ="cluePool != null">
                #{cluePool},
            </if>
            <if test="merchantType != null">
                #{merchantType},
            </if>
            <if test="poiNote != null">
                #{poiNote,jdbcType=VARCHAR},
            </if>
            <if test="skuShow != null">
                #{skuShow},
            </if>
            <if test="examineType != null">
                #{examineType},
            </if>
            <if test="displayButton != null">
                #{displayButton},
            </if>
            <if test="preRegisterFlag != null">
                #{preRegisterFlag},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective">
        update merchant
        <set >
            <if test="mname != null" >
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null" >
                mcontact = #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="openid != null" >
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="phone != null" >
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="islock != null" >
                islock = #{islock,jdbcType=INTEGER},
            </if>
            <if test="rankId != null" >
                rank_id = #{rankId,jdbcType=INTEGER},
            </if>
            <if test="registerTime != null" >
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginTime != null" >
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invitecode != null" >
                invitecode = #{invitecode,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null" >
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUser != null" >
                audit_user = #{auditUser,jdbcType=INTEGER},
            </if>
            <if test="businessLicense != null" >
                business_license = #{businessLicense,jdbcType=VARCHAR},
            </if>
            <if test="province != null" >
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null" >
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null" >
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null" >
                area_no = #{areaNo,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="poiNote != null" >
                poi_note = #{poiNote,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="shopSign != null" >
                shop_sign = #{shopSign,jdbcType=VARCHAR},
            </if>
            <if test="otherProof != null" >
                other_proof = #{otherProof,jdbcType=VARCHAR},
            </if>
            <if test="lastOrderTime != null" >
                last_order_time = #{lastOrderTime},
            </if>
            <if test="unionid != null" >
                unionid = #{unionid},
            </if>
            <if test="mpOpenid != null" >
                mp_openid = #{mpOpenid},
            </if>
            <if test="memberIntegral != null">
                member_integral = #{memberIntegral},
            </if>
            <if test="grade != null">
                grade = #{grade},
            </if>
            <if test="popView != null">
                pop_view = #{popView},
            </if>
            <if test="firstLoginPop != null">
                first_login_pop = #{firstLoginPop},
            </if>
            <if test="changePop != null">
                change_pop = #{changePop},
            </if>
        </set>
        where m_id = #{mId,jdbcType=BIGINT}
    </update>
    <update id="resetAllMemberGrade" parameterType="java.lang.Integer">
        update merchant
        set member_integral = 0,grade = null
        where area_no = #{areaNo} and islock = 0
    </update>
    <update id="updateMemberGrade">
        update merchant
        set member_integral = #{rechargeAmount},grade = #{grade}
        where m_id = #{mId}
    </update>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT m.m_id, m.mname, m.mcontact from merchant m
        <where>
         m.mname = #{name}
        </where>

    </select>

    <select id="existChannelCode" resultType="boolean">
        select count(0) > 0 from merchant where channel_code = #{channelCode}
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap" >
        SELECT <include refid="Base_Column_List" />
        FROM merchant t
        WHERE t.m_id = #{mId}
    </select>

    <select id="selectByIdForceMaster" parameterType="java.lang.Long" resultMap="BaseResultMap" >
        /*FORCE_MASTER*/
        SELECT <include refid="Base_Column_List" />
        FROM merchant t
        WHERE t.m_id = #{mId}
    </select>


    <select id="selectByIds" parameterType="java.lang.Long" resultMap="BaseResultMap" >
        SELECT <include refid="Base_Column_List" />
        FROM merchant t
        WHERE t.m_id in
        <foreach collection="mIds" item="mid" open="(" close=")" separator=",">
        #{mid}
        </foreach>
    </select>

    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM merchant m
        <where>
            <if test="channelCode != null">
                AND m.channel_code = #{channelCode}
            </if>
            <if test="openid != null">
                AND m.openid = #{openid}
            </if>
        </where>
        limit 1
    </select>
    <select id="selectMemberIntegralPage"
            resultType="net.summerfarm.manage.infrastructure.model.merchant.Merchant">
        SELECT
            m.m_id as mId,
            m.member_integral as memberIntegral,
            m.grade as grade
        FROM merchant m
        WHERE m.area_no = #{areaNo} ORDER BY m.m_id
        limit #{pageStart}, #{pageSize}
    </select>


    <update id="updateByPrimaryKeySelectiveBatch" parameterType="net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam">
        <foreach collection="list" item="item" index="index" separator=";">
            update merchant
            <set>
                <if test="item.memberIntegral != null">
                member_integral = #{item.memberIntegral},
                </if>
                <if test="item.nextGrade != null">
                next_grade = #{item.nextGrade},
                </if>
                <if test="item.grade != null">
                grade = #{item.grade},
                </if>
            </set>
            where m_id = #{item.mId}
        </foreach>
    </update>
</mapper>
