<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.product.InventoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.product.Inventory">
    <id column="inv_id" property="invId" jdbcType="BIGINT"/>
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
    <result column="ait_id" property="aitId" jdbcType="INTEGER"/>
    <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
    <result column="origin" property="origin" jdbcType="VARCHAR"/>
    <result column="unit" property="unit" jdbcType="VARCHAR"/>
    <result column="pack" property="pack" jdbcType="VARCHAR"/>
    <result column="weight" property="weight" jdbcType="VARCHAR"/>
    <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
    <result column="production_date" property="productionDate" jdbcType="DATE"/>
    <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
    <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>
    <result column="promotion_price" property="promotionPrice" jdbcType="DECIMAL"/>
    <result column="outdated" property="outdated" jdbcType="BIT"/>
    <result column="after_sale_quantity" property="afterSaleQuantity" jdbcType="INTEGER"/>
    <result column="base_sale_quantity" jdbcType="INTEGER" property="baseSaleQuantity"/>
    <result column="base_sale_unit" jdbcType="INTEGER" property="baseSaleUnit"/>
    <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
    <result column="volume" property="volume" jdbcType="VARCHAR"/>
    <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="sub_type" property="subType" jdbcType="INTEGER"/>
    <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <result column="sample_pool" property="samplePool"/>
    <result column="sku_pic" property="skuPic"/>
    <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR"/>
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
    <result column="auditor" property="auditor" jdbcType="INTEGER"/>
    <result column="creator" property="creator" jdbcType="VARCHAR"/>
    <result column="ext_type" property="extType" jdbcType="INTEGER"/>
    <result column="create_type" property="createType" jdbcType="INTEGER"/>
    <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
    <result column="is_domestic" property="isDomestic" jdbcType="VARCHAR"/>
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    <result column="net_weight_num" jdbcType="DECIMAL" property="netWeightNum" />
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="after_sale_rule_detail" jdbcType="VARCHAR" property="afterSaleRuleDetail" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
    <result column="net_weight_unit" jdbcType="VARCHAR" property="netWeightUnit" />
    <result column="video_upload_user" jdbcType="VARCHAR" property="videoUploadUser" />
    <result column="video_upload_time" jdbcType="TIMESTAMP" property="videoUploadTime"/>
    <result column="quote_type" property="quoteType" jdbcType="INTEGER"/>
    <result column="min_auto_after_sale_threshold" property="minAutoAfterSaleThreshold" jdbcType="INTEGER"/>
  </resultMap>

  <resultMap id="CommonResultMap" type="net.summerfarm.manage.domain.product.entity.ProductEntity">
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
    <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
    <result column="weight" property="weight" jdbcType="VARCHAR"/>
    <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
    <result column="net_weight_num" property="netWeightNum" jdbcType="DECIMAL"/>
    <result column="sub_type" property="subType" jdbcType="INTEGER"/>
    <result column="sku_pic" property="skuPic"/>
    <result column="ext_type" property="extType" jdbcType="INTEGER"/>
    <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
    <result column="pd_name" property="productName" jdbcType="VARCHAR"/>
    <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
    <result column="type" property="categoryType" jdbcType="INTEGER"/>
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="video_upload_user" jdbcType="VARCHAR" property="videoUploadUser" />
    <result column="video_upload_time" jdbcType="TIMESTAMP" property="videoUploadTime" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
  </resultMap>

  <resultMap id="ProductMap" type="net.summerfarm.manage.domain.product.entity.ProductEntity">
    <id column="pd_id" property="pdId" jdbcType="INTEGER" />
    <result column="pd_name" property="productName" jdbcType="VARCHAR" />
    <result column="pd_no" property="pdNo" jdbcType="VARCHAR"/>
    <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
    <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
    <result column="category_id" property="categoryId"/>
    <result column="create_type" property="createType"/>
  </resultMap>


  <sql id="Base_Column_List">
    inv_id, sku, sku_name, ait_id, pd_id, origin, unit, pack, weight, production_date,
        storage_method, sale_price,
        promotion_price, maturity, after_sale_quantity,outdated,volume,weight_num ,
        type,sub_type,admin_id,base_sale_quantity,base_sale_unit,sample_pool, sku_pic, after_sale_unit,
        audit_status,audit_time,creator,ext_type,create_type,auditor,refuse_reason, is_domestic, tenant_id,
        net_weight_num,video_url,after_sale_rule_detail,buyer_name,buyer_id,net_weight_unit
        ,video_upload_user,video_upload_time,quote_type,min_auto_after_sale_threshold
  </sql>


  <select id="selectByIds"
    resultType="net.summerfarm.manage.infrastructure.model.product.Inventory">
    select
    inv_id invId, sku, after_sale_unit afterSaleUnit, after_sale_quantity afterSaleQuantity, pd_id
    pdId, tenant_id tenantId
    FROM inventory
    where inv_id in
    <foreach collection="invIds" item="it" open="(" separator="," close=")">
      #{it}
    </foreach>
  </select>

  <select id="listByQuery" parameterType="net.summerfarm.manage.domain.product.param.ProductPageQueryParam" resultMap="CommonResultMap">
    SELECT i.sku,
           i.sku_name,
           i.sku_pic,
           i.weight,
           i.weight_num,
           i.net_weight_num,
           i.ext_type,
           i.sub_type,
           i.pd_id,
           i.video_url ,
           i.after_sale_rule_detail ,
           i.buyer_name ,
           i.buyer_id ,
           i.net_weight_unit ,
           i.video_upload_user ,
           i.video_upload_time,
           p.pd_name,
           p.picture_path,
           p.category_id,
           c.type
    from inventory i
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
    where i.outdated = 0
      and p.outdated = 0
      <if test="sku != null">
        and i.sku = #{sku}
      </if>
      <if test="skuList != null and skuList.size() > 0">
        and i.sku in
        <foreach collection="skuList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="subType != null">
        and i.sub_type = #{subType}
      </if>
      <if test="nEqSubType != null">
        and i.sub_type != #{nEqSubType}
      </if>
      <if test="buyerId != null">
        and i.buyer_id = #{buyerId}
      </if>
      <if test = "name != null">
        and (p.pd_name like CONCAT('%',#{name},'%') or i.sku_name like CONCAT('%',#{name},'%'))
      </if>
      <if test="categoryIds != null and categoryIds.size() > 0">
        and p.category_id in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </select>



  <select id="queryBySku" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM inventory i
    where i.outdated=0  and i.sku = #{sku}
  </select>


  <select id="selectPage" resultMap="ProductMap">
    select  p.pd_id,p.pd_name,p.pd_no,p.picture_path,p.outdated,p.category_id, p.create_type
    from `products` p
    left join inventory i on i.pd_id = p.pd_id
    left join category c on p.category_id = c.id
    left join category pc on c.parent_id = pc.id
    <where>
      <!-- 特殊逻辑，商城过滤saas仅自营品 -->
      p.create_type != 3
      <choose>
        <when test="outdated != null and outdated.toString() != ''">
          AND p.outdated= #{outdated}
        </when>
        <otherwise>
          and p.outdated in (-1,0,1)
        </otherwise>
      </choose>
      <if test="pdNo != null">
        AND p.pd_no = #{pdNo}
      </if>
      <if test="outdated != null">
        AND p.outdated= #{outdated}
      </if>
      <if test="name !=null">
        AND p.pd_name like CONCAT('%',#{name},'%')
      </if>
      <if test="categoryId != null">
        AND p.category_id = #{categoryId}
      </if>
      <if test="parentCategoryId != null">
        AND c.parent_id = #{parentCategoryId}
      </if>
      <if test="grandCategoryId != null">
        AND pc.parent_id = #{grandCategoryId}
      </if>
      <if test="sku !=null and sku !=''">
        AND i.sku =#{sku}
      </if>
      <if test="type != null">
        AND i.type = #{type}
      </if>
      <if test="subType != null">
        AND i.sub_type = #{subType}
      </if>
      <if test="buyerId != null">
        AND i.buyer_id= #{buyerId}
      </if>
    </where>
    GROUP BY p.pd_id
  </select>


  <select id="queryByPdid" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM inventory i
    <where>
      <if test="sku != null and sku !=''">
        AND i.sku =#{sku}
      </if>
      <if test="type != null">
        AND i.type = #{type}
      </if>
      <if test="subType != null">
        AND i.sub_type = #{subType}
      </if>
      <if test="outdated != null">
        AND i.outdated= #{outdated}
      </if>
      <if test="pdId != null">
        AND i.pd_id= #{pdId}
      </if>
      <if test="pdIds != null and pdIds.size() > 0">
        AND pd_id IN
        <foreach collection="pdIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="buyerId != null">
        AND i.buyer_id= #{buyerId}
      </if>
    </where>
  </select>

  <select id="querySelectOne" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM inventory
    <where>
      <if test="sku != null">
        AND sku =#{sku}
      </if>
      <if test="invId != null">
        AND inv_id =#{invId}
      </if>
      <if test="extType != null">
        AND ext_type = #{extType}
      </if>
    </where>
  </select>

    <select id="selectByPdId" resultMap="BaseResultMap">
      SELECT  <include refid="Base_Column_List" />
      FROM inventory
      WHERE pd_id = #{pdId}
    </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM inventory
    <where>
      <!-- 特殊逻辑，商城过滤saas仅自营品 -->
      create_type != 3
      <if test="outdated != null">
        AND outdated =#{outdated}
      </if>
      <if test="pdId != null">
        AND pd_id = #{pdId}
      </if>
      <if test="auditStatus != null">
        AND audit_status = #{auditStatus}
      </if>
      <if test="extType != null">
        AND ext_type = #{extType}
      </if>
    </where>
  </select>

  <select id="listBySkus" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM inventory WHERE sku IN
    <foreach collection="skus" item="sku" index="index" open="(" close=")" separator=",">
      #{sku}
    </foreach>
  </select>
    <select id="queryInfo" resultType="net.summerfarm.manage.domain.product.entity.InventoryEntity">
      SELECT
      i.inv_id invId,i.sku,i.sku_name skuName, p.pd_name pdName, i.origin, i.unit, i.pack, i.weight, i.production_date productionDate,
      i.storage_method storageMethod, i.base_sale_quantity baseSaleQuantity, i.base_sale_unit baseSaleUnit,
      i.promotion_price promotionPrice, i.maturity, i.after_sale_quantity afterSaleQuantity,i.volume,i.weight_num weightNum,
      a.name_remakes nameRemakes,a.admin_id  adminId, type, i.sub_type subType, i.sample_pool samplePool,i.sku_pic skuPic, i.after_sale_unit afterSaleUnit,i.outdated,
      i.audit_status auditStatus, i.audit_time auditTime, i.creator, i.ext_type extType, p.real_name realName, i.create_remark createRemark,
      i.pd_id pdId,i.weight_notes weightNotes,i.is_domestic isDomestic,i.average_price_flag averagePriceFlag, i.create_type createType,
      i.net_weight_num netWeightNum,i.video_url videoUrl,i.after_sale_rule_detail afterSaleRuleDetail,i.buyer_name buyerName,i.buyer_id buyerId,
      i.video_upload_user videoUploadUser,i.video_upload_time videoUploadTime, i.quote_type quoteType,
      i.min_auto_after_sale_threshold minAutoAfterSaleThreshold, p.category_id categoryId, p.picture_path picturePath, p.detail_picture as detailPicture
      FROM inventory i
      LEFT JOIN products p on i.pd_id = p.pd_id
      LEFT JOIN admin a on a.admin_id = i.admin_id
      <where>
        <if test="outdated != null">
          AND i.outdated= #{outdated}
        </if>
        <if test="pdId != null">
          AND p.pd_id = #{pdId}
        </if>
        <if test="sku != null">
          AND i.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
          and i.sku in
          <foreach collection="skuList" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="auditStatus != null">
          AND i.audit_status = #{auditStatus}
        </if>
        <if test="extType != null">
          AND i.ext_type = #{extType}
        </if>
        <if test="subType != null">
          AND i.sub_type = #{subType}
        </if>
        <if test="samplePool != null">
          AND i.sample_pool = #{samplePool}
        </if>
      </where>
    </select>
  <select id="listByParamGroupByLargeArea"
          resultType="net.summerfarm.manage.domain.product.entity.InvetoryGroupByLargeAreaNoEntity">
    SELECT i.sku,p.`pd_name` skuName, p.pd_name pdName, i.weight,ppvpz.products_property_value as levelPropertyName,
    ppvjb.products_property_value as `level`,p.category_id categoryId,la.large_area_no largeAreaNo,
           la.large_area_name largeAreaName,i.ext_type extType,p.quality_time qualityTime ,min(asku.`price`) minPrice,
           MAX(`asku`.`price`) maxPrice,ppvcd.products_property_value as origin  FROM `inventory` i
    LEFT JOIN products p ON i.`pd_id` = p.`pd_id`
    LEFT JOIN products_property_value ppvjb ON i.sku = ppvjb.sku  AND ppvjb.products_property_id = (SELECT id FROM products_property WHERE status = 1 AND `name` = '级别' AND `type` = 1)
    LEFT JOIN products_property_value ppvpz ON p.pd_id = ppvpz.pd_id AND ppvpz.products_property_id = (SELECT id FROM products_property WHERE status = 1 AND `name` = '品种' AND `type` = 0)
    LEFT JOIN products_property_value ppvcd ON p.pd_id = ppvcd.pd_id  AND ppvcd.products_property_id = (SELECT id FROM products_property WHERE status = 1 AND `name` = '产地' AND `type` = 0)
    LEFT JOIN `area_sku` asku ON asku.sku = i.sku
    LEFT JOIN area ON area.area_no = asku.area_no
    LEFT JOIN `large_area` la ON la.large_area_no = area.large_area_no
    <where> i.create_type != 3 AND i.outdated = 0
      <if test="levelPropertyNames !=null and levelPropertyNames.size > 0">
        AND ppvjb.sku IS NOT NULL AND ppvjb.products_property_value in <foreach collection="levelPropertyNames" item="levelPropertyName" separator="," open="(" close=")">#{levelPropertyName}</foreach>
      </if>
      <if test="varietyList !=null and varietyList.size > 0">
        AND ppvpz.pd_id IS NOT NULL AND ppvpz.products_property_value in <foreach collection="varietyList" item="variety" separator="," open="(" close=")">#{variety}</foreach>
      </if>
      <if test="skus !=null and skus.size > 0">
        AND i.sku in <foreach collection="skus" item="sku" separator="," open="(" close=")">#{sku}</foreach>
      </if>
    <if test="itemCode != null">
      AND i.sku = #{itemCode}
    </if>
    <if test="largeAreaNos !=null and largeAreaNos.size > 0">
      AND la.large_area_no in <foreach collection="largeAreaNos" item="largeAreaNo" separator="," open="(" close=")">#{largeAreaNo}</foreach>
    </if>
    <if test="extTypes !=null and extTypes.size > 0">
      AND i.ext_type in <foreach collection="extTypes" item="extType" separator="," open="(" close=")">#{extType}</foreach>
    </if>
    <if test="adminId != null">
      AND ((i.admin_id = #{adminId} and i.sub_type=4) or (i.admin_id is null and i.sub_type!=4))
    </if>
    <if test="adminId == null">
      AND i.admin_id is null and i.sub_type!=4
    </if>
    </where>
    GROUP BY la.`large_area_no` ,asku.sku
  </select>
  <select id="querySkusByNameLike" resultType="java.lang.String">
    select sku FROM `inventory` i
    LEFT JOIN products p ON i.`pd_id` = p.`pd_id`
    <where>
     i.create_type != 3 AND i.outdated = 0 and p.outdated = 0 AND p.pd_name like CONCAT('%',#{spuTitleLike},'%')
    </where>
  </select>

  <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
  <insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.product.Inventory" keyProperty="invId" useGeneratedKeys="true">
    INSERT INTO inventory
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="invId != null">
        inv_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="storeQuantity != null">
        store_quantity,
      </if>
      <if test="limitedQuantity != null">
        limited_quantity,
      </if>
      <if test="aitId != null">
        ait_id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="origin != null">
        origin,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="maturity != null">
        maturity,
      </if>
      <if test="pack != null">
        pack,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="productionDate != null">
        production_date,
      </if>
      <if test="storageMethod != null">
        storage_method,
      </if>
      <if test="slogan != null">
        slogan,
      </if>
      <if test="saleCount != null">
        sale_count,
      </if>
      <if test="salesMode != null">
        sales_mode,
      </if>
      <if test="alertInventory != null">
        alert_inventory,
      </if>
      <if test="safeInventory != null">
        safe_inventory,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="promotionPrice != null">
        promotion_price,
      </if>
      <if test="introduction != null">
        introduction,
      </if>
      <if test="afterSaleQuantity != null">
        after_sale_quantity,
      </if>
      <if test="outdated != null">
        outdated,
      </if>
      <if test="baseSaleQuantity != null">
        base_sale_quantity,
      </if>
      <if test="baseSaleUnit != null">
        base_sale_unit,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="weightNum != null">
        weight_num,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="samplePool != null">
        sample_pool,
      </if>
      <if test="skuPic != null">
        sku_pic,
      </if>
      <if test="afterSaleUnit != null">
        after_sale_unit,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="extType != null">
        ext_type,
      </if>
      <if test="createRemark != null">
        create_remark,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="createType != null">
        create_type,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="weightNotes != null">
        weight_notes,
      </if>
      <if test="isDomestic != null">
        is_domestic,
      </if>
      <if test="supplierVisible != null">
        supplier_visible,
      </if>
      <if test="averagePriceFlag != null">
        average_price_flag,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="invId != null">
        #{invId,jdbcType=NUMERIC},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="storeQuantity != null">
        #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="limitedQuantity != null">
        #{limitedQuantity,jdbcType=INTEGER},
      </if>
      <if test="aitId != null">
        #{aitId,jdbcType=INTEGER},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=NUMERIC},
      </if>
      <if test="origin != null">
        #{origin,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="maturity != null">
        #{maturity,jdbcType=VARCHAR},
      </if>
      <if test="pack != null">
        #{pack,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="productionDate != null">
        #{productionDate,jdbcType=DATE},
      </if>
      <if test="storageMethod != null">
        #{storageMethod,jdbcType=VARCHAR},
      </if>
      <if test="slogan != null">
        #{slogan,jdbcType=VARCHAR},
      </if>
      <if test="saleCount != null">
        #{saleCount,jdbcType=INTEGER},
      </if>
      <if test="salesMode != null">
        #{salesMode,jdbcType=INTEGER},
      </if>
      <if test="alertInventory != null">
        #{alertInventory,jdbcType=INTEGER},
      </if>
      <if test="safeInventory != null">
        #{safeInventory,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="promotionPrice != null">
        #{promotionPrice,jdbcType=DOUBLE},
      </if>
      <if test="introduction != null">
        #{introduction,jdbcType=LONGVARCHAR},
      </if>
      <if test="afterSaleQuantity != null">
        #{afterSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="outdated != null">
        #{outdated,jdbcType=INTEGER},
      </if>
      <if test="baseSaleQuantity != null">
        #{baseSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="baseSaleUnit != null">
        #{baseSaleUnit,jdbcType=INTEGER},
      </if>
      <if test="volume != null">
        #{volume,jdbcType=VARCHAR},
      </if>
      <if test="weightNum != null">
        #{weightNum,jdbcType=DOUBLE},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="samplePool != null">
        #{samplePool,jdbcType=INTEGER},
      </if>
      <if test="skuPic != null">
        #{skuPic,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUnit != null">
        #{afterSaleUnit,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="extType != null">
        #{extType,jdbcType=INTEGER},
      </if>
      <if test="createRemark != null">
        #{createRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="createType != null">
        #{createType,jdbcType=INTEGER},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=INTEGER},
      </if>
      <if test="weightNotes != null">
        #{weightNotes,jdbcType=VARCHAR},
      </if>
      <if test="isDomestic != null">
        #{isDomestic,jdbcType=TINYINT},
      </if>
      <if test="supplierVisible != null">
        #{supplierVisible,jdbcType=TINYINT},
      </if>
      <if test="averagePriceFlag != null">
        #{averagePriceFlag,jdbcType=TINYINT},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=NUMERIC},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <!--根据sku更新sku信息-->
  <update id="updateBySku" parameterType="net.summerfarm.manage.infrastructure.model.product.Inventory">
    update inventory
    <set>
      <if test="aitId != null">
        ait_id = #{aitId,jdbcType=INTEGER},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="origin != null">
        origin = #{origin,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="pack != null">
        pack = #{pack,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="productionDate != null">
        production_date = #{productionDate,jdbcType=DATE},
      </if>
      <if test="storageMethod != null">
        storage_method = #{storageMethod,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="promotionPrice != null">
        promotion_price = #{promotionPrice,jdbcType=DECIMAL},
      </if>
      <if test="introduction != null">
        introduction = #{introduction},
      </if>
      <if test="salesMode != null">
        sales_mode = #{salesMode},
      </if>
      <if test="maturity != null">
        maturity = #{maturity},
      </if>
      <if test="outdated != null">
        outdated = #{outdated},
      </if>
      <if test="afterSaleQuantity != null">
        after_sale_quantity = #{afterSaleQuantity},
      </if>
      <if test="baseSaleQuantity != null">
        base_sale_quantity = #{baseSaleQuantity},
      </if>
      <if test="baseSaleUnit != null">
        base_sale_unit = #{baseSaleUnit},
      </if>
      <if test="volume != null and volume != ''">
        volume = #{volume},
      </if>
      <if test="weightNum != null">
        weight_num = #{weightNum},
      </if>
      <if test="type != null">
        type = #{type} ,
      </if>
      <if test="adminId != null">
        admin_id = #{adminId},
      </if>
      <if test="samplePool != null">
        sample_pool = #{samplePool},
      </if>
      <if test="skuPic != null and skuPic != ''">
        sku_pic = #{skuPic},
      </if>
      <if test="afterSaleUnit != null and afterSaleUnit != ''">
        after_sale_unit = #{afterSaleUnit},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="extType != null">
        ext_type = #{extType},
      </if>
      <if test="createRemark != null">
        create_remark = #{createRemark},
      </if>
      <if test="auditor != null">
        auditor = #{auditor},
      </if>
      <if test="supplierVisible != null">
        supplier_visible = #{supplierVisible},
      </if>
      <if test="isDomestic != null">
        is_domestic = #{isDomestic},
      </if>
      <if test="averagePriceFlag != null">
        average_price_flag = #{averagePriceFlag},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason},
      </if>
      <if test="createType != null">
        create_type = #{createType},
      </if>
      <if test="taskType != null">
        task_type = #{taskType},
      </if>
      <if test="subType!=null">
        sub_type=#{subType},
      </if>
      <if test="netWeightNum != null" >
        net_weight_num = #{netWeightNum,jdbcType=DECIMAL},
      </if>
      <if test="netWeightUnit != null" >
        net_weight_unit = #{netWeightUnit,jdbcType=VARCHAR},
      </if>
      <if test="videoUrl != null" >
        video_url = #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleRuleDetail != null" >
        after_sale_rule_detail = #{afterSaleRuleDetail,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null" >
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null" >
        buyer_id = #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="videoUploadTime != null">
        video_upload_time = #{videoUploadTime},
      </if>
      <if test="videoUploadUser != null">
        video_upload_user = #{videoUploadUser},
      </if>
    </set>
    where sku = #{sku,jdbcType=VARCHAR}
  </update>
  <update id="updateWithNull">
    update inventory
    <set>
      <if test="aitId != null">
        ait_id = #{aitId,jdbcType=INTEGER},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="origin != null">
        origin = #{origin,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="pack != null">
        pack = #{pack,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="productionDate != null">
        production_date = #{productionDate,jdbcType=DATE},
      </if>
      <if test="storageMethod != null">
        storage_method = #{storageMethod,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="promotionPrice != null">
        promotion_price = #{promotionPrice,jdbcType=DECIMAL},
      </if>
      <if test="introduction != null">
        introduction = #{introduction},
      </if>
      <if test="salesMode != null">
        sales_mode = #{salesMode},
      </if>
      <if test="maturity != null">
        maturity = #{maturity},
      </if>
      <if test="outdated != null">
        outdated = #{outdated},
      </if>
      <if test="afterSaleQuantity != null">
        after_sale_quantity = #{afterSaleQuantity},
      </if>
      <if test="baseSaleQuantity != null">
        base_sale_quantity = #{baseSaleQuantity},
      </if>
      <if test="baseSaleUnit != null">
        base_sale_unit = #{baseSaleUnit},
      </if>
      <if test="volume != null">
        volume = #{volume},
      </if>
      <if test="weightNum != null">
        weight_num = #{weightNum},
      </if>
      <if test="type != null">
        type = #{type} ,
      </if>
      <if test="adminId != null">
        admin_id = #{adminId},
      </if>
      <if test="samplePool != null">
        sample_pool = #{samplePool},
      </if>
      sku_pic = #{skuPic},
      <if test="afterSaleUnit != null and afterSaleUnit != ''">
        after_sale_unit = #{afterSaleUnit},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="extType != null">
        ext_type = #{extType},
      </if>
      <if test="createRemark != null">
        create_remark = #{createRemark},
      </if>
      <if test="auditor != null">
        auditor = #{auditor},
      </if>
      <if test="supplierVisible != null">
        supplier_visible = #{supplierVisible},
      </if>
      <if test="isDomestic != null">
        is_domestic = #{isDomestic},
      </if>
      <if test="averagePriceFlag != null">
        average_price_flag = #{averagePriceFlag},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason},
      </if>
      <if test="createType != null">
        create_type = #{createType},
      </if>
      <if test="taskType != null">
        task_type = #{taskType},
      </if>
      <if test="subType!=null">
        sub_type=#{subType},
      </if>
      <if test="netWeightNum != null" >
        net_weight_num = #{netWeightNum,jdbcType=DECIMAL},
      </if>
      <if test="netWeightUnit != null" >
        net_weight_unit = #{netWeightUnit,jdbcType=VARCHAR},
      </if>
      video_url = #{videoUrl,jdbcType=VARCHAR},
      <if test="afterSaleRuleDetail != null" >
        after_sale_rule_detail = #{afterSaleRuleDetail,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null" >
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null" >
        buyer_id = #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="videoUploadTime != null">
        video_upload_time = #{videoUploadTime},
      </if>
      <if test="videoUploadUser != null">
        video_upload_user = #{videoUploadUser},
      </if>
      <if test="quoteType != null">
        quote_type = #{quoteType},
      </if>
      <if test="minAutoAfterSaleThreshold != null">
        min_auto_after_sale_threshold = #{minAutoAfterSaleThreshold},
      </if>
    </set>
    where sku = #{sku,jdbcType=VARCHAR}
  </update>

  <select id="selectByCondition" resultType="net.summerfarm.manage.domain.product.entity.InventoryEntity">
    select
      i.sku as sku,
      p.pd_id as pdId,
      p.pd_name as pdName,
      p.category_id as categoryId,
      tc.category as categoryName,
      sc.category as secondCategoryName,
      i.weight as weight,
      i.unit as unit,
      i.type as type,
      i.sub_type as subType,
      i.outdated as outdated,
      i.add_time as addTime,
      i.creator as creator
    from
      `inventory` i
        INNER JOIN `products` p on i.`pd_id` = p.`pd_id`
        INNER JOIN `category` tc on tc.id = p.category_id
        LEFT JOIN `category` sc on sc.id = tc.`parent_id`
        LEFT JOIN `products_property_value` ppv ON i.`sku` = ppv.`sku`
    <where>
      <if test="sku !=null and sku !=''">
        AND i.sku =#{sku}
      </if>
      <if test="pdId !=null">
        AND i.pd_id =#{pdId}
      </if>
      <if test="secondCategoryId != null">
        AND tc.parent_id = #{secondCategoryId}
      </if>
      <if test="firstCategoryId != null">
        AND sc.parent_id = #{firstCategoryId}
      </if>
      <if test="type != null">
        AND i.type = #{type}
      </if>
      <if test="subType != null">
        AND i.sub_type = #{subType}
      </if>
      <choose>
        <when test="outdated != null and outdated.toString() != ''">
          AND i.outdated= #{outdated}
        </when>
        <otherwise>
          and i.outdated in (-1,0,1)
        </otherwise>
      </choose>
      <if test="categoryId != null">
        AND p.category_id = #{categoryId}
      </if>
      <if test="productsPropertyId != null and productsPropertyValue != null">
        AND ppv.products_property_id = #{productsPropertyId}
        AND ppv.products_property_value = #{productsPropertyValue}
      </if>
    </where>
    GROUP BY i.`sku`
    ORDER BY i.`inv_id` desc
  </select>

  <select id="selectPendingAssociationProductList"
            resultType="net.summerfarm.manage.domain.product.entity.PendingAssociationProductEntity">
    <!-- 特殊业务场景固定sql，条件写死，连接库存&商货表，后续迁移es-->
    select
      i.sku as sku,
      p.pd_id as pdId,
      p.pd_name as pdName,
      i.weight as weight,
      i.weight_num as weightNum,
      i.net_weight_num as netWeightNum,
      i.net_weight_unit as netWeightUnit,
      p.category_id as categoryId,
      -- c.category as categoryName
      p.picture_path as picturePath,
      i.add_time as addTime
    FROM
      `inventory` i
        INNER JOIN `products` p on i.pd_id = p.pd_id
        INNER JOIN `category` c on p.category_id = c.id
        INNER JOIN `area_store` a on i.`sku` = a.`sku`
        LEFT JOIN `cross_biz_goods_mapping` cbgm on i.`sku` = cbgm.`src_sku`
        and cbgm.biz_type = 1 -- 1-鲜沐鲜果同步到POP
    WHERE
      i.`sub_type` = 3 -- 自营经销
      and c.type = 4 -- 鲜果
      and i.`ext_type` = 0 -- 常规
      and i.`outdated` = 0 -- 生效中
      and a.`area_no` = #{warehouseNo,jdbcType=INTEGER}
      and a.`quantity` > 0 -- 实物库存大于0
      and i.`weight` not LIKE '%专用%' -- 规格不包含专用
      and cbgm.id is null -- 未与顺鹿达SKU建立关系
        <if test="categoryId != null">
          and c.id = #{categoryId,jdbcType=BIGINT} -- 根据三级类目筛选
        </if>
    group by
      i.sku
    order by
      i.add_time desc

  </select>

  <select id="listPendingAssociationCategory" resultType="net.summerfarm.manage.domain.product.entity.CategoryEntity">
    select
      c.id as id,
      c.category as category,
      c.parent_id as parentId,
      c.type as type
    FROM
      `inventory` i
        INNER JOIN `products` p on i.pd_id = p.pd_id
        INNER JOIN `category` c on p.category_id = c.id
        INNER JOIN `area_store` a on i.`sku` = a.`sku`
        LEFT JOIN `cross_biz_goods_mapping` cbgm on i.`sku` = cbgm.`src_sku` and cbgm.biz_type = 1 -- 1-鲜沐鲜果同步到POP
    WHERE
      i.`sub_type` = 3 -- 自营经销
      and c.type = 4 -- 鲜果
      and i.`ext_type` = 0 -- 常规
      and i.`outdated` = 0 -- 生效中
      and a.`area_no` =  #{warehouseNo,jdbcType=INTEGER}
      and a.`quantity` > 0 -- 实物库存大于0
      and i.`weight` not LIKE '%专用%' -- 规格不包含专用
      and cbgm.id is null -- 未与顺鹿达SKU建立关系
    group by c.id;
  </select>

  <!-- 批量查询商品基础数据用于AI问题生成 -->
  <select id="batchQueryProductBasicInfoForAI" resultType="net.summerfarm.manage.domain.product.entity.ProductBasicInfoEntity">
    SELECT
      sku.`sku` AS 'sku',
      spu.pd_id AS `pdId`,
      spu.pd_name AS `pdName`,
      -- 规格：SKU属性优先，回退到inventory.weight
      COALESCE(sku_attr.规格, sku.weight) AS `specification`,
      -- 产地：产品属性优先，回退到inventory.origin
      COALESCE(ppv_agg.产地, sku.origin) AS 'origin',
      sku.volume AS 'volume',
      sku.weight_num AS 'weightNum',
      'KG' AS 'weightUnit',
      sku.pack AS 'pack',
      sku.after_sale_unit AS 'afterSaleUnit',

      -- 品牌：标准化品牌表优先，属性表作为优先
      COALESCE(ppv_agg.品牌, brand.name) AS 'brandName',

      -- 类目信息
      c.category AS 'thirdCategoryName',
      c2.category AS 'secondCategoryName',
      c1.category AS 'firstCategoryName',

      -- 商品信息
      spu.pddetail AS 'pdDetail',
      spu.picture_path AS 'picturePath',
      spu.detail_picture AS 'detailPicture',

      -- 保质期信息
      spu.quality_time AS 'qualityTime',
      spu.quality_time_unit AS 'qualityTimeUnit',
      spu.warn_time AS 'warnTime',
      '天' as 'warnTimeUnit',

      -- 售后信息
      spu.refund_type AS 'refundType',
      spu.after_sale_type AS 'afterSaleType',
      CASE
          WHEN spu.storage_location = 0 THEN '未分类'
          WHEN spu.storage_location = 1 THEN '冷冻'
          WHEN spu.storage_location = 2 THEN '冷藏'
          WHEN spu.storage_location = 3 THEN '常温'
          WHEN spu.storage_location = 4 THEN '顶汇大流通'
          ELSE '未知'
      END AS 'storageArea',

      -- 产品级属性（全部保留，明确标注）
      ppv_agg.储藏温度 AS 'productAttrStorageTemp',
      ppv_agg.商品形态 AS 'productAttrGoodsForm',
      ppv_agg.乳脂含量 AS 'productAttrMilkFatContent',
      ppv_agg.使用方法 AS 'productAttrUsageMethod',
      ppv_agg.成分 AS 'productAttrIngredient',
      ppv_agg.面筋含量 AS 'productAttrGlutenContent',
      ppv_agg.口味 AS 'productAttrTaste',
      ppv_agg.是否含糖 AS 'productAttrContainsSugar',
      ppv_agg.湿度 AS 'productAttrHumidity',
      ppv_agg.肉类品种 AS 'productAttrMeatVariety',
      ppv_agg.其他 AS 'productAttrOther',
      ppv_agg.蔬菜品种 AS 'productAttrVegetableVariety',
      ppv_agg.品种 AS 'productAttrVariety',
      ppv_agg.熟度 AS 'productAttrRipeness',
      ppv_agg.每100g乳脂含量 AS 'productAttrMilkFatPer100g',
      ppv_agg.每100g含蛋白质 AS 'productAttrProteinPer100g',

      -- SKU级属性（全部保留，明确标注）
      sku_attr.规格 AS 'skuAttrSpecification',
      sku_attr.级别 AS 'skuAttrLevel',
      sku_attr.果规 AS 'skuAttrFruitSpec',
      sku_attr.自定义属性 AS 'skuAttrCustom',
      sku_attr.口味 AS 'skuAttrTaste',
      sku_attr.原料_成品 AS 'skuAttrRawMaterialFinished',
      sku_attr.尺寸 AS 'skuAttrSize',
      sku_attr.重复利用 AS 'skuAttrReusable',
      sku_attr.自动出库 AS 'skuAttrAutoOutbound'
    FROM `inventory` sku
    INNER JOIN `products` spu ON spu.pd_id = sku.pd_id
    -- 产品级属性子查询
    LEFT JOIN (
      SELECT
        ppv.pd_id,
        MAX(CASE WHEN pp.name = '产地' THEN ppv.products_property_value END) AS '产地',
        MAX(CASE WHEN pp.name = '储藏温度' THEN ppv.products_property_value END) AS '储藏温度',
        MAX(CASE WHEN pp.name = '品牌' THEN ppv.products_property_value END) AS '品牌',
        MAX(CASE WHEN pp.name = '商品形态' THEN ppv.products_property_value END) AS '商品形态',
        MAX(CASE WHEN pp.name = '乳脂含量' THEN ppv.products_property_value END) AS '乳脂含量',
        MAX(CASE WHEN pp.name = '使用方法' THEN ppv.products_property_value END) AS '使用方法',
        MAX(CASE WHEN pp.name = '成分' THEN ppv.products_property_value END) AS '成分',
        MAX(CASE WHEN pp.name = '面筋含量' THEN ppv.products_property_value END) AS '面筋含量',
        MAX(CASE WHEN pp.name = '口味' THEN ppv.products_property_value END) AS '口味',
        MAX(CASE WHEN pp.name = '是否含糖' THEN ppv.products_property_value END) AS '是否含糖',
        MAX(CASE WHEN pp.name = '湿度' THEN ppv.products_property_value END) AS '湿度',
        MAX(CASE WHEN pp.name = '肉类品种' THEN ppv.products_property_value END) AS '肉类品种',
        MAX(CASE WHEN pp.name = '其他' THEN ppv.products_property_value END) AS '其他',
        MAX(CASE WHEN pp.name = '蔬菜品种' THEN ppv.products_property_value END) AS '蔬菜品种',
        MAX(CASE WHEN pp.name = '品种' THEN ppv.products_property_value END) AS '品种',
        MAX(CASE WHEN pp.name = '熟度' THEN ppv.products_property_value END) AS '熟度',
        MAX(CASE WHEN pp.name = '每100g乳脂含量' THEN ppv.products_property_value END) AS '每100g乳脂含量',
        MAX(CASE WHEN pp.name = '每100g含蛋白质' THEN ppv.products_property_value END) AS '每100g含蛋白质'
      FROM products_property_value ppv
      INNER JOIN products_property pp
        ON ppv.products_property_id = pp.id
        AND pp.status = 1
      WHERE ppv.sku IS NULL
      GROUP BY ppv.pd_id
    ) ppv_agg ON ppv_agg.pd_id = spu.pd_id
    -- SKU级属性子查询
    LEFT JOIN (
      SELECT
        ppv.sku,
        MAX(CASE WHEN pp.name = '规格' THEN ppv.products_property_value END) AS 规格,
        MAX(CASE WHEN pp.name = '级别' THEN ppv.products_property_value END) AS 级别,
        MAX(CASE WHEN pp.name = '果规' THEN ppv.products_property_value END) AS 果规,
        MAX(CASE WHEN pp.name = '自定义属性' THEN ppv.products_property_value END) AS 自定义属性,
        MAX(CASE WHEN pp.name = '口味' THEN ppv.products_property_value END) AS 口味,
        MAX(CASE WHEN pp.name = '原料/成品' THEN ppv.products_property_value END) AS 原料_成品,
        MAX(CASE WHEN pp.name = '尺寸' THEN ppv.products_property_value END) AS 尺寸,
        MAX(CASE WHEN pp.name = '重复利用' THEN ppv.products_property_value END) AS 重复利用,
        MAX(CASE WHEN pp.name = '自动出库' THEN ppv.products_property_value END) AS 自动出库
      FROM products_property_value ppv
      INNER JOIN products_property pp
        ON ppv.products_property_id = pp.id
        AND pp.status = 1
      WHERE ppv.sku IS NOT NULL
      GROUP BY ppv.sku
    ) sku_attr ON sku_attr.sku = sku.sku
    -- 类目和品牌表
    INNER JOIN `category` c ON c.id = spu.category_id
    LEFT JOIN `category` c2 ON c2.`id` = c.`parent_id`
    LEFT JOIN `category` c1 ON c1.`id` = c2.`parent_id`
    LEFT JOIN brand brand ON brand.`brand_id` = spu.`brand_id`
    inner join (
     select DISTINCT store.sku  from `area_store` store
     inner join `warehouse_storage_center` wsc on wsc.`warehouse_no` = `store`.`area_no` and wsc.`status` = 1
     where store.`quantity` >0 or store.`online_quantity` >0
   ) store on store.sku = sku.sku
    WHERE sku.`outdated` = 0
    and sku.`tenant_id` = 1
    and spu.`pd_name` not like '%测试%'
    and spu.`pd_name` not like '%压测%'
    and sku.sku in
    <foreach collection="skus" item="sku" open="(" close=")" separator=",">
      #{sku}
    </foreach>
  </select>

  <!-- 查询需要生成AI问题的SKU列表 -->
  <select id="querySkusNeedAiQuestions" resultType="java.lang.String">
    select sku.sku
    from inventory sku
    inner join products spu on spu.pd_id = sku.pd_id
        and spu.`pd_name` not like '%测试%'
        and spu.`pd_name` not like '%压测%'
    left join market_item_ai_ext miae on sku.sku = miae.sku and miae.ext_type = 1
    where sku.`tenant_id` = 1
    and sku.`outdated` = 0
    and sku.sku in (
        select distinct sku from area_sku where on_sale = 1
    )
    and miae.sku is null
  </select>

</mapper>