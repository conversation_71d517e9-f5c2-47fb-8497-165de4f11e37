package net.summerfarm.manage.application.test.service;

import com.github.pagehelper.PageInfo;
import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.area.input.LargeAreaQueryInput;
import net.summerfarm.manage.application.inbound.controller.area.vo.LargeAreaWithSubAreaVO;
import net.summerfarm.manage.application.inbound.controller.contact.input.ContactInsertInput;
import net.summerfarm.manage.application.inbound.controller.major.input.command.MajorPriceExcelDataInput;
import net.summerfarm.manage.application.service.area.AreaQueryService;
import net.summerfarm.manage.application.service.contact.ContactService;
import net.summerfarm.manage.application.service.download.majorprice.MajorPriceImportHandler;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.starter.Application;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@ComponentScan(value = {"net.summerfarm.manage.*", "net.xianmu.authentication"})
@MapperScan(value = {"net.summerfarm.manage.infrastructure.mapper.**"}, annotationClass = Mapper.class)
@Slf4j
public class AreaQueryServiceImplTest {

    @Resource
    private AreaQueryService queryService;
    @Resource
    private ContactService contactService;

    @Autowired
    private AreaSkuCommandService areaSkuCommandService;
    @Autowired
    private MajorPriceImportHandler ajorPriceImportHandler;

    @Test
    public void testQueryLargeArea() {
        Set<Integer> areaNos = new HashSet<>(Arrays.asList(
                29318, 29319, 29320, 29321, 29326, 29458, 29459, 2836,
                29332, 29460, 29333, 29334, 1820, 29346, 29347, 29351,
                29352, 29354, 29355, 29359, 29361, 29362, 29493, 29496,
                29497, 29370, 2750, 29248, 29251, 1605, 29382, 29384,
                29385, 29386, 29259, 3027, 29267, 29401, 29405, 29408,
                1381, 1001, 1002, 1264, 29298, 29299, 29300, 10358,
                29308, 29310, 29438
        ));
        areaSkuCommandService.updateAreaSkuOnSaleBatch("1052644630358",areaNos, false);
    }


    @Test
    public void addContactWithoutAudit() {
        MajorPriceExcelDataInput majorPriceExcelDataInput = new MajorPriceExcelDataInput ();
        majorPriceExcelDataInput.setAdminId(20578);
        majorPriceExcelDataInput.setDirectString("现结");
        majorPriceExcelDataInput.setSku("2154281342856");
        majorPriceExcelDataInput.setStatus("保存");
        majorPriceExcelDataInput.setAreaName("杭州");
        majorPriceExcelDataInput.setValidTime("2025-06-09 10:10:11");
        majorPriceExcelDataInput.setInvalidTime("2026-06-09 10:10:11");
        majorPriceExcelDataInput.setMallShowString("是");
        majorPriceExcelDataInput.setPriceTypeString("商城价上浮");
        majorPriceExcelDataInput.setAmount(new BigDecimal ("66"));
//        majorPriceExcelDataInput.setFixedPrice();
        majorPriceExcelDataInput.setInterestRate(new BigDecimal ("66"));
//        majorPriceExcelDataInput.setPayMethod();
//        majorPriceExcelDataInput.setErrorMsg();


        MajorPriceExcelDataInput majorPriceExcelDataInput1 = new MajorPriceExcelDataInput ();
        majorPriceExcelDataInput1.setAdminId(20578);
        majorPriceExcelDataInput1.setDirectString("现结");
        majorPriceExcelDataInput1.setSku("2154281342856");
        majorPriceExcelDataInput1.setStatus("保存");
        majorPriceExcelDataInput1.setAreaName("上海");
        majorPriceExcelDataInput1.setValidTime("2025-06-09 10:10:11");
        majorPriceExcelDataInput1.setInvalidTime("2026-06-09 10:10:11");
        majorPriceExcelDataInput1.setMallShowString("是");
        majorPriceExcelDataInput1.setPriceTypeString("商城价上浮");
        majorPriceExcelDataInput1.setAmount(new BigDecimal ("55"));
//        majorPriceExcelDataInp1ut.setFixedPrice();
        majorPriceExcelDataInput1.setInterestRate(new BigDecimal ("55"));
//        majorPriceExcelDataInput.setPayMethod();
//        majorPriceExcelDataInput.setErrorMsg();


        List<MajorPriceExcelDataInput> list = new ArrayList<> ();
        list.add (majorPriceExcelDataInput);
        list.add (majorPriceExcelDataInput1);
        ajorPriceImportHandler.dealExcelData (list,null);
    }
}
