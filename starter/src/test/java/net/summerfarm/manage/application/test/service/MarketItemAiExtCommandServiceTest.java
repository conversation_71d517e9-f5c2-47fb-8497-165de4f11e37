package net.summerfarm.manage.application.test.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.marketItem.MarketItemAiExtCommandService;
import net.summerfarm.manage.application.service.marketItem.dto.BatchInitResult;
import net.summerfarm.manage.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class MarketItemAiExtCommandServiceTest {

    @Resource
    private MarketItemAiExtCommandService marketItemAiExtCommandService;

    @Test
    public void testbatchInitMarketItemQuestions() {
        BatchInitResult result = marketItemAiExtCommandService.batchInitMarketItemQuestions(
                Arrays.asList("4831436217"));
        log.info("result:{}", JSON.toJSONString(result));
    }


    @Test
    public void testtestProductInfoText() {
        String  result = marketItemAiExtCommandService.testProductInfoText(
                "18650638620");
        log.info("result:{}", JSON.toJSONString(result));
    }
}
