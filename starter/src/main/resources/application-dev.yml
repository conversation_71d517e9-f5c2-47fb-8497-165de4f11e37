#set( $symbol_pound = '#' )
#set( $symbol_dollar = '$' )
#set( $symbol_escape = '\' )

server:
  port: 80
  servlet:
    context-path: /sf-mall-manage
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: *****************************************************************************************************************&
  username: dev
spring:
  application:
    name: sf-mall-manage
  schedulerx2:
    appKey: ib8zGKARNRaJiaiawzplwEzrA
    endpoint: acm.aliyun.com
    groupId: sf-mall-manage
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: *********************************************************************************************************************************************************&
    username: dev
    password: xianmu619
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 20 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
xm:
  oss:
    # 生产测试不一样
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true

rocketmq:
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: Rocketmq
    group: GID_mall_manage
    secret-key: Rocketmq
    sendMsgTimeout: 10000
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

mybatis:
  mapper-locations: classpath*:mapper/**/*.xml
# 配置中心
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954
xianmu:
  mall:
    domain: https://devh5.summerfarm.net
wechat:
  app:
    id: wx86d6db2ec4b9e9cc
    secret: 070812b6ce547bacaa402d91245ddc80
  mp-app:
    id: wx0234b1d4eb212e12
    secret: 7779dbf6349ca85212b05435dfc38716
