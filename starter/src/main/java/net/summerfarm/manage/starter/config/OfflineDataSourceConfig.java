package net.summerfarm.manage.starter.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageHelper;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.io.IOException;

/**
 * 离线数据库配置
 */
@Configuration
@MapperScan(basePackages = {"net.summerfarm.manage.infrastructure.offlinemapper"}, sqlSessionFactoryRef = "offlineSqlSessionFactory")
public class OfflineDataSourceConfig {

    @Value(value = "${mysql.dbType}")
    private String dbType;
    @Value(value = "${mysql.driverClassName:}")
    private String driverClassName;
    @Value(value = "${mysql.offline.url:}")
    private String url;
    @Value(value = "${mysql.offline.username:}")
    private String username;
    @Value(value = "${mysql.offline.password:}")
    private String password;
    @Value(value = "${mysql.minIdle:}")
    private int minIdle;
    @Value(value = "${mysql.initialSize:}")
    private int initialSize;
    @Value(value = "${mysql.maxActive:}")
    private int maxActive;
    @Value(value = "${mysql.maxWait:}")
    private int maxWait;
    @Value(value = "${mysql.asyncInit:}")
    private boolean asyncInit;
    @Value(value = "${mysql.testWhileIdle:}")
    private boolean testWhileIdle;


    @ConfigurationProperties(prefix = "spring.datasource.read.only")
    @Bean("offlineDataSource")
    public DruidDataSource druidDataSource() {
        System.out.println("生成离线库数据源");
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setAsyncInit(asyncInit);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        return druidDataSource;
    }


    @Bean("offlineSqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("offlineDataSource") DruidDataSource druidDataSource) throws IOException {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath*:offlinemapper/*.xml");
        sqlSessionFactoryBean.setDataSource(druidDataSource);
        sqlSessionFactoryBean.setMapperLocations(resources);
        return sqlSessionFactoryBean;
    }

}
