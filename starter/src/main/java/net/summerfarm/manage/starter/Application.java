package net.summerfarm.manage.starter;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.dts.*;
import net.summerfarm.manage.common.constants.DBTableName;
import net.summerfarm.manage.starter.config.MainConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@SpringBootApplication(scanBasePackages = {"net.summerfarm", "net.xianmu.jobsdk"})
@Import({MainConfiguration.class})
public class Application implements WebMvcConfigurer {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("#############服务启动成功##############");
    }


    // 引入Fastjson解析json，不使用默认的jackson
    // 必须在pom.xml引入fastjson的jar包，并且版必须大于1.2.10
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        //1、定义一个convert转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        //2、添加fastjson的配置信息F
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        //3、在convert中添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        fastConverter.setSupportedMediaTypes(supportedMediaTypes);
        fastConverter.setFeatures(SerializerFeature.DisableCircularReferenceDetect);
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        //4、将convert添加到converters中
        converters.add(stringHttpMessageConverter);
        converters.add(fastConverter);

    }

    @Bean("dbTableDmlFactory")
    public DbTableDmlFactory dbTableDmlFactory(AfterSaleOrderDmlImpl afterSaleOrderDml, OrdersDmlImpl ordersDml) {
        DbTableDmlFactory dbTableDmlFactory = new DbTableDmlFactory();
        Map<String, DbTableDml> dbTableDmlHashMap = new ConcurrentHashMap<>(2);
        dbTableDmlHashMap.put(DBTableName.AFTER_SALE_ORDER, afterSaleOrderDml);
        dbTableDmlHashMap.put(DBTableName.ORDERS, ordersDml);
        dbTableDmlFactory.setDbTableDmlMap(dbTableDmlHashMap);
        return dbTableDmlFactory;
    }

    @Bean("dbTableOrderlyDmlFactory")
    public DbTableOrderlyDmlFactory dbTableOrderlyDmlFactory(DeliveryPlanDmlImpl deliveryPlanDml) {
        DbTableOrderlyDmlFactory dbTableDmlFactory = new DbTableOrderlyDmlFactory();
        Map<String, DbTableDml> dbTableDmlHashMap = new ConcurrentHashMap<>(2);
        dbTableDmlHashMap.put(DBTableName.DELIVERY_PLAN, deliveryPlanDml);
        dbTableDmlFactory.setDbTableDmlMap(dbTableDmlHashMap);
        return dbTableDmlFactory;
    }
}
