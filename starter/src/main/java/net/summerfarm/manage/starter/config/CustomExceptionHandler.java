package net.summerfarm.manage.starter.config;

import com.alibaba.fastjson.JSON;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.CallerException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import org.apache.shiro.authz.UnauthorizedException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * 内部处理了controller层级dubbo的异常
 * 1.controller使用了2种方式,1.注解ControllerAdvice,2,实现HandlerExceptionResolver,注解方式会被优先匹配处理,没被注解匹配到的会走HandlerExceptionResolver
 * 2.dubbo处理,基于实现net.summerfarm.dubbo.support.handle.ExceptionHandler 的接口方法
 */
@Component
@RestControllerAdvice
public class CustomExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomExceptionHandler.class);


    /**
     * 异常书写顺序
     * 1.在用异常在前面
     * 2.调用方异常在前面
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public CommonResult providerException(Exception e) {
        CommonResult result = null;
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            String message = ex.getBindingResult().getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElse("参数异常");
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, message);
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
        } else if (e instanceof ParamsException) {
            ParamsException ex = (ParamsException) e;
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方参数异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof CallerException) {
            CallerException ex = (CallerException) e;
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof BizException) {
            BizException ex = (BizException) e;
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.warn("调用方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof ProviderException) {
            ProviderException ex = (ProviderException) e;
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ex.getMessage(), ex.getErrorCode().getCode());
            logger.error("提供方异常, 异常信息:{}", ex.getMessage(), ex);
        } else if (e instanceof DuplicateKeyException) {
            String message = e.getCause().getMessage();
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, message);
            logger.error("提供方异常,异常信息:{}", e.getMessage(), e);
        } else if (e instanceof DataIntegrityViolationException){
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
            logger.error("提供方异常,异常信息:{}", e.getMessage(), e);
        } else if(e instanceof UnauthorizedException){
            result = CommonResult.fail(ResultStatusEnum.FORBIDDEN, "权限不足请联系管理员");
            logger.error("提供方异常,异常信息:{}", e.getMessage(), e);
        } else {
            result = CommonResult.fail(ResultStatusEnum.SERVER_ERROR);
            logger.error("提供方未知异常,异常信息:{}", e.getMessage(), e);
        }

        logger.info("接口响应参数：responseBody = [{}]", JSON.toJSONString(result));

        return result;
    }

    @Override
    public DubboResponse processError(Throwable e, ProceedingJoinPoint joinPoint) {
        if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException)e;
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException)e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException)e;
            logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else {
            logger.error("提供方未知异常, 异常信息:{}", e.getMessage(), e);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return DubboResponse.getError(providerErrorCode.getCode(), e.getMessage());
        }
    }

}
