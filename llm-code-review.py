import subprocess
import requests
import os

LLM_API_KEY = os.getenv("XM_LLM_API_KEY", "sk-4wXuSpwi8cOdruDM603b2aC1Ef864709A5E4550bB17f16A9")
LLM_BASE_URL = os.getenv("XM_LLM_BASE_URL", "https://test-one-api.summerfarm.top/v1")
LLM_MODEL = os.getenv("XM_LLM_MODEL", "deepseek-v3-250324")

def get_changed_files():
    # Fetch the latest changes from the remote repository
    subprocess.run(["git", "fetch", "origin"])

    # Get the list of changed files
    changed_files = subprocess.check_output(
        ["git", "diff", "--name-only", "origin/master...HEAD"]
    )

    print(f"changed_files:{changed_files}")

    # Decode the bytes to a string and split into lines
    changed_files_str = changed_files.decode().splitlines()

    # Filter the list of changed files
    inclusions = [
        file
        for file in changed_files_str
        if file.endswith(".java")
        and not file.startswith("src/test")
        and not file.endswith("Mapper.java")
    ]

    # Sort the list and remove the directory path
    inclusions = sorted(inclusions, key=lambda x: x.split("/")[-1])

    return inclusions


def get_git_diff_output_of_file(file):
    #   $(git diff -U0 origin/master...HEAD "$file" | jq -R -s '.')
    git_diff = subprocess.check_output(
        ["git", "diff", "-U0", "origin/master...HEAD", file]
    )
    git_diff = git_diff.decode()
    return git_diff


def get_file_content(file):
    # Get the full content of the file in the current branch
    try:
        with open(file, 'r') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading file {file}: {e}")
        return ""

system_instruction="""
你是一位经验丰富的资深代码评审专家。严格分析提供的 Git diff 输出和完整文件内容，只识别并报告最关键的问题。你的评审应重点关注：

1.  **严重 Bug：** 直接通过更改可识别的逻辑错误、崩溃或数据损坏风险。
2.  **重大安全漏洞：** 在代码中可见的常见弱点（例如，注入缺陷、访问控制失效、敏感数据暴露）。
3.  **主要性能瓶颈：** 引入显著低效的代码更改（例如，N+1 查询、处理大型数据集的低效循环），影响资源使用或延迟。
4.  **关键架构/设计缺陷：** 根据提供的上下文，引入显著耦合、违反核心设计原则或严重阻碍未来维护/扩展性的更改。

对于找到的每个关键问题：
*   **问题与影响：** 清晰地说明问题及其对系统的潜在后果。
*   **解决方案：** 提供简洁、可操作的修复或推荐方法。
*   **代码片段（如果需要）：** 使用指定格式（语言、文件路径、函数/类上下文、最小 diff 样式）说明建议的更改。

**重要指示：**
*   【一定要遵守】请使用中文回答。
*   分析提供的 Git diff 和完整文件内容。从代码中推断编程语言。
*   你的分析基于 diff 中的更改以及完整文件内容提供的上下文。
*   **严格忽略：** 次要代码风格问题、小错字、注释、文档、次要优化以及非关键建议。
*   按严重程度优先报告问题。
*   直接、专业、简洁。
*   如果根据这些标准**未**找到任何关键问题，则**只**回复："恭喜🎉！未发现任何严重问题。"
"""

# ... rest of code here ...
import json # 导入 json 模块用于解析流数据

def post_git_diff_to_llm(git_diff: str, file_content: str, file_path: str) -> str:
    if len(git_diff) <= 10:
        return f"git diff content too less:{git_diff}"

    url = f"{LLM_BASE_URL}/chat/completions"
    payload = {
        "model": LLM_MODEL,
        "max_tokens": 16384,
        "top_p": 1,
        "top_k": 40,
        "presence_penalty": 0,
        "frequency_penalty": 0,
        "temperature": 0.9,
        "stream": True,
        "messages": [
            {
                "role": "system",
                "content": system_instruction,
            },
            {
                "role": "user",
                "content": f"以下是文件 {file_path} 的完整内容：\n\n{file_content}\n\n以下是这次修改的diff内容：\n\n{git_diff}",
            },
        ],
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {LLM_API_KEY}",
    }

    # 发送请求，启用流式处理
    response = requests.post(url, headers=headers, json=payload, stream=True)
    response.raise_for_status() # 如果请求失败 (状态码 4xx or 5xx), 则抛出 HTTPError

    full_content = []
    try:
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                # SSE 事件通常以 "data: " 开头
                if decoded_line.startswith('data: '):
                    json_str = decoded_line[len('data: '):]
                    if json_str.strip() == '[DONE]': # 流结束标记
                        break
                    try:
                        chunk = json.loads(json_str)
                        if chunk.get("choices"):
                            delta = chunk["choices"][0].get("delta", {})
                            content_piece = delta.get("content")
                            if content_piece:
                                print(content_piece, end='', flush=True)
                                full_content.append(content_piece)
                    except json.JSONDecodeError:
                        print(f"无法解析JSON: {json_str}") # 记录解析错误
                        # 根据需要可以决定是否继续或中断
                        continue
    except requests.exceptions.RequestException as e:
        print(f"请求LLM API时发生错误: {e}")
        return "error:request failed"
    finally:
        response.close() # 确保关闭响应流

    if not full_content:
        return "error:empty response from stream"

    return "".join(full_content)

overroll_markdown = ""

changed_files = get_changed_files()
if len(changed_files) == 0:
    print("no changed files")
    exit(0)

for file in changed_files:
    # Get the base name
    base_name = os.path.basename(file)
    overroll_markdown = f"{overroll_markdown}## CR结果:{base_name}\n"
    git_diff = get_git_diff_output_of_file(file)
    file_content = get_file_content(file)
    print(f"开始AI评审文件:{file}\n")
    markdown_of_code_review = post_git_diff_to_llm(git_diff, file_content, file)
    overroll_markdown = f"{overroll_markdown}{markdown_of_code_review}"


# Save the overroll_markdown content to a local file
file_name = "code_review_result.md"
with open(file_name, "w") as file:
    file.write(overroll_markdown)
    print(f"\n\nfile saved into:{file_name}")
