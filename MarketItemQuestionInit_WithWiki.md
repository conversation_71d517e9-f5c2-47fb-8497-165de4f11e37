# 商品相关问题初始化 - 集成Wiki知识库

## 功能概述

商品相关问题初始化功能现已集成Wiki知识库搜索，能够基于商品信息和相关专业知识生成更准确、更专业的客户问题。

## 新增功能

### 1. Wiki知识库集成
- 根据商品信息自动搜索相关专业知识
- 将知识库内容与商品信息结合，提供给AI生成问题
- 支持多种搜索策略和结果过滤

### 2. 智能搜索策略
- **商品名称**：主要搜索关键词
- **品牌信息**：增强搜索精度
- **类目信息**：扩展相关知识范围
- **成分信息**：获取专业知识

### 3. 上下文构建
- 商品基础信息
- 相关专业知识（最多3条）
- 结构化的上下文格式

## 工作流程

```mermaid
graph TD
    A[商品基础信息] --> B[构建搜索关键词]
    B --> C[搜索Wiki知识库]
    C --> D[获取相关专业知识]
    D --> E[构建完整上下文]
    E --> F[调用AI生成问题]
    F --> G[保存生成的问题]
```

## 搜索关键词构建

系统会根据以下优先级构建搜索关键词：

1. **商品名称**（必选）
2. **品牌名称**（如果有）
3. **三级类目** > 二级类目（如果有）
4. **成分信息**（如果有）

### 示例
```
商品：安佳淡奶油
搜索关键词：安佳淡奶油 安佳 乳制品 生牛乳
```

## 上下文格式

生成的完整上下文格式如下：

```
=== 商品信息 ===
商品名称：安佳淡奶油
规格：1L装
产地：新西兰
品牌：安佳
类目：饮料 > 乳制品 > 奶油
成分：生牛乳
储藏温度：2-8℃
...

=== 相关专业知识 ===
相关知识1：淡奶油是从牛奶中提取的乳脂，脂肪含量通常在35%以上，适用于制作甜点、咖啡调味等...
相关知识2：安佳品牌来自新西兰，以优质的乳制品著称，其淡奶油具有良好的打发性能...
相关知识3：淡奶油开封后需要冷藏保存，建议在3-5天内使用完毕，避免变质...
```

## 配置参数

在 `application.yml` 中添加Wiki配置：

```yaml
wiki:
  auth:
    api:
      key: "your-api-key"
      url:
        prefix: "https://fastgpt-test.summerfarm.net/"
  catering:
    dataset:
      id: "684aa95eaba1d13d05b4fc61"
```

## 使用方式

### 1. 自动集成
现有的商品问题初始化功能会自动使用Wiki知识库：

```bash
# Job调度（自动查询）
参数: {}

# Job调度（指定SKU）
参数: {"skus": ["SKU001", "SKU002"]}
```

### 2. HTTP接口
```http
POST /marketItemAiExt/init/questions
["SKU001", "SKU002"]
```

### 3. 测试Wiki搜索
```http
GET /wiki/search?text=安佳淡奶油使用方法
```

## 生成问题质量提升

集成Wiki知识库后，生成的问题更加专业和准确：

### 优化前
```json
[
  "这个商品的保质期是多久？",
  "这个商品怎么保存？",
  "这个商品有什么特点？"
]
```

### 优化后
```json
[
  "安佳淡奶油的脂肪含量是多少？适合制作哪些甜点？",
  "淡奶油开封后如何正确保存？可以保存多长时间？",
  "安佳淡奶油的打发性能如何？与其他品牌有什么区别？",
  "使用淡奶油制作咖啡时有什么技巧？",
  "淡奶油可以直接饮用吗？有什么营养价值？"
]
```

## 性能优化

1. **搜索限制**：每次最多搜索2000字符的知识内容
2. **结果过滤**：最多使用3条最相关的知识
3. **异常处理**：搜索失败时不影响基础功能
4. **缓存机制**：可考虑对常用商品的知识进行缓存

## 监控指标

- Wiki搜索成功率
- 知识库命中率
- 生成问题的质量评分
- 处理耗时变化

## 错误处理

1. **Wiki服务不可用**：降级为仅使用商品信息
2. **搜索无结果**：使用基础商品信息生成问题
3. **知识解析失败**：记录错误，继续处理
4. **网络超时**：设置合理的超时时间

## 注意事项

1. **API密钥安全**：确保Wiki API密钥的安全性
2. **网络依赖**：增加了对外部服务的依赖
3. **数据质量**：知识库内容的质量直接影响生成效果
4. **成本控制**：注意Wiki API的调用频率和成本

## 后续优化方向

1. **智能缓存**：对热门商品的知识进行缓存
2. **多数据源**：集成更多专业知识库
3. **知识评分**：根据相关性对知识进行评分排序
4. **个性化**：根据用户偏好调整知识搜索策略
