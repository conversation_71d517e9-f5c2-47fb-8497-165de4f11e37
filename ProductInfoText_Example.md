# 商品信息文本组装示例

## 字段处理逻辑

现在`buildProductInfoText`方法已经优化，只有非空字段才会被包含在商品信息文本中：

1. **商品名称**：如果不为空则显示（通常是必填）
2. **所有其他字段**：只有当字段值不为空时才显示
3. **类目组装**：按照"一级 > 二级 > 三级"的格式组装
4. **属性区分**：明确区分产品级属性和SKU级属性
5. **空值过滤**：null、空字符串、纯空格的字段都不会显示

## 组装示例

### 完整信息的商品
```
商品名称：有机牛奶
规格：1L装
产地：新西兰
品牌：安佳
类目：饮料 > 乳制品 > 牛奶
商品描述：优质有机牛奶，营养丰富
体积：10*5*20cm
重量：1.05
重量单位：KG
包装单位：瓶
仓储区域：冷藏
保质期时长：30
保质期单位：天
储藏温度：2-8℃
商品形态：液体
乳脂含量：3.5%
成分：生牛乳
每100g乳脂含量：3.5g
每100g含蛋白质：3.2g
SKU规格：1L
级别：A级
```

### 信息较少的商品
```
商品名称：苹果
产地：山东
类目：生鲜 > 水果 > 苹果
包装单位：斤
仓储区域：常温
品种：红富士
级别：一级
```

## 优势

1. **信息精简**：只传递有价值的信息给AI
2. **避免干扰**：不会有"null"或空字段影响AI理解
3. **提高质量**：AI基于真实有效的信息生成问题
4. **节省token**：减少无用信息，降低AI调用成本

## 测试方法

可以通过以下接口测试商品信息组装：

```http
GET /marketItemAiExt/test/product-info/{sku}
```

这样可以查看具体SKU的商品信息组装结果，验证字段是否正确过滤。
